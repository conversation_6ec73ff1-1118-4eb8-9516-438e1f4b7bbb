package com.stt.android.exceptions

import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream

class AggregateExceptionTest {

    @Test
    fun `aggregate exception message`() {
        val e = AggregateException(
            RuntimeException("Message1"),
            NullPointerException("Message2")
        )
        val message = e.message
        println(message)
        assertThat(message).contains("RuntimeException")
        assertThat(message).contains("NullPointerException")
        assertThat(message).contains("Message1")
        assertThat(message).contains("Message2")
        e.printStackTrace()
    }

    @Test
    fun `serialization doesn't throw and is consistent`() {
        val e1 = AggregateException(
            RuntimeException("Message1"),
            NullPointerException("Message2")
        )
        val serialized1 = serialize(e1)
        val deserialized1 = deserialize(serialized1!!) as AggregateException
        val message = deserialized1.message

        assertThat(message).contains("RuntimeException")
        assertThat(message).contains("NullPointerException")
        assertThat(message).contains("Message1")
        assertThat(message).contains("Message2")
    }

    private fun serialize(obj: Any): ByteArray? {
        val b = ByteArrayOutputStream()
        val o = ObjectOutputStream(b)
        o.writeObject(obj)
        return b.toByteArray()
    }

    private fun deserialize(bytes: ByteArray): Any? {
        val b = ByteArrayInputStream(bytes)
        val o = ObjectInputStream(b)
        return o.readObject()
    }
}
