package com.stt.android.utils

import android.content.Context
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.os.Build
import androidx.core.content.ContextCompat

fun Context.isNearbyDevicesPermissionGranted(): <PERSON><PERSON>an {
    var scanPermission: Int = PERMISSION_GRANTED
    var connectPermission: Int = PERMISSION_GRANTED

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S || "S" == Build.VERSION.CODENAME) {
        // For now, BT is considered "disabled" if these permissions are not granted on Android
        // 12 and up.
        scanPermission = ContextCompat.checkSelfPermission(this, "android.permission.BLUETOOTH_SCAN")
        connectPermission = ContextCompat.checkSelfPermission(this, "android.permission.BLUETOOTH_CONNECT")
    }

    return scanPermission == PERMISSION_GRANTED && connectPermission == PERMISSION_GRANTED
}
