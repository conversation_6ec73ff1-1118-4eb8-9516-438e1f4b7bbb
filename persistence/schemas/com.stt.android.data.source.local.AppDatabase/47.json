{"formatVersion": 1, "database": {"version": 47, "identityHash": "700c3ed65d27a43946c587b51dca6174", "entities": [{"tableName": "sleep", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `sleep_seconds` REAL NOT NULL, `deep_sleep` INTEGER, `awake` INTEGER, `feeling` INTEGER, `avg_hr` REAL, `min_hr` REAL, `quality` INTEGER, `fell_asleep` INTEGER, `woke_up` INTEGER, `segments` TEXT, PRIMARY KEY(`serial`, `timestamp`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleepSeconds", "columnName": "sleep_seconds", "affinity": "REAL", "notNull": true}, {"fieldPath": "deepSleep", "columnName": "deep_sleep", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "awake", "columnName": "awake", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "feeling", "columnName": "feeling", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "avgHr", "columnName": "avg_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "minHr", "columnName": "min_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "quality", "columnName": "quality", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fell<PERSON>leep", "columnName": "fell_asleep", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "wokeUp", "columnName": "woke_up", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "segments", "columnName": "segments", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["serial", "timestamp"]}, "indices": [], "foreignKeys": []}, {"tableName": "sleepsegments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `quality` REAL, `avg_hr` REAL, `min_hr` REAL, `feeling` INTEGER, `duration_seconds` REAL NOT NULL, `deep_sleep_duration_seconds` REAL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, `bedtime_start` INTEGER, `bedtime_end` INTEGER, `rem_sleep_duration_seconds` REAL, `light_sleep_duration_seconds` REAL, `body_resources_insight_id` INTEGER, `sleep_id` INTEGER, `max_spo2` REAL, `altitude` REAL, PRIMARY KEY(`timestamp_seconds`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestampSeconds", "columnName": "timestamp_seconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "quality", "columnName": "quality", "affinity": "REAL", "notNull": false}, {"fieldPath": "avgHr", "columnName": "avg_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "minHr", "columnName": "min_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "feeling", "columnName": "feeling", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "durationSeconds", "columnName": "duration_seconds", "affinity": "REAL", "notNull": true}, {"fieldPath": "deepSleepDurationSeconds", "columnName": "deep_sleep_duration_seconds", "affinity": "REAL", "notNull": false}, {"fieldPath": "syncedStatus", "columnName": "synced_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bedtimeStart", "columnName": "bedtime_start", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bedtimeEnd", "columnName": "bedtime_end", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "remSleepDurationSeconds", "columnName": "rem_sleep_duration_seconds", "affinity": "REAL", "notNull": false}, {"fieldPath": "lightSleepDurationSeconds", "columnName": "light_sleep_duration_seconds", "affinity": "REAL", "notNull": false}, {"fieldPath": "bodyResourcesInsightId", "columnName": "body_resources_insight_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sleepId", "columnName": "sleep_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "maxSpO2", "columnName": "max_spo2", "affinity": "REAL", "notNull": false}, {"fieldPath": "altitude", "columnName": "altitude", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["timestamp_seconds"]}, "indices": [], "foreignKeys": []}, {"tableName": "trenddata_v2", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `energy` REAL NOT NULL, `steps` INTEGER NOT NULL, `heartrate` REAL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, `hrMin` REAL, `hrMax` REAL, `spo2` REAL, `altitude` REAL, PRIMARY KEY(`timestamp_seconds`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestampSeconds", "columnName": "timestamp_seconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "energy", "columnName": "energy", "affinity": "REAL", "notNull": true}, {"fieldPath": "steps", "columnName": "steps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "heartrate", "columnName": "heartrate", "affinity": "REAL", "notNull": false}, {"fieldPath": "syncedStatus", "columnName": "synced_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hrMin", "columnName": "hrMin", "affinity": "REAL", "notNull": false}, {"fieldPath": "hrMax", "columnName": "hrMax", "affinity": "REAL", "notNull": false}, {"fieldPath": "spo2", "columnName": "spo2", "affinity": "REAL", "notNull": false}, {"fieldPath": "altitude", "columnName": "altitude", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["timestamp_seconds"]}, "indices": [], "foreignKeys": []}, {"tableName": "recoverydata", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `balance` REAL NOT NULL, `stress_state` INTEGER NOT NULL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, PRIMARY KEY(`timestamp_seconds`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestampSeconds", "columnName": "timestamp_seconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "balance", "columnName": "balance", "affinity": "REAL", "notNull": true}, {"fieldPath": "stressState", "columnName": "stress_state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "syncedStatus", "columnName": "synced_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["timestamp_seconds"]}, "indices": [], "foreignKeys": []}, {"tableName": "summaryextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`pte` REAL, `feeling` INTEGER, `avgTemperature` REAL, `peakEpoc` REAL, `avgPower` REAL, `avgCadence` REAL, `avgSpeed` REAL, `ascentTime` REAL, `descentTime` REAL, `performanceLevel` REAL, `recoveryTime` INTEGER, `ascent` REAL, `descent` REAL, `deviceHardwareVersion` TEXT, `deviceSoftwareVersion` TEXT, `productType` TEXT, `displayName` TEXT, `deviceName` TEXT, `deviceSerialNumber` TEXT, `deviceManufacturer` TEXT, `exerciseId` TEXT, `zapps` TEXT NOT NULL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "pte", "columnName": "pte", "affinity": "REAL", "notNull": false}, {"fieldPath": "feeling", "columnName": "feeling", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "avgTemperature", "columnName": "avgTemperature", "affinity": "REAL", "notNull": false}, {"fieldPath": "peakEpoc", "columnName": "peakEpoc", "affinity": "REAL", "notNull": false}, {"fieldPath": "avgPower", "columnName": "avgPower", "affinity": "REAL", "notNull": false}, {"fieldPath": "avgCadence", "columnName": "avgCadence", "affinity": "REAL", "notNull": false}, {"fieldPath": "avgSpeed", "columnName": "avgSpeed", "affinity": "REAL", "notNull": false}, {"fieldPath": "ascentTime", "columnName": "ascentTime", "affinity": "REAL", "notNull": false}, {"fieldPath": "descentTime", "columnName": "descentTime", "affinity": "REAL", "notNull": false}, {"fieldPath": "performanceLevel", "columnName": "performanceLevel", "affinity": "REAL", "notNull": false}, {"fieldPath": "recoveryTime", "columnName": "recoveryTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "ascent", "columnName": "ascent", "affinity": "REAL", "notNull": false}, {"fieldPath": "descent", "columnName": "descent", "affinity": "REAL", "notNull": false}, {"fieldPath": "deviceHardwareVersion", "columnName": "deviceHardwareVersion", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deviceSoftwareVersion", "columnName": "deviceSoftwareVersion", "affinity": "TEXT", "notNull": false}, {"fieldPath": "productType", "columnName": "productType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deviceName", "columnName": "deviceName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deviceSerialNumber", "columnName": "deviceSerialNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deviceManufacturer", "columnName": "deviceManufacturer", "affinity": "TEXT", "notNull": false}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "zapps", "columnName": "zapps", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "diveextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`maxDepth` REAL, `algorithm` TEXT, `personalSetting` INTEGER, `diveNumberInSeries` INTEGER, `cns` REAL, `algorithmLock` INTEGER, `diveMode` TEXT, `otu` REAL, `pauseDuration` REAL, `gasConsumption` REAL, `altitudeSetting` REAL, `gasQuantities` TEXT NOT NULL, `surfaceTime` REAL, `gasesUsed` TEXT NOT NULL, `maxDepthTemperature` REAL, `avgDepth` REAL, `minGF` REAL, `maxGF` REAL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "max<PERSON><PERSON><PERSON>", "columnName": "max<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": false}, {"fieldPath": "algorithm", "columnName": "algorithm", "affinity": "TEXT", "notNull": false}, {"fieldPath": "personalSetting", "columnName": "personalSetting", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "diveNumberInSeries", "columnName": "diveNumberInSeries", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "cns", "columnName": "cns", "affinity": "REAL", "notNull": false}, {"fieldPath": "algorithmLock", "columnName": "algorithmLock", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "diveMode", "columnName": "diveMode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "otu", "columnName": "otu", "affinity": "REAL", "notNull": false}, {"fieldPath": "pauseDuration", "columnName": "pauseDuration", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasConsumption", "columnName": "gasConsumption", "affinity": "REAL", "notNull": false}, {"fieldPath": "altitudeSetting", "columnName": "altitudeSetting", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasQuantities", "columnName": "gasQuantities", "affinity": "TEXT", "notNull": true}, {"fieldPath": "surfaceTime", "columnName": "surfaceTime", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasesUsed", "columnName": "gasesUsed", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maxDepthTemperature", "columnName": "maxDepthTemperature", "affinity": "REAL", "notNull": false}, {"fieldPath": "avg<PERSON><PERSON><PERSON>", "columnName": "avg<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": false}, {"fieldPath": "minGF", "columnName": "minGF", "affinity": "REAL", "notNull": false}, {"fieldPath": "maxGF", "columnName": "maxGF", "affinity": "REAL", "notNull": false}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "smlzippreference", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`workoutId` INTEGER NOT NULL, `logbookEntryId` INTEGER NOT NULL, `zipPath` TEXT NOT NULL, `workoutKey` TEXT, `synced` INTEGER NOT NULL, `syncedErrorMessage` TEXT, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "logbookEntryId", "columnName": "logbookEntryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "zipPath", "columnName": "zipPath", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutKey", "columnName": "workoutKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "synced", "columnName": "synced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "syncedErrorMessage", "columnName": "syncedErrorMessage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "swimmingextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`avgSwolf` INTEGER NOT NULL, `avgStrokeRate` REAL NOT NULL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "avgSwolf", "columnName": "avgSwolf", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avgStrokeRate", "columnName": "avgStrokeRate", "affinity": "REAL", "notNull": true}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "routes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` TEXT NOT NULL, `watchRouteId` INTEGER NOT NULL, `key` TEXT NOT NULL, `ownerUserName` TEXT NOT NULL, `name` TEXT NOT NULL, `visibility` TEXT NOT NULL, `activityIds` TEXT NOT NULL, `avgSpeed` REAL NOT NULL, `totalDistance` REAL NOT NULL, `ascent` REAL NOT NULL, `startPoint` TEXT NOT NULL, `centerPoint` TEXT NOT NULL, `stopPoint` TEXT NOT NULL, `locallyChanged` INTEGER NOT NULL, `modifiedDate` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `created` INTEGER NOT NULL, `segmentsModifiedDate` INTEGER NOT NULL, `watchSyncState` TEXT NOT NULL, `watchSyncResponseCode` INTEGER NOT NULL, `watchEnabled` INTEGER NOT NULL, `segments` BLOB NOT NULL, `isInProgress` INTEGER NOT NULL, `turnWaypointsEnabled` INTEGER NOT NULL, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "watchRouteId", "columnName": "watchRouteId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ownerUserName", "columnName": "ownerUserName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visibility", "columnName": "visibility", "affinity": "TEXT", "notNull": true}, {"fieldPath": "activityIds", "columnName": "activityIds", "affinity": "TEXT", "notNull": true}, {"fieldPath": "averageSpeed", "columnName": "avgSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalDistance", "columnName": "totalDistance", "affinity": "REAL", "notNull": true}, {"fieldPath": "ascent", "columnName": "ascent", "affinity": "REAL", "notNull": true}, {"fieldPath": "startPoint", "columnName": "startPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "centerPoint", "columnName": "centerPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stopPoint", "columnName": "stopPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "locallyChanged", "columnName": "locallyChanged", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedDate", "columnName": "modifiedDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdDate", "columnName": "created", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "segmentsModifiedDate", "columnName": "segmentsModifiedDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "watchSyncState", "columnName": "watchSyncState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "watchSyncResponseCode", "columnName": "watchSyncResponseCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "watchEnabled", "columnName": "watchEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "segments", "columnName": "segments", "affinity": "BLOB", "notNull": true}, {"fieldPath": "isInProgress", "columnName": "isInProgress", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "turnWaypointsEnabled", "columnName": "turnWaypointsEnabled", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "rankings", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` TEXT NOT NULL, `workoutKey` TEXT NOT NULL, `rankingType` TEXT NOT NULL, `ranking` INTEGER, `numberOfWorkouts` INTEGER, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "workoutKey", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "rankingType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ranking", "columnName": "ranking", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "numberOfWorkouts", "columnName": "numberOfWorkouts", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "workoutAttributesUpdate", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`workoutId` INTEGER NOT NULL, `ownerUsername` TEXT NOT NULL, `attributes` TEXT, `fields` TEXT NOT NULL, `requiresUserConfirmation` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerUsername", "columnName": "ownerUsername", "affinity": "TEXT", "notNull": true}, {"fieldPath": "attributes", "columnName": "attributes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fields", "columnName": "fields", "affinity": "TEXT", "notNull": true}, {"fieldPath": "requiresUserConfirmation", "columnName": "requiresUserConfirmation", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "goal_definitions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userName` TEXT NOT NULL, `name` TEXT, `type` INTEGER NOT NULL, `period` INTEGER NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `target` INTEGER NOT NULL, `created` INTEGER NOT NULL, `activityIds` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "period", "columnName": "period", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "target", "columnName": "target", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityIds", "columnName": "activityIds", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "weatherextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`airPressure` REAL, `cloudiness` INTEGER, `groundLevelAirPressure` REAL, `humidity` INTEGER, `rainVolume1h` REAL, `rainVolume3h` REAL, `seaLevelAirPressure` REAL, `snowVolume1h` REAL, `snowVolume3h` REAL, `temperature` REAL, `weatherIcon` TEXT, `windDirection` REAL, `windSpeed` REAL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "airPressure", "columnName": "airPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "cloudiness", "columnName": "cloudiness", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "groundLevelAirPressure", "columnName": "groundLevelAirPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "humidity", "columnName": "humidity", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "rainVolume1h", "columnName": "rainVolume1h", "affinity": "REAL", "notNull": false}, {"fieldPath": "rainVolume3h", "columnName": "rainVolume3h", "affinity": "REAL", "notNull": false}, {"fieldPath": "seaLevelAirPressure", "columnName": "seaLevelAirPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "snowVolume1h", "columnName": "snowVolume1h", "affinity": "REAL", "notNull": false}, {"fieldPath": "snowVolume3h", "columnName": "snowVolume3h", "affinity": "REAL", "notNull": false}, {"fieldPath": "temperature", "columnName": "temperature", "affinity": "REAL", "notNull": false}, {"fieldPath": "weatherIcon", "columnName": "weatherIcon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "windDirection", "columnName": "windDirection", "affinity": "REAL", "notNull": false}, {"fieldPath": "windSpeed", "columnName": "windSpeed", "affinity": "REAL", "notNull": false}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId"]}, "indices": [], "foreignKeys": []}, {"tableName": "achievements", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `workoutKey` TEXT NOT NULL, `activityType` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, `cumulativeAchievements` TEXT NOT NULL, `personalBestAchievements` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutKey", "columnName": "workoutKey", "affinity": "TEXT", "notNull": true}, {"fieldPath": "activityType", "columnName": "activityType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cumulativeAchievements", "columnName": "cumulativeAchievements", "affinity": "TEXT", "notNull": true}, {"fieldPath": "personalBestAchievements", "columnName": "personalBestAchievements", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "pois", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`creation` INTEGER NOT NULL, `modified` INTEGER NOT NULL, `longitude` REAL NOT NULL, `latitude` REAL NOT NULL, `altitude` REAL, `name` TEXT, `type` INTEGER, `activityId` INTEGER, `country` TEXT, `locality` TEXT, `watchEnabled` INTEGER NOT NULL, `key` TEXT, `syncState` TEXT NOT NULL, `deleted` INTEGER NOT NULL, `remoteSyncErrorCode` INTEGER, `watchSyncErrorCode` INTEGER, PRIMARY KEY(`creation`))", "fields": [{"fieldPath": "creation", "columnName": "creation", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modified", "columnName": "modified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "altitude", "columnName": "altitude", "affinity": "REAL", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "activityId", "columnName": "activityId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "country", "columnName": "country", "affinity": "TEXT", "notNull": false}, {"fieldPath": "locality", "columnName": "locality", "affinity": "TEXT", "notNull": false}, {"fieldPath": "watchEnabled", "columnName": "watchEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "syncState", "columnName": "syncState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remoteSyncErrorCode", "columnName": "remoteSyncErrorCode", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "watchSyncErrorCode", "columnName": "watchSyncErrorCode", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["creation"]}, "indices": [], "foreignKeys": []}, {"tableName": "poi_sync_log", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`timestamp` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, `event` TEXT NOT NULL, `metadata` TEXT, `shown` INTEGER, `_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "timestampMillis", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "event", "columnName": "event", "affinity": "TEXT", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shown", "columnName": "shown", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "gear", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serialNumber` TEXT NOT NULL, `manufacturer` TEXT NOT NULL, `name` TEXT NOT NULL, `softwareVersion` TEXT NOT NULL, `hardwareVersion` TEXT NOT NULL, `lastSyncDate` INTEGER NOT NULL, `firstSyncDate` INTEGER, PRIMARY KEY(`serialNumber`))", "fields": [{"fieldPath": "serialNumber", "columnName": "serialNumber", "affinity": "TEXT", "notNull": true}, {"fieldPath": "manufacturer", "columnName": "manufacturer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "softwareVersion", "columnName": "softwareVersion", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hardwareVersion", "columnName": "hardwareVersion", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastSyncDate", "columnName": "lastSyncDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "firstSyncDate", "columnName": "firstSyncDate", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["serialNumber"]}, "indices": [], "foreignKeys": []}, {"tableName": "suunto_plus_guides", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `catalogueId` TEXT, `modified` INTEGER NOT NULL, `name` TEXT NOT NULL, `owner` TEXT NOT NULL, `date` TEXT, `url` TEXT, `iconUrl` TEXT, `description` TEXT NOT NULL, `activityIds` TEXT, `pinned` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `remoteSyncErrorCode` INTEGER, `watchSyncErrorCode` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "catalogueId", "columnName": "catalogueId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "modified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "owner", "columnName": "owner", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconUrl", "columnName": "iconUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "activityIds", "columnName": "activityIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remoteSyncErrorCode", "columnName": "remoteSyncErrorCode", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "watchSyncErrorCode", "columnName": "watchSyncErrorCode", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "suunto_plus_features", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `modified` INTEGER NOT NULL, `name` TEXT NOT NULL, `owner` TEXT NOT NULL, `url` TEXT, `iconUrl` TEXT NOT NULL, `description` TEXT NOT NULL, `short_description` TEXT, `enabled` INTEGER NOT NULL, `expiration` INTEGER, `plugin_id` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "modified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "owner", "columnName": "owner", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconUrl", "columnName": "iconUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shortDescription", "columnName": "short_description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expiration<PERSON>ime<PERSON><PERSON><PERSON>", "columnName": "expiration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "pluginId", "columnName": "plugin_id", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "suunto_plus_plugin_device_status", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `plugin_id` TEXT NOT NULL, `modified` INTEGER NOT NULL, `capabilities` TEXT NOT NULL, `type` TEXT NOT NULL, `interest_value` INTEGER, `status` TEXT NOT NULL, PRIMARY KEY(`serial`, `plugin_id`))", "fields": [{"fieldPath": "watchSerial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pluginId", "columnName": "plugin_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "modified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "capabilities", "columnName": "capabilities", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "interestValue", "columnName": "interest_value", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["serial", "plugin_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "suunto_plus_guide_sync_log", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`timestamp` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, `event` TEXT NOT NULL, `metadata` TEXT, `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "timestampMillis", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "event", "columnName": "event", "affinity": "TEXT", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "watch_capabilities", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `model` TEXT NOT NULL, `fw` TEXT NOT NULL, `hw` TEXT NOT NULL, `capabilities` TEXT NOT NULL, PRIMARY KEY(`serial`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "model", "columnName": "model", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fwVersion", "columnName": "fw", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hwVersion", "columnName": "hw", "affinity": "TEXT", "notNull": true}, {"fieldPath": "capabilities", "columnName": "capabilities", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["serial"]}, "indices": [], "foreignKeys": []}, {"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `key` TEXT, `username` TEXT NOT NULL, `session` TEXT, `website` TEXT, `city` TEXT, `country` TEXT, `profileImageUrl` TEXT, `profileImageKey` TEXT, `realName` TEXT, `description` TEXT, `followModel` INTEGER, `fieldTester` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "session", "columnName": "session", "affinity": "TEXT", "notNull": false}, {"fieldPath": "website", "columnName": "website", "affinity": "TEXT", "notNull": false}, {"fieldPath": "city", "columnName": "city", "affinity": "TEXT", "notNull": false}, {"fieldPath": "country", "columnName": "country", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImageUrl", "columnName": "profileImageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImage<PERSON>ey", "columnName": "profileImage<PERSON>ey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "realName", "columnName": "realName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "followModel", "columnName": "followModel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fieldTester", "columnName": "fieldTester", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_user_username", "unique": false, "columnNames": ["username"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_user_username` ON `${TABLE_NAME}` (`username`)"}], "foreignKeys": []}, {"tableName": "workout_headers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `key` TEXT, `totalDistance` REAL NOT NULL, `maxSpeed` REAL NOT NULL, `activityId` INTEGER NOT NULL, `avgSpeed` REAL NOT NULL, `description` TEXT, `startPosition` TEXT, `stopPosition` TEXT, `centerPosition` TEXT, `startTime` INTEGER NOT NULL, `stopTime` INTEGER NOT NULL, `totalTime` REAL NOT NULL, `energyConsumption` REAL NOT NULL, `username` TEXT NOT NULL, `heartRateAvg` REAL NOT NULL, `heartRateAvgPercentage` REAL NOT NULL, `heartRateMax` REAL NOT NULL, `heartRateMaxPercentage` REAL NOT NULL, `heartRateUserSetMax` REAL NOT NULL, `pictureCount` INTEGER NOT NULL, `viewCount` INTEGER NOT NULL, `commentCount` INTEGER NOT NULL, `sharingFlags` INTEGER NOT NULL, `locallyChanged` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `manuallyCreated` INTEGER NOT NULL, `averageCadence` INTEGER NOT NULL, `maxCadence` INTEGER NOT NULL, `polyline` TEXT, `stepCount` INTEGER NOT NULL, `reactionCount` INTEGER NOT NULL, `totalAscent` REAL NOT NULL, `totalDescent` REAL NOT NULL, `recoveryTime` INTEGER NOT NULL, `maxAltitude` REAL, `minAltitude` REAL, `seen` INTEGER NOT NULL, `extensionsFetched` INTEGER NOT NULL, `tss` TEXT, `tssList` TEXT, `isCommute` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalDistance", "columnName": "totalDistance", "affinity": "REAL", "notNull": true}, {"fieldPath": "maxSpeed", "columnName": "maxSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "activityId", "columnName": "activityId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avgSpeed", "columnName": "avgSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startPosition", "columnName": "startPosition", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stopPosition", "columnName": "stopPosition", "affinity": "TEXT", "notNull": false}, {"fieldPath": "centerPosition", "columnName": "centerPosition", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stopTime", "columnName": "stopTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalTime", "columnName": "totalTime", "affinity": "REAL", "notNull": true}, {"fieldPath": "energyConsumption", "columnName": "energyConsumption", "affinity": "REAL", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "heartRateAvg", "columnName": "heartRateAvg", "affinity": "REAL", "notNull": true}, {"fieldPath": "heartRateAvgPercentage", "columnName": "heartRateAvgPercentage", "affinity": "REAL", "notNull": true}, {"fieldPath": "heartRateMax", "columnName": "heartRateMax", "affinity": "REAL", "notNull": true}, {"fieldPath": "heartRateMaxPercentage", "columnName": "heartRateMaxPercentage", "affinity": "REAL", "notNull": true}, {"fieldPath": "heartRateUserSetMax", "columnName": "heartRateUserSetMax", "affinity": "REAL", "notNull": true}, {"fieldPath": "pictureCount", "columnName": "pictureCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "viewCount", "columnName": "viewCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "commentCount", "columnName": "commentCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sharingFlags", "columnName": "sharingFlags", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "locallyChanged", "columnName": "locallyChanged", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "manuallyCreated", "columnName": "manuallyCreated", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageCadence", "columnName": "averageCadence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxCadence", "columnName": "maxCadence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "polyline", "columnName": "polyline", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stepCount", "columnName": "stepCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reactionCount", "columnName": "reactionCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalAscent", "columnName": "totalAscent", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalDescent", "columnName": "totalDescent", "affinity": "REAL", "notNull": true}, {"fieldPath": "recoveryTime", "columnName": "recoveryTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxAltitude", "columnName": "maxAltitude", "affinity": "REAL", "notNull": false}, {"fieldPath": "minAltitude", "columnName": "minAltitude", "affinity": "REAL", "notNull": false}, {"fieldPath": "seen", "columnName": "seen", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extensionsFetched", "columnName": "extensionsFetched", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tss", "columnName": "tss", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tssList", "columnName": "tssList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isCommute", "columnName": "isCommute", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_workout_headers_key", "unique": false, "columnNames": ["key"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_headers_key` ON `${TABLE_NAME}` (`key`)"}, {"name": "index_workout_headers_startTime", "unique": false, "columnNames": ["startTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_headers_startTime` ON `${TABLE_NAME}` (`startTime`)"}], "foreignKeys": []}, {"tableName": "user_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `key` TEXT, `name` TEXT NOT NULL, `lastAddedDate` INTEGER NOT NULL, `lastModifiedDate` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastAddedDate", "columnName": "lastAddedDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModifiedDate", "columnName": "lastModifiedDate", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "user_tag_workout_headers_cross_ref", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`workoutId` INTEGER NOT NULL, `userTagId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`, `userTagId`), <PERSON>OREI<PERSON><PERSON> KEY(`workoutId`) REFERENCES `workout_headers`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOR<PERSON><PERSON><PERSON> KEY(`userTagId`) REFERENCES `user_tag`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userTagId", "columnName": "userTagId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["workoutId", "userTagId"]}, "indices": [], "foreignKeys": [{"table": "workout_headers", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["workoutId"], "referencedColumns": ["id"]}, {"table": "user_tag", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["userTagId"], "referencedColumns": ["id"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '700c3ed65d27a43946c587b51dca6174')"]}}