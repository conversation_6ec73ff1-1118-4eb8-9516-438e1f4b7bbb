package com.stt.android.data.source.local.offlinemusic

import androidx.room.TypeConverter

enum class LocalOfflineMusicSourceType {
    HEADSET, WATCH
}

class LocalOfflineMusicSourceConverter {
    @TypeConverter
    fun fromLocalOfflineMusicSource(localOfflineMusicSourceType: LocalOfflineMusicSourceType?): String? =
        localOfflineMusicSourceType?.name

    @TypeConverter
    fun toLocalOfflineMusicSource(localOfflineMusicSourceString: String?): LocalOfflineMusicSourceType? =
        LocalOfflineMusicSourceType.entries.firstOrNull { it.name == localOfflineMusicSourceString }
}
