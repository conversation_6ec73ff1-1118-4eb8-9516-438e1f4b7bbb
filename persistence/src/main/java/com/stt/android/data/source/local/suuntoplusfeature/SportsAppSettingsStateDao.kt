package com.stt.android.data.source.local.suuntoplusfeature

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import com.stt.android.data.source.local.TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
import kotlinx.coroutines.flow.Flow

@Dao
abstract class SportsAppSettingsStateDao {

    @Transaction
    open suspend fun setLockedFlag(watchSerial: String, pluginId: String, locked: Boolean) {
        if (find(watchSerial, pluginId) == null) {
            insert(
                LocalSportsAppSettingsState(
                    watchSerial = watchSerial,
                    pluginId = pluginId,
                    locked = locked,
                    settingsBusy = false,
                    settingsDataJson = null
                )
            )
        } else {
            updateLocked(
                watchSerial = watchSerial,
                pluginId = pluginId,
                locked = locked
            )
        }
    }

    suspend fun findDataJson(watchSerial: String, pluginId: String): String? = find(
        watchSerial = watchSerial,
        pluginId = pluginId
    )?.settingsData<PERSON>son

    suspend fun isBusy(watchSerial: String, pluginId: String): Boolean = find(
        watchSerial = watchSerial,
        pluginId = pluginId
    )?.settingsBusy == true

    suspend fun isMarkedAsLocked(watchSerial: String, pluginId: String): Boolean = find(
        watchSerial = watchSerial,
        pluginId = pluginId
    )?.locked == true

    @Transaction
    open suspend fun setBusy(watchSerial: String, pluginId: String) {
        val existingState = find(watchSerial, pluginId)
        if (existingState?.settingsBusy == true) {
            throw IllegalStateException("Unable to set settings busy flag for $pluginId - already busy!")
        }

        if (existingState == null) {
            insert(
                LocalSportsAppSettingsState(
                    watchSerial = watchSerial,
                    pluginId = pluginId,
                    locked = false,
                    settingsBusy = true,
                    settingsDataJson = null
                )
            )
        } else {
            updateBusy(
                watchSerial = watchSerial,
                pluginId = pluginId,
                busy = true
            )
        }
    }

    @Transaction
    open suspend fun clearBusy(watchSerial: String, pluginId: String) {
        updateBusy(
            watchSerial = watchSerial,
            pluginId = pluginId,
            busy = false
        )
    }

    @Transaction
    open suspend fun setDataJson(watchSerial: String, pluginId: String, dataJson: String?) {
        if (find(watchSerial, pluginId) == null) {
            insert(
                LocalSportsAppSettingsState(
                    watchSerial = watchSerial,
                    pluginId = pluginId,
                    locked = false,
                    settingsBusy = false,
                    settingsDataJson = dataJson
                )
            )
        } else {
            updateDataJson(
                watchSerial = watchSerial,
                pluginId = pluginId,
                dataJson = dataJson
            )
        }
    }

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
        SET ${LocalSportsAppSettingsState.SETTINGS_DATA_JSON} = :dataJson
        WHERE ${LocalSportsAppSettingsState.WATCH_SERIAL} = :watchSerial AND ${LocalSportsAppSettingsState.PLUGIN_ID} = :pluginId
        """
    )
    abstract suspend fun updateDataJson(watchSerial: String, pluginId: String, dataJson: String?)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
        SET ${LocalSportsAppSettingsState.SETTINGS_BUSY} = :busy
        WHERE ${LocalSportsAppSettingsState.WATCH_SERIAL} = :watchSerial AND ${LocalSportsAppSettingsState.PLUGIN_ID} = :pluginId
        """
    )
    abstract suspend fun updateBusy(watchSerial: String, pluginId: String, busy: Boolean)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
        SET ${LocalSportsAppSettingsState.LOCKED} = :locked
        WHERE ${LocalSportsAppSettingsState.WATCH_SERIAL} = :watchSerial AND ${LocalSportsAppSettingsState.PLUGIN_ID} = :pluginId
        """
    )
    abstract suspend fun updateLocked(watchSerial: String, pluginId: String, locked: Boolean)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
        SET ${LocalSportsAppSettingsState.LOCKED} = 0, ${LocalSportsAppSettingsState.SETTINGS_BUSY} = 0
        """
    )
    abstract suspend fun resetForNewWatchConnection()

    @Query(
        """
        SELECT (
            SELECT
                COUNT(*) FROM $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
            WHERE
               ${LocalSportsAppSettingsState.WATCH_SERIAL} == :watchSerial AND
               ${LocalSportsAppSettingsState.SETTINGS_BUSY} != 0
        ) > 0;
        """
    )
    abstract fun anySportsAppBusy(watchSerial: String): Flow<Boolean>

    @Query(
        """
        SELECT * FROM $TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE
        WHERE ${LocalSportsAppSettingsState.WATCH_SERIAL} = :watchSerial AND ${LocalSportsAppSettingsState.PLUGIN_ID} = :pluginId
        """
    )
    abstract suspend fun find(watchSerial: String, pluginId: String): LocalSportsAppSettingsState?

    @Insert
    abstract suspend fun insert(state: LocalSportsAppSettingsState)
}
