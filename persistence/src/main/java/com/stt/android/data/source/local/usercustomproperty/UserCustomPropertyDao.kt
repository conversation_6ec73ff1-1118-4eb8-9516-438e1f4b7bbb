package com.stt.android.data.source.local.usercustomproperty

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_USER_CUSTOM_PROPERTY

@Dao
abstract class UserCustomPropertyDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun save(localUserCustomProperty: LocalUserCustomProperty)

    @Update
    abstract suspend fun update(localUserCustomProperty: LocalUserCustomProperty)

    @Query(
        """
        SELECT *
        FROM $TABLE_USER_CUSTOM_PROPERTY
        WHERE `key` = :key
        """
    )
    abstract suspend fun findByKey(key: String): LocalUserCustomProperty?

    @Query(
        """
        SELECT *
        FROM $TABLE_USER_CUSTOM_PROPERTY
        WHERE isSynced = 0
        """
    )
    abstract suspend fun findNotSync(): List<LocalUserCustomProperty>

    @Query(
        """
            DELETE
            FROM $TABLE_USER_CUSTOM_PROPERTY
        """
    )
    abstract fun deleteAll()
}
