package com.stt.android.data.source.local.menstrualcycle

import androidx.room.TypeConverter

enum class LocalMenstrualCycleType {
    HISTORICAL,
    PREDICTED
}

class LocalMenstrualCycleTypeConverter {

    @TypeConverter
    fun fromLocalMenstrualCycleType(localMenstrualCycleType: LocalMenstrualCycleType): String =
        localMenstrualCycleType.name

    @TypeConverter
    fun toLocalMenstrualCycleType(localMenstrualCycleTypeString: String): LocalMenstrualCycleType =
        LocalMenstrualCycleType.entries.first { it.name == localMenstrualCycleTypeString }
}
