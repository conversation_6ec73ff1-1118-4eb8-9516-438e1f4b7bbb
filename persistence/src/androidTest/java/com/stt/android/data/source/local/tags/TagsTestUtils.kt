package com.stt.android.data.source.local.tags

object TagsTestUtils {
    fun createLocalUserTag(
        id: Int = 1,
        key: String? = "key",
        name: String = "name"
    ) = LocalUserTag(
        userTagId = id,
        userTagKey = key,
        name = name
    )

    fun createLocalUserTagWorkoutHeaderCrossRef(
        workoutId: Int = 1,
        userTagId: Int = 2,
        isSynced: Boolean = false,
        isRemoved: Boolean = false
    ) = LocalUserTagWorkoutHeaderCrossRef(
        workoutId = workoutId,
        userTagId = userTagId,
        isSynced = isSynced,
        isRemoved = isRemoved
    )
}
