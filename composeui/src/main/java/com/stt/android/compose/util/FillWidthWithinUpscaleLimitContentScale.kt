package com.stt.android.compose.util

import androidx.compose.ui.geometry.Size
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.ScaleFactor

/**
 * Content scale implementation that tries to fill available width if possible, but will never
 * scale the image to larger than [maxUpscaleFactor] size from the original.
 */
class FillWidthWithinUpscaleLimitContentScale(private val maxUpscaleFactor: Float) : ContentScale {
    override fun computeScaleFactor(srcSize: Size, dstSize: Size): ScaleFactor =
        if (srcSize.width > 0f) {
            val factor = (dstSize.width / srcSize.width).coerceAtMost(maxUpscaleFactor)
            ScaleFactor(factor, factor)
        } else {
            ScaleFactor(1f, 1f)
        }
}
