package com.stt.android.compose.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Shapes
import androidx.compose.material3.Typography
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun M3AppTheme(
    content: @Composable () -> Unit
) {
    // Material 3 theme
    SuuntoAppLightTheme(content)
}

private val lightColorScheme = lightColorScheme(
    primary = SuuntoColors.suuntoBlue,
    onPrimary = Color.White,
    primaryContainer = SuuntoColors.nearBlack,
    onPrimaryContainer = Color.White,
    inversePrimary = SuuntoColors.suuntoBlue,
    secondary = SuuntoColors.darkGrey,
    onSecondary = Color.White,
    secondaryContainer = SuuntoColors.lightGrey,
    tertiary = SuuntoColors.nearBlack,
    onTertiary = Color.White,
    background = SuuntoColors.lightGrey,
    onBackground = SuuntoColors.nearBlack,
    surface = Color.White,
    onSurface = SuuntoColors.nearBlack,
    error = SuuntoColors.brightRed,
    onError = Color.White,
    outlineVariant = SuuntoColors.lightGrey,
)

private val shapes = Shapes(
    medium = RoundedCornerShape(8.dp),
    large = RoundedCornerShape(16.dp),
)

private val bodyMedium: TextStyle = TextStyle(
    fontSize = 14.sp,
    lineHeight = 18.sp,
    fontFamily = suuntoFontFamily
)

private val typography = Typography(
    bodySmall = bodyMedium.merge(TextStyle(fontSize = 12.sp, lineHeight = 16.sp)),
    bodyMedium = bodyMedium,
    bodyLarge = bodyMedium.merge(TextStyle(fontSize = 16.sp, lineHeight = 20.sp)),
    displayMedium = TextStyle(
        fontFamily = suuntoFontFamily,
        fontSize = 14.sp,
        lineHeight = 24.sp,
        fontWeight = FontWeight.Bold
    ),
    titleLarge = TextStyle(
        fontFamily = suuntoFontFamily,
        fontSize = 14.sp,
        fontWeight = FontWeight.Bold
    ),
    labelLarge = TextStyle(
        fontFamily = suuntoFontFamily,
        fontSize = 14.sp,
        fontWeight = FontWeight.Bold
    )
)

@Composable
private fun SuuntoAppLightTheme(
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(LocalIsMaterial3 provides true) {
        MaterialTheme(
            colorScheme = lightColorScheme,
            typography = typography,
            shapes = shapes,
            content = content
        )
    }
}
