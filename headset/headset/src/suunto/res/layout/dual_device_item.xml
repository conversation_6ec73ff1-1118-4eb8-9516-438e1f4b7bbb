<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_dual_device_name"
        style="@style/Body.Larger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/size_spacing_medium"
        android:text="@string/connect_two_device_label"
        tools:text="ashdkjashdkjsahdkjsahjkdhsajkhdjksahdkjsaasdasdsadas"
        android:ellipsize="end"
        android:maxLines="1"
        android:maxEms="13"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dual_device_name" />
</androidx.constraintlayout.widget.ConstraintLayout>
