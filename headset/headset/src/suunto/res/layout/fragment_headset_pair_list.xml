<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator"
    android:background="?suuntoItemBackgroundColor"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/padding"
    android:paddingEnd="@dimen/padding">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_anchor="@+id/list"
        app:layout_behavior="com.stt.android.watch.VerticalFadeBehavior">

        <ImageView
            android:id="@+id/device_scan_overlay"
            android:layout_width="0dp"
            android:layout_height="@dimen/device_image_height"
            android:src="@drawable/headset_pairing_ghost"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/device_scan_animation"
            android:layout_width="66dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            app:layout_constraintBottom_toBottomOf="@+id/device_scan_overlay"
            app:layout_constraintEnd_toEndOf="@+id/device_scan_overlay"
            app:layout_constraintStart_toStartOf="@+id/device_scan_overlay"
            app:layout_constraintTop_toTopOf="@+id/device_scan_overlay"
            app:lottie_autoPlay="true"
            app:lottie_fileName="@string/headphone_activity_anim_progress"
            app:lottie_loop="true" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone"
        tools:listitem="@layout/headset_device_item" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
