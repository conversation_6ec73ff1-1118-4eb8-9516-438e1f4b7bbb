package com.suunto.headset.viewmodel

import androidx.compose.runtime.mutableStateOf
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.FEET_TO_METERS
import com.stt.android.domain.user.METERS_TO_YARDS
import com.stt.android.eventtracking.EventTracker
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.model.PoolLength
import com.suunto.headset.model.PoolLengthUnit
import com.suunto.headset.model.PoolLengthUnitType
import com.suunto.headset.model.SportType
import com.suunto.headset.model.SportsTypeOption
import com.suunto.soa.ble.control.attr.isRoping
import com.suunto.soa.ble.control.attr.isRunning
import com.suunto.soa.ble.control.attr.isSwimming
import com.suunto.soa.ble.response.ButtonShortcutKey
import com.suunto.soa.data.SaoDistanceUnit
import com.suunto.soa.data.SoaPoolDistance
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

@HiltViewModel
class SportsSwitchViewModel @Inject constructor(
    private val buttonCustomizedUseCase: ButtonCustomizedUseCase,
    private val baseHeadsetFeature: BaseHeadsetFeature,
    private val supportedHeadsetDevices: SupportedHeadsetDevices,
    private val deviceStatusNotifyUseCase: DeviceStatusNotifyUseCase,
    private val eventTracker: EventTracker,
    coroutinesDispatchers: CoroutinesDispatchers
) : CoroutineViewModel(coroutinesDispatchers) {

    private val _sportTypeOptions =
        MutableStateFlow<ImmutableList<SportsTypeOption>>(persistentListOf())
    val sportTypeOptions = _sportTypeOptions.asStateFlow()

    private val _sportButton = MutableStateFlow(
        buttonCustomizedUseCase.getDefaultSportButton()
    )
    val sportButton = _sportButton.asStateFlow()

    val modifySupported: Boolean
        get() = supportedHeadsetDevices.supportSportButtonModify(baseHeadsetFeature)
    val poolDistanceSettingSupported: Boolean
        get() = supportedHeadsetDevices.supportPoolDistanceSetting(baseHeadsetFeature)

    var conflictButton = mutableStateOf<HeadsetButton?>(null)

    private val _headphoneStartSport = MutableStateFlow(false)
    val headphoneStartSport = _headphoneStartSport.asStateFlow()

    init {
        getSportButton()
        getSportTypes()
        startNotifyHeadphoneStatus()
    }

    private fun startNotifyHeadphoneStatus() {
        launch(io) {
            deviceStatusNotifyUseCase.getDeviceStatusFlow().collect { deviceStatus ->
                deviceStatus.getSwimStatus()?.let { swimStatus ->
                    _headphoneStartSport.value = swimStatus.isSwimming()
                }
                deviceStatus.jumpRopeSportStatus()?.let { jumpRopeStatus ->
                    _headphoneStartSport.value = jumpRopeStatus.isRoping()
                }
                deviceStatus.runningSportStatus()?.let { runningStatus ->
                    _headphoneStartSport.value = runningStatus.isRunning()
                }
            }
        }
    }

    private fun getSportButton() {
        launch(io) {
            runSuspendCatching {
                buttonCustomizedUseCase.getSportButton()
            }.onSuccess { sportButton ->
                Timber.d("get sport button:%s", sportButton)
                sportButton?.let { _sportButton.value = it }
            }.onFailure {
                Timber.w(it, "get custom buttons failed: ${it.message}")
            }
        }
    }

    private fun getSportTypes() {
        launch(io) {
            val sportTypes = supportedHeadsetDevices.getSportTypes(baseHeadsetFeature)
            runSuspendCatching {
                baseHeadsetFeature.getSportType()
            }.onSuccess { currentSportType ->
                Timber.d("get currentSportType:%s", currentSportType)
                _sportTypeOptions.value = sportTypes.map {
                    SportsTypeOption(it, it == currentSportType)
                }.toPersistentList()
                getPoolDistanceSetting()
            }.onFailure { e ->
                Timber.w(e, "get sport type failed: ${e.message}")
                _sportTypeOptions.value = sportTypes.map {
                    SportsTypeOption(it, false)
                }.toPersistentList()
            }
        }
    }

    fun setPoolLength(poolLength: Int, unit: PoolLengthUnit) {
        launch(io) {
            runSuspendCatching {
                val poolDistance = if (unit.unitType == PoolLengthUnitType.METRIC) {
                    SoaPoolDistance(
                        distanceInMetric = poolLength,
                        distanceUnit = SaoDistanceUnit.METRIC,
                        distanceInImperial = (poolLength * METERS_TO_YARDS).roundToInt(),
                    )
                } else {
                    SoaPoolDistance(
                        distanceInMetric = (poolLength / METERS_TO_YARDS).roundToInt(),
                        distanceUnit = SaoDistanceUnit.IMPERIAL,
                        distanceInImperial = poolLength,
                    )
                }
                baseHeadsetFeature.setPoolDistance(poolDistance)
            }.onSuccess { successfully ->
                Timber.d("set pool distance:$successfully")
                if (successfully) {
                    _sportTypeOptions.update { options ->
                        options.map {
                            if (it.sportType == SportType.POOL_SWIMMING) {
                                it.copy(poolLength = PoolLength.create(poolLength, unit))
                            } else it
                        }.toPersistentList()
                    }
                }
            }.onFailure {
                Timber.w(it, "set pool distance failed: ${it.message}")
            }
        }
    }

    fun switchSportType(sportType: SportType) {
        launch(io) {
            runSuspendCatching {
                baseHeadsetFeature.updateSportType(sportType)
            }.onSuccess {
                _sportTypeOptions.update { options ->
                    options.map { it.copy(enabled = it.sportType == sportType) }.toImmutableList()
                }
                sendAnalysisEvent(sportType)
            }.onFailure {
                Timber.w(it, "switch sport type failed: ${it.message}")
            }
        }
    }

    fun getButtonShortcutListForSportSetting(): List<ButtonShortcutKey> {
        return buttonCustomizedUseCase.getDefaultButtonShortcutList().filterNot {
            it == ButtonShortcutKey.EMPTY
        }
    }

    suspend fun getConflictButton(sportButton: HeadsetButton): HeadsetButton? {
        return buttonCustomizedUseCase.getShortcutKeyConflictButton(sportButton)
    }

    fun updateSportButton(sportButton: HeadsetButton) {
        launch(io) {
            runSuspendCatching {
                buttonCustomizedUseCase.updateButton(sportButton)
            }.onSuccess {
                Timber.d("updateSportButton successful!")
                _sportButton.value = sportButton
            }.onFailure {
                Timber.w(it, "updateSportButton failed: ${it.message}")
            }
        }
    }

    private fun getPoolDistanceSetting() {
        if (poolDistanceSettingSupported) {
            launch(io) {
                runSuspendCatching {
                    baseHeadsetFeature.getPoolDistance()
                }.onSuccess { soaPoolDistance ->
                    Timber.d("get pool distance:$soaPoolDistance successful!")
                    soaPoolDistance?.let { distance ->
                        val (distanceValue, unit) = if (distance.distanceUnit == SaoDistanceUnit.METRIC) {
                            distance.distanceInMetric to PoolLengthUnit(
                                R.string.meters,
                                PoolLengthUnitType.METRIC
                            )
                        } else {
                            distance.distanceInImperial to PoolLengthUnit(
                                R.string.TXT_YD,
                                PoolLengthUnitType.IMPERIAL
                            )
                        }
                        _sportTypeOptions.update { options ->
                            options.map {
                                if (it.sportType == SportType.POOL_SWIMMING) {
                                    it.copy(poolLength = PoolLength.create(distanceValue, unit))
                                } else it
                            }.toPersistentList()
                        }
                    }

                }.onFailure {
                    Timber.w(it, "get pool distance failed: ${it.message}")
                }
            }
        }
    }

    private fun sendAnalysisEvent(sportType: SportType) {
        eventTracker.trackEvent(
            AnalyticsEvent.HEADPHONE_SETTINGS,
            hashMapOf(
                AnalyticsEventProperty.FEATURE_NAME to AnalyticsPropertyValue.HeadphoneFeatureName.SPORTS_SWITCH,
                AnalyticsEventProperty.SETTING to when (sportType) {
                    SportType.JUMP_ROPE -> AnalyticsPropertyValue.SportSwitchValue.JUMP_ROPE
                    SportType.POOL_SWIMMING -> AnalyticsPropertyValue.SportSwitchValue.POOL_SWIMMING
                    SportType.RUNNING -> AnalyticsPropertyValue.SportSwitchValue.RUNNING
                    SportType.OPEN_WATER_SWIMMING -> AnalyticsPropertyValue.SportSwitchValue.OPEN_WATER_SWIMMING
                }
            )
        )
    }
}
