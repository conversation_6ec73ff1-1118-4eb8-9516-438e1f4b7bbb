package com.suunto.headset.capability

import com.suunto.soa.ble.client.DeviceInfoFeatures
import com.suunto.soa.ble.client.RunningInfoFeatures
import com.suunto.soa.ble.control.attr.BodySenseOperation
import com.suunto.soa.ble.control.attr.DevicesConnectSupport
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.attr.AttrBluetoothStatus
import javax.inject.Inject

open class SU03HeadsetFeature @Inject constructor(
    private val runningInfoFeatures: RunningInfoFeatures,
    private val deviceInfoFeatures: DeviceInfoFeatures,
    val supportedHeadsetDevices: SupportedHeadsetDevices,
    headsetBtDevice: HeadsetBtDevice
) : BaseHeadsetFeature(headsetBtDevice) {

    override suspend fun getSerial(): String {
        if (!supportedHeadsetDevices.supportGetSerial(this)) {
            throw UnsupportedOperationException()
        }
        return deviceInfoFeatures.getSerial()
    }

    override suspend fun getDualDeviceConnect(): DevicesConnectSupport {
        if (!supportedHeadsetDevices.supportDualDeviceConnect(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getSupportDevicesConnect()
    }

    override suspend fun setDualDeviceConnect(support: DevicesConnectSupport): Boolean {
        if (!supportedHeadsetDevices.supportDualDeviceConnect(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.setSupportDevicesConnect(support)
    }

    override suspend fun getConnectedDevices(): List<AttrBluetoothStatus.ConnectedDevice> {
        if (!supportedHeadsetDevices.supportGetConnectedDevices(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getConnectedDevices()
    }

    override suspend fun getBodySense(): BodySenseOperation {
        if (!supportedHeadsetDevices.supportBodySense(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getBodySense()
    }

    override suspend fun setBodySense(bodySenseOperation: BodySenseOperation): Boolean {
        if (!supportedHeadsetDevices.supportBodySense(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.setBodySense(bodySenseOperation)
    }

    override suspend fun getEqMode(): EqMode {
        if (!supportedHeadsetDevices.supportEqMode(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getEqMode()
    }

    override suspend fun setEqMode(eqMode: EqMode): Boolean {
        if (!supportedHeadsetDevices.supportEqMode(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.setEqMode(eqMode)
    }

    override suspend fun getLowLatencyMode(): LowLatencyMode {
        if (!supportedHeadsetDevices.supportLowLatency(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getLowLatencyMode()
    }

    override suspend fun setLowLatencyMode(lowLatencyMode: LowLatencyMode): Boolean {
        if (!supportedHeadsetDevices.supportLowLatency(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.setLowLatencyMode(lowLatencyMode)
    }

    override suspend fun getSportsMode(): SportsMode {
        if (!supportedHeadsetDevices.supportLEDLightMode(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.getSportsMode()
    }

    override suspend fun setSportsMode(sportsMode: SportsMode): Boolean {
        if (!supportedHeadsetDevices.supportLEDLightMode(this)) {
            throw UnsupportedOperationException()
        }
        return runningInfoFeatures.setSportsMode(sportsMode)
    }

    override suspend fun setLedLightSwitchState(enabled: Boolean): Boolean {
        if (!supportedHeadsetDevices.supportLEDLightMode(this)) {
            throw UnsupportedOperationException()
        }
        return setSportsMode(if (enabled) SportsMode.TEAM else SportsMode.CLOSE)
    }
}
