package com.suunto.headset.ui.components.jump

import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Utils
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.domain.user.MeasurementUnit
import com.suunto.headset.R
import com.suunto.headset.assessmentstandard.JumpAssessmentHelper
import com.suunto.headset.assessmentstandard.NeuromuscularFatigueLevel
import com.suunto.headset.model.JumpAssessmentGraphData
import com.suunto.headset.model.JumpAssessmentGraphType
import com.suunto.headset.model.JumpAssessmentItemValue
import com.suunto.headset.model.JumpAssessmentPointValue
import com.suunto.headset.ui.components.AssessmentGuideSheet
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.ui.components.FunctionConflictDialog
import com.suunto.headset.ui.components.NeuromuscularFatigueYAxisRenderer
import com.suunto.headset.ui.components.SU07GraphLineChartView
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AssessmentMainScreen(
    graphData: ImmutableList<JumpAssessmentGraphData>,
    baselineValue: JumpAssessmentItemValue,
    unit: MeasurementUnit,
    onAddMeasurement: () -> Unit,
    onReRecordBaseline: () -> Unit,
    onBack: () -> Unit,
    onHelp: () -> Unit,
    loading: Boolean,
    showGuide: Boolean,
    onBuildBaseline: () -> Unit,
    onHideGuide: () -> Unit,
    assetBaseUrl: String,
    swimming: Boolean,
    buildBaselineAnimationUri: Uri,
    userAgent: String,
    modifier: Modifier = Modifier,
    haveBaselineData: Boolean = false,
    onlyHaveBaselineData: Boolean = true
) {
    var helpGuide by rememberSaveable {
        mutableStateOf(false)
    }
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(id = R.string.jump_assessment),
                onBackClick = onBack
            ) {
                IconButton(onClick = {
                    helpGuide = true
                    onHelp.invoke()
                }) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_help),
                        contentDescription = ""
                    )
                }
            }
        },
    ) { paddingValues ->
        ContentCenteringColumn(
            modifier = Modifier
                .padding(paddingValues)
                .background(MaterialTheme.colors.surface),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            var showFunctionConflictDialog by remember {
                mutableStateOf(false)
            }
            if (loading) {
                LoadingContent(isLoading = true)
            } else {
                if (haveBaselineData) {
                    Column {
                        if (onlyHaveBaselineData) {
                            BaselineContent(baselineValue = baselineValue, unit, onReRecordBaseline)
                        } else {
                            var currentGraphData by remember(graphData) {
                                mutableStateOf(
                                    if (graphData.isEmpty()) JumpAssessmentGraphData(
                                        JumpAssessmentGraphType.N_FATIGUE,
                                        persistentListOf()
                                    ) else graphData.first()
                                )
                            }
                            AssessmentGraphContent(
                                currentGraphData = currentGraphData,
                                unit = unit,
                                onSelectGraphType = { graphType ->
                                    graphData.find { it.type == graphType }?.let {
                                        currentGraphData = it
                                    }
                                }
                            )
                        }
                        Text(
                            modifier = Modifier.padding(MaterialTheme.spacing.large),
                            text = stringResource(id = R.string.recommendation_assessment),
                            color = MaterialTheme.colors.darkGrey,
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )

                        Button(
                            modifier = Modifier
                                .padding(horizontal = MaterialTheme.spacing.large)
                                .fillMaxWidth()
                                .height(48.dp), onClick = {
                                if (swimming) {
                                    showFunctionConflictDialog = true
                                } else {
                                    onAddMeasurement.invoke()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Add,
                                contentDescription = "",
                                tint = MaterialTheme.colors.surface
                            )
                            Text(
                                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                                text = stringResource(id = R.string.add_measurement).uppercase(),
                                color = MaterialTheme.colors.nearWhite,
                                style = MaterialTheme.typography.bodyBold
                            )
                        }
                    }
                } else {
                    JumpAssessmentBuildBaselineScreen(
                        onBuildBaseline = {
                            if (swimming) {
                                showFunctionConflictDialog = true
                            } else {
                                onBuildBaseline.invoke()
                            }
                        },
                        animationUri = buildBaselineAnimationUri,
                        R.drawable.ja_landing_page_placeholder,
                        userAgent = userAgent,
                    )
                }
            }
            if (showGuide) {
                AssessmentGuideSheet(
                    guideData = JumpAssessmentHelper.getGuideData(assetBaseUrl),
                    onDismissRequest = onHideGuide,
                    onStartAssessment = onHideGuide,
                    showOperationButton = !helpGuide
                )
            }
            if (showFunctionConflictDialog) {
                FunctionConflictDialog(onDismissRequest = {
                    showFunctionConflictDialog = false
                }, onConfirm = {
                    showFunctionConflictDialog = false
                    onAddMeasurement.invoke()
                })
            }
        }
    }
}

@Composable
private fun AssessmentGraphContent(
    currentGraphData: JumpAssessmentGraphData,
    unit: MeasurementUnit,
    onSelectGraphType: (JumpAssessmentGraphType) -> Unit
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .padding(
                    top = MaterialTheme.spacing.medium,
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium
                )
                .fillMaxWidth()
                .border(
                    MaterialTheme.spacing.xxxsmall,
                    MaterialTheme.colors.lightGrey,
                    RoundedCornerShape(6.dp)
                )
        ) {
            var showGraphTypeMenu by remember {
                mutableStateOf(false)
            }
            Row(
                modifier = Modifier
                    .padding(MaterialTheme.spacing.medium)
                    .height(IntrinsicSize.Min)
                    .fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(3.dp)
                        .background(MaterialTheme.colors.darkestGrey)
                )
                Column(
                    modifier = Modifier
                        .padding(start = MaterialTheme.spacing.small)
                        .weight(1.0f)
                ) {
                    Text(
                        text = stringResource(getGraphTitleRes(currentGraphData.type)),
                        color = MaterialTheme.colors.nearBlack,
                        style = MaterialTheme.typography.bodyBold
                    )
                    Text(
                        text = when (currentGraphData.type) {
                            JumpAssessmentGraphType.N_FATIGUE -> stringResource(id = R.string.fatigue_index)
                            JumpAssessmentGraphType.JUMP_HEIGHT -> stringResource(id = unit.shorterDistanceUnitResId)
                            JumpAssessmentGraphType.FLIGHT_TIME -> stringResource(id = com.stt.android.core.R.string.ms)
                            JumpAssessmentGraphType.TAKEOFF_V -> stringResource(id = unit.shortSpeedUnitResId)
                        },
                        color = MaterialTheme.colors.nearBlack,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                IconButton(
                    modifier = Modifier.size(32.dp),
                    onClick = { showGraphTypeMenu = true }) {
                    Icon(
                        imageVector = Icons.Filled.ArrowDropDown,
                        contentDescription = ""
                    )
                }
            }

            GraphTypeMenu(
                expanded = showGraphTypeMenu,
                onDismissRequest = { showGraphTypeMenu = false },
                onSelectGraphType = {
                    showGraphTypeMenu = false
                    onSelectGraphType.invoke(it)
                },
                modifier = Modifier.background(MaterialTheme.colors.surface)
            )
        }
        ChartView(currentGraphData, unit)
        if (currentGraphData.type == JumpAssessmentGraphType.N_FATIGUE) {
            NeuromuscularFatigueLegend(
                legends = JumpAssessmentHelper.NEUROMUSCULAR_FATIGUE_LEGENDS
            )
        }
    }
}

@Composable
private fun ChartView(currentGraphData: JumpAssessmentGraphData, unit: MeasurementUnit) {
    SU07GraphLineChartView(
        chartTitle = stringResource(id = getGraphTitleRes(currentGraphData.type)),
        data = currentGraphData.data.map {
            Entry(it.time.toFloat(), it.value)
        }.toImmutableList(),
        dataUnit = when (currentGraphData.type) {
            JumpAssessmentGraphType.N_FATIGUE -> ""
            JumpAssessmentGraphType.JUMP_HEIGHT -> stringResource(id = unit.shorterDistanceUnitResId)
            JumpAssessmentGraphType.FLIGHT_TIME -> stringResource(id = com.stt.android.core.R.string.ms)
            JumpAssessmentGraphType.TAKEOFF_V -> stringResource(id = unit.shortSpeedUnitResId)
        },
        limitLineValues = if (currentGraphData.type == JumpAssessmentGraphType.N_FATIGUE)
            persistentListOf()
        else persistentListOf(
            Pair(
                currentGraphData.averageValue,
                colorResource(R.color.near_black).toArgb()
            )
        ),
        circleColors = if (currentGraphData.type == JumpAssessmentGraphType.N_FATIGUE) {
            currentGraphData.data.map {
                JumpAssessmentHelper.getNeuromuscularFatigueLevel(it.value).color.toArgb()
            }.toImmutableList()
        } else {
            persistentListOf(MaterialTheme.colors.nearBlack.toArgb())
        },
        intValue = when (currentGraphData.type) {
            JumpAssessmentGraphType.N_FATIGUE, JumpAssessmentGraphType.JUMP_HEIGHT, JumpAssessmentGraphType.FLIGHT_TIME -> true
            JumpAssessmentGraphType.TAKEOFF_V -> false
        }
    ) { lineChart ->
        lineChart.apply {
            if (currentGraphData.type == JumpAssessmentGraphType.N_FATIGUE) {
                rendererLeftYAxis = NeuromuscularFatigueYAxisRenderer(
                    viewPortHandler,
                    axisLeft,
                    getTransformer(YAxis.AxisDependency.LEFT),
                    listOf(
                        NeuromuscularFatigueLevel.LEAST,
                        NeuromuscularFatigueLevel.LESS,
                        NeuromuscularFatigueLevel.HIGHER,
                        NeuromuscularFatigueLevel.PEAK
                    )
                )
                axisLeft.xOffset = Utils.convertDpToPixel(6f)
                axisLeft.axisMaximum = 2f
                axisLeft.axisMinimum = -2f
            } else {
                rendererLeftYAxis = YAxisRenderer(
                    viewPortHandler,
                    axisLeft,
                    getTransformer(YAxis.AxisDependency.LEFT)
                )
                axisLeft.resetAxisMaximum()
                axisLeft.resetAxisMinimum()
            }
            axisLeft.setLabelCount(5, true)
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun NeuromuscularFatigueLegend(legends: ImmutableList<Pair<androidx.compose.ui.graphics.Color, Int>>) {
    FlowRow(
        modifier = Modifier
            .padding(MaterialTheme.spacing.medium)
            .fillMaxWidth()
    ) {
        legends.forEach {
            Row(
                modifier = Modifier.padding(end = MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(it.first)
                )
                Text(
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.xsmall,
                        top = MaterialTheme.spacing.xsmall
                    ),
                    text = stringResource(id = it.second),
                    color = MaterialTheme.colors.nearBlack,
                    style = MaterialTheme.typography.body
                )
            }
        }
    }
}

@Composable
private fun GraphTypeMenu(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    onSelectGraphType: (JumpAssessmentGraphType) -> Unit,
    modifier: Modifier = Modifier
) {
    DropdownMenu(expanded = expanded, onDismissRequest = onDismissRequest, modifier = modifier) {
        JumpAssessmentGraphType.entries.forEach {
            DropdownMenuItem(text = {
                Text(
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    text = stringResource(id = getGraphTitleRes(it)),
                    color = MaterialTheme.colors.nearBlack,
                    style = MaterialTheme.typography.bodyLarge
                )
            }, onClick = { onSelectGraphType.invoke(it) })
        }
    }
}

private fun getGraphTitleRes(type: JumpAssessmentGraphType): Int {
    return when (type) {
        JumpAssessmentGraphType.N_FATIGUE -> R.string.neuromuscular_fatigue
        JumpAssessmentGraphType.JUMP_HEIGHT -> R.string.jump_height
        JumpAssessmentGraphType.FLIGHT_TIME -> R.string.flight_time
        JumpAssessmentGraphType.TAKEOFF_V -> R.string.takeoff_v
    }
}

@Composable
private fun BaselineContent(
    baselineValue: JumpAssessmentItemValue,
    unit: MeasurementUnit,
    onReRecordBaseline: () -> Unit
) {
    Column(
        modifier = Modifier
            .background(MaterialTheme.colors.surface)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.fillMaxWidth().aspectRatio(1124f  / 1120f),
            painter = painterResource(id = R.drawable.ja_introduction1),
            contentDescription = "",
            contentScale = ContentScale.FillBounds
        )
        var showReRecordBaseline by rememberSaveable {
            mutableStateOf(false)
        }
        Row(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(id = R.string.your_baseline),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLargeBold
            )
            Box(modifier = Modifier) {
                IconButton(onClick = { showReRecordBaseline = true }) {
                    Icon(imageVector = Icons.Filled.MoreVert, contentDescription = "")
                }
                DropdownMenu(
                    expanded = showReRecordBaseline,
                    onDismissRequest = { showReRecordBaseline = false },
                    modifier = Modifier.background(MaterialTheme.colors.surface)
                ) {
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = stringResource(id = R.string.re_record_baseline),
                                color = MaterialTheme.colors.nearBlack,
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.padding(MaterialTheme.spacing.medium)
                            )
                        },
                        onClick = {
                            showReRecordBaseline = false
                            onReRecordBaseline.invoke()
                        })
                }
            }
        }
        Divider(color = MaterialTheme.colors.lightGrey)
        AssessmentSummary(
            averageHeight = baselineValue.jumpHeightInCm,
            flightTime = baselineValue.flightTimeInMs,
            takeoffVelocity = baselineValue.takeoffVelocityInMeterPerSecond,
            unit = unit
        )

    }
}

@Composable
@Preview
private fun AssessmentMainScreenPreview() {
    AssessmentMainScreen(
        graphData = persistentListOf(
            JumpAssessmentGraphData(
                JumpAssessmentGraphType.N_FATIGUE,
                persistentListOf(
                    JumpAssessmentPointValue(1718622985000, -1f),
                    JumpAssessmentPointValue(1718709385000, 1f),
                    JumpAssessmentPointValue(1718795785000, 0f),
                    JumpAssessmentPointValue(1718882185000, -1.5f)
                ),
            )
        ),
        baselineValue = JumpAssessmentItemValue(
            jumpHeightInCm = 40f,
            flightTimeInMs = 450,
            takeoffVelocityInMeterPerSecond = 1.7f
        ),
        unit = MeasurementUnit.METRIC,
        onAddMeasurement = {},
        onReRecordBaseline = {},
        onBack = {},
        onHelp = {},
        loading = false,
        showGuide = false,
        onBuildBaseline = {},
        onHideGuide = {},
        assetBaseUrl = "",
        swimming = false,
        buildBaselineAnimationUri = Uri.EMPTY,
        haveBaselineData = true,
        onlyHaveBaselineData = true,
        userAgent = "",
    )
}
