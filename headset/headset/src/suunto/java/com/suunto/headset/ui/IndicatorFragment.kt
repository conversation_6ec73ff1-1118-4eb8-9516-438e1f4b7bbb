package com.suunto.headset.ui

import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.compose.theme.M3AppTheme
import com.suunto.extension.lazyArgument
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.IndicatorContract
import com.suunto.headset.databinding.FragmentIndicatorBinding
import com.suunto.headset.model.toSoaSportMode
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.ui.components.ledlights.LEDLightSettingScreen
import com.suunto.headset.viewmodel.IndicatorViewModel
import com.suunto.soa.ble.control.attr.SportsMode
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.reflect.KClass

/**
 *  The headset indicator UI when using it for the first time.
 */
@AndroidEntryPoint
class IndicatorFragment :
    BaseStateFragment<IndicatorContract.UIIntent, IndicatorContract.UIState, IndicatorViewModel>() {

    private val binding by viewBinding(FragmentIndicatorBinding::bind, R.id.layout_indicator)
    private val currentLedLightMode by lazyArgument<SportsMode>(INDICATOR_STATUS)

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    override fun getLayoutResId(): Int {
        return R.layout.fragment_indicator
    }

    override val viewModelClass: KClass<IndicatorViewModel>
        get() = IndicatorViewModel::class

    override fun getTitleResId(): Int {
        return R.string.option_indicator_title
    }

    override fun appbarElevationEnabled(): Boolean = false

    override fun initView() {
        binding.composeView.setContent {
            M3AppTheme {
                LEDLightSettingScreen(
                    viewModel = viewModel,
                    onLedLightModeTypeChange = { mode ->
                        IndicatorContract.UIIntent.SetSportsMode(mode.toSoaSportMode()).sendIntent()
                    },
                    onLedLightModeStateChange = { modeState ->
                        IndicatorContract.UIIntent.SportsModeSwitchState(modeState)
                            .sendIntent()
                    })
            }
        }
        handleObserveConnectState()
        IndicatorContract.UIIntent.LoadSportsMode(currentLedLightMode).sendIntent()
    }
}

const val INDICATOR_STATUS = "INDICATOR_STATUS"
