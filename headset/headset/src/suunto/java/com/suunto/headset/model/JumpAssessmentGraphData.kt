package com.suunto.headset.model

import kotlinx.collections.immutable.ImmutableList

data class JumpAssessmentGraphData(
    val type: JumpAssessmentGraphType,
    val data: ImmutableList<JumpAssessmentPointValue>,
    val averageValue: Float = 0f
)

data class JumpAssessmentPointValue(val time: Long, val value: Float)
enum class JumpAssessmentGraphType {
    N_FATIGUE, JUMP_HEIGHT, FLIGHT_TIME, TAKEOFF_V
}
