package com.suunto.headset.model

import com.suunto.headset.R

data class OwsSoundModeSetting(
    val soundMode: OwsSoundMode,
    val selected: Boolean = false
)

enum class OwsSoundMode(val nameRes: Int) {
    LEGENDARY(R.string.sound_mode_legendary),
    BASS_BOOST(R.string.sound_mode_bass_boost),
    TREBLE_BOOST(R.string.sound_mode_treble_boost),
    VOCAL_BOOST(R.string.sound_mode_vocal_boost),
    CUSTOMIZATION(R.string.sound_mode_customization),
}

data class EqualizerSetting(
    val band: EqualizerFrequencyBand,
    val gain: Int = 0 // Gain in dB, typically -12 to +12
)

enum class EqualizerFrequencyBand(
    val nameRes: Int
) {
    BAND_LOW(R.string.equalizer_low_frequency),
    BAND_LOW_MID(R.string.equalizer_low_mid_frequency),
    BAND_MID(R.string.equalizer_mid_frequency),
    BAND_HIGH_MID(R.string.equalizer_high_mid_frequency),
    BAND_HIGH(R.string.equalizer_high_frequency),
}
