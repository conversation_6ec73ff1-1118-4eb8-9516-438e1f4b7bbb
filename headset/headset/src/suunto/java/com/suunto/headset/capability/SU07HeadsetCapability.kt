package com.suunto.headset.capability

import com.suunto.headset.R
import com.suunto.headset.model.ButtonType
import com.suunto.headset.model.DeviceCapability
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.model.SportSupports
import com.suunto.headset.model.SportType
import com.suunto.headset.ui.HeadsetDeviceType
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.headset.ui.onboarding.SU07OnBoardingPages
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.ButtonFunction
import com.suunto.soa.ble.response.ButtonShortcutKey

class SU07HeadsetCapability : BaseHeadsetCapability() {
    override val headsetDeviceType: HeadsetDeviceType = HeadsetDeviceType.SU07

    override fun supportEqMode(): Boolean = true

    override fun supportBodySense(): Boolean = true

    override fun supportDualDeviceConnect(): Boolean = true

    override fun supportGetSerial(): Boolean = false

    override fun supportLowLatency(): Boolean = false

    override fun supportGetConnectedDevices(): Boolean = true

    override fun supportLEDLightMode(): Boolean = false

    override fun supportLanguageSetting(): Boolean = true

    override fun supportNotifyBatteryChanged(): Boolean = true

    override fun supportOfflineMusic(): Boolean = true

    override fun supportButtonCustomization(): Boolean = true

    override fun supportSwimFeature(): Boolean = true

    override fun getFunctionIntroduction(): List<HeadsetOnBoardingPage> {
        return SU07OnBoardingPages
    }

    override fun getUserGuideUrl(): Int {
        return R.string.user_guide_url_su07
    }

    override fun getMoreInformationUrl(): Int {
        return R.string.more_information_url_su07
    }

    override fun getSportsModeList(): List<SportsMode> {
        return mutableListOf(SportsMode.CLOSE, SportsMode.TEAM, SportsMode.LEADER, SportsMode.SOS)
    }

    override fun getSoundModeNormalPhotoResIds(): Int {
        // not used
        return 0
    }

    override fun getSoundModeOutdoorPhotoResIds(): Int {
        // not used
        return 0
    }

    override fun getSoundModeList(): List<EqMode> {
        return listOf(EqMode.NORMAL, EqMode.OUTDOOR, EqMode.UNDERWATER)
    }

    override fun getButtonList(): List<HeadsetButton> {
        return getSU07ButtonFunctions().map { function ->
            HeadsetButton(
                buttonType = getHeadsetButtonType(function),
                buttonFunction = function,
                buttonShortcutKey = getHeadsetButtonDefaultShortcutKey(function),
                isSupportCustomized = isSupportCustomized(function)
            )
        }
    }

    override fun getCustomizationShortcutKeyList(): List<ButtonShortcutKey> {
        return ButtonShortcutKey.entries
            .filterNot {
                it == ButtonShortcutKey.EMPTY
                    || it == ButtonShortcutKey.HOLD_DOWN_PLUS_3S
                    || it == ButtonShortcutKey.PRESS_PLUS_OR_REDUCE
                    || it == ButtonShortcutKey.HOLD_DOWN_MFB_AND_PLUS_3S
                    || it == ButtonShortcutKey.PRESS_MFB_ONCE
                    || it == ButtonShortcutKey.PRESS_AND_HOLD_MFB
                    || it == ButtonShortcutKey.HOLD_DOWN_MFB_5S
            } + ButtonShortcutKey.EMPTY
    }

    override fun isCheckDeviceAvailableBeforeOTA(): Boolean = true

    override fun getHeadsetButtonType(function: ButtonFunction): ButtonType {
        return when (function) {
            ButtonFunction.ACTIVE_VOICE_ASSISTANT,
            ButtonFunction.HEAD_MOVE_CONTROL,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF -> ButtonType.ANY_CIRCUMSTANCES

            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE -> ButtonType.WHILE_PLAYING_MUSIC

            ButtonFunction.POWER_ON_OFF,
            ButtonFunction.VOLUME_ADJUSTMENT,
            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.PHONE_CALL_CONTROL,
            ButtonFunction.REJECT_CALL,
            ButtonFunction.DUAL_DEVICE_CONNECTION,
            ButtonFunction.REST_SETTING -> ButtonType.NON_CUSTOMIZABLE

            ButtonFunction.ENABLE_SPORT_TRACKING,
            ButtonFunction.START_BLE_PAIR,
            ButtonFunction.LED_MODE_ON_OFF,
            ButtonFunction.EMPTY -> ButtonType.INVALID
        }
    }

    private fun getHeadsetButtonDefaultShortcutKey(function: ButtonFunction): ButtonShortcutKey {
        return when (function) {
            ButtonFunction.NEXT_SONG -> ButtonShortcutKey.DOUBLE_PRESS_MFB
            ButtonFunction.LAST_SONG -> ButtonShortcutKey.TRIPE_PRESS_MFB
            ButtonFunction.HEAD_MOVE_CONTROL -> ButtonShortcutKey.HOLD_DOWN_MFB_AND_REDUCE_3S
            ButtonFunction.SWITCH_MUSIC_MODE -> ButtonShortcutKey.HOLD_DOWN_REDUCE_3S
            ButtonFunction.ACTIVE_VOICE_ASSISTANT -> ButtonShortcutKey.HOLD_DOWN_MFB_3S
            ButtonFunction.POWER_ON_OFF -> ButtonShortcutKey.HOLD_DOWN_PLUS_3S
            ButtonFunction.VOLUME_ADJUSTMENT -> ButtonShortcutKey.PRESS_PLUS_OR_REDUCE
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.DUAL_DEVICE_CONNECTION -> ButtonShortcutKey.HOLD_DOWN_MFB_AND_PLUS_3S

            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.PHONE_CALL_CONTROL -> ButtonShortcutKey.PRESS_MFB_ONCE

            ButtonFunction.REJECT_CALL -> ButtonShortcutKey.PRESS_AND_HOLD_MFB
            ButtonFunction.REST_SETTING -> ButtonShortcutKey.HOLD_DOWN_MFB_5S

            ButtonFunction.ENABLE_SPORT_TRACKING -> ButtonShortcutKey.HOLD_DOWN_PLUS_AND_REDUCE_3S

            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF,
            ButtonFunction.START_BLE_PAIR,
            ButtonFunction.LED_MODE_ON_OFF,
            ButtonFunction.EMPTY -> ButtonShortcutKey.EMPTY
        }
    }

    private fun isSupportCustomized(function: ButtonFunction): Boolean {
        return when (function) {
            ButtonFunction.ACTIVE_VOICE_ASSISTANT,
            ButtonFunction.HEAD_MOVE_CONTROL,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF,
            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE,
            ButtonFunction.ENABLE_SPORT_TRACKING -> true

            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.POWER_ON_OFF,
            ButtonFunction.VOLUME_ADJUSTMENT,
            ButtonFunction.DUAL_DEVICE_CONNECTION,
            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.PHONE_CALL_CONTROL,
            ButtonFunction.REJECT_CALL,
            ButtonFunction.REST_SETTING,
            ButtonFunction.START_BLE_PAIR,
            ButtonFunction.LED_MODE_ON_OFF,
            ButtonFunction.EMPTY -> false
        }
    }

    private fun getSU07ButtonFunctions(): List<ButtonFunction> {
        return listOf(
            ButtonFunction.POWER_ON_OFF,
            ButtonFunction.VOLUME_ADJUSTMENT,
            ButtonFunction.ACTIVE_VOICE_ASSISTANT,
            ButtonFunction.HEAD_MOVE_CONTROL,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF,
            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE,
            ButtonFunction.PHONE_CALL_CONTROL,
            ButtonFunction.REJECT_CALL,
            ButtonFunction.DUAL_DEVICE_CONNECTION,
            ButtonFunction.REST_SETTING,
            ButtonFunction.ENABLE_SPORT_TRACKING,
        )
    }

    override fun getSportTypes(deviceCapability: DeviceCapability): List<SportType> {
        val sportTypes = mutableListOf<SportType>()
        if (supportsPoolSwimSport(deviceCapability)) {
            sportTypes.add(SportType.POOL_SWIMMING)
        }
        sportTypes.add(SportType.OPEN_WATER_SWIMMING)
        if (supportJumpRopeFeature(deviceCapability)) {
            sportTypes.add(SportType.JUMP_ROPE)
        }
        if (supportRunningFeature(deviceCapability)) {
            sportTypes.add(SportType.RUNNING)
        }
        return sportTypes
    }

    override fun getDefaultSportType(): SportType {
        return SportType.OPEN_WATER_SWIMMING
    }

    override fun supportNeckMovementAssessment(): Boolean = true

    override fun supportMusicModeFeature(): Boolean = true

    override fun supportCallStatusFeature(): Boolean = true

    override fun supportJumpAssessment(): Boolean = true

    override fun getHeadControlInstructions(): List<HeadControlInstruction> {
        return listOf(
            HeadControlInstruction(
                R.string.head_control_instructions_call, listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_nod,
                        R.string.head_control_item_answer_call_title,
                        R.string.head_control_item_answer_call_content
                    ),
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_answer_reject_title,
                        R.string.head_control_item_shake_head
                    )
                )
            ),
            HeadControlInstruction(
                R.string.head_control_instructions_media, listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_skip_song_title,
                        R.string.head_control_item_shake_head
                    )
                )
            )
        )
    }

    override fun supportJumpRopeFeature(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.jumpRopeSportSupported
    }

    override fun supportRunningFeature(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.runningSportSupported
    }

    override fun getSportSupports(deviceCapability: DeviceCapability): SportSupports {
        val supportSwim = supportSwimFeature()
        val supportJumpRope = supportJumpRopeFeature(deviceCapability)
        val supportRunning = supportRunningFeature(deviceCapability)
        return SportSupports(
            isSwimSupported = supportSwim,
            isJumpRopeSupported = supportJumpRope,
            isRunningSupported = supportRunning,
            poolSwimmingSupported = supportsPoolSwimSport(deviceCapability)
        )
    }

    override fun supportSportButtonModify(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.sportButtonCustomizedSupported
    }

    override fun supportSportsSwitch(deviceCapability: DeviceCapability): Boolean {
        val supportSwim = supportSwimFeature()
        val supportJumpRope = supportJumpRopeFeature(deviceCapability)
        val supportRunning = supportRunningFeature(deviceCapability)
        return supportSwim || supportJumpRope || supportRunning
    }

    override fun supportsUserInfoSetting(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.userInfoSettingSupported
    }

    override fun supportsPoolSwimSport(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.poolSwimSportSupported
    }

    override fun supportsPoolDistanceSetting(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.poolSwimDistanceSupported
    }
}
