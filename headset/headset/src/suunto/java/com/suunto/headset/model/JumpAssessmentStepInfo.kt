package com.suunto.headset.model

import android.net.Uri
import com.stt.android.domain.user.MeasurementUnit

data class JumpAssessmentStepInfo(
    val jumpStep: JumpStep,
    val assessmentState: NeckMobilityAssessmentState? = null,
    // unit: cm
    val assessmentValueInCm: Float = 0f,
    val measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    val animationUri: Uri = Uri.EMPTY,
    val placeholderResId: Int = 0
)

enum class JumpStep(val index: Int) {
    FIRST(1), SECOND(2), LAST(3)
}
