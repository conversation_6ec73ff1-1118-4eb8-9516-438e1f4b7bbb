package com.suunto.soa.bluetooth.ota.client

import android.bluetooth.BluetoothDevice
import com.suunto.soa.bluetooth.ota.service.OtaState
import com.suunto.soa.bluetooth.ota.service.UpgradeInfo
import kotlinx.coroutines.flow.Flow
import java.io.File

interface OtaFeatures {

    suspend fun initialization()

    suspend fun destroy()

    suspend fun transferFirmware(device: BluetoothDevice, otaFile: File, upgradeInfo: UpgradeInfo)

    suspend fun getOtaStateFlow(): Flow<OtaState>

    fun isInTransferring(): Boolean

    fun isInCheckingBin(): Boolean

    fun isInProgress(): Boolean

    fun getLogDir(): File
}
