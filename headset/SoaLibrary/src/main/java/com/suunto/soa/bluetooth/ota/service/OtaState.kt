package com.suunto.soa.bluetooth.ota.service

sealed class OtaState {

    // OTA finished and disconnect connection
    object None : OtaState()

    // Ready to start, read file and connect bt
    data object Preparing : OtaState()

    // Bt has been connected
    data object Connected : OtaState()

    // Earplugs upgrade allowed
    data object UpgradeAllowed : OtaState()

    // Ota file is transferring
    class Transferring(val transferProgress: TransferProgress) : OtaState()

    // Upgrade transfer file failure
    class Failed(val thr: Throwable, val logPath: String? = null) : OtaState()

    data object BatteryLower : OtaState()

    data object PhoneCalling : OtaState()

    data object OfflineMusicPlaying : OtaState()

    data object MusicScanning : OtaState()

    // Upgrade transfer file success
    data object Succeed : OtaState()
}

fun isOtaStateUnAvailable(otaState: OtaState): Boolean {
    return otaState is OtaState.Failed ||
        otaState is OtaState.BatteryLower ||
        otaState is OtaState.PhoneCalling ||
        otaState is OtaState.OfflineMusicPlaying ||
        otaState is OtaState.MusicScanning
}
