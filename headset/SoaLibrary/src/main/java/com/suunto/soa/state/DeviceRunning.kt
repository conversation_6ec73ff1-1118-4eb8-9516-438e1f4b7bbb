package com.suunto.soa.state

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DeviceRunning @Inject constructor() {

    private val runningState = MutableStateFlow<RunningState>(RunningState.Unknown)

    internal fun setRunningState(state: RunningState) {
        runningState.value = state
    }

    fun getRunningStateFlow() = runningState.asStateFlow()

    fun getRunningState() = runningState.value
}
