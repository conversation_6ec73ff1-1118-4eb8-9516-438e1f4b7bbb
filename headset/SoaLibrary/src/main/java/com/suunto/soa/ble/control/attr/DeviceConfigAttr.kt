package com.suunto.soa.ble.control.attr

import com.suunto.soa.ble.response.CustomizationButton
import com.suunto.soa.data.SoaPoolDistance
import com.suunto.soa.to2Bytes
import java.nio.ByteBuffer

enum class DeviceConfigAttr(val configType: ByteArray) {

    CUSTOMIZATION_BUTTON(byteArrayOf(0x00, 0x10)),

    NECK_JUMP_SWIM_SETTING(byteArrayOf(0x00, 0x11)),

    APP_LANGUAGE_SETTING(byteArrayOf(0x00, 0x12)),

    SPORT_TYPE_SETTING(byteArrayOf(0x00, 0x13)),

    POOL_DISTANCE_SETTING(byteArrayOf(0x00, 0x14)),
}

enum class AppLanguageAttr(val languageConfig: ByteArray) {

    ZH(byteArrayOf(0x00)),

    ENGLISH(byteArrayOf(0x01))
}

enum class NeckSettingAttr(val neckConfig: ByteArray) {

    INIT_CHECK_START(byteArrayOf(0x01, 0x00, 0x01)),

    INIT_CHECK_STOP(byteArrayOf(0x01, 0x00, 0x00)),

    HORIZONTAL_ROTATION_LEFT_START(byteArrayOf(0x01, 0x01, 0x01)),

    HORIZONTAL_ROTATION_LEFT_STOP(byteArrayOf(0x01, 0x01, 0x00)),

    HORIZONTAL_ROTATION_RIGHT_START(byteArrayOf(0x01, 0x02, 0x01)),

    HORIZONTAL_ROTATION_RIGHT_STOP(byteArrayOf(0x01, 0x02, 0x00)),

    FORWARD_TILT_START(byteArrayOf(0x01, 0x03, 0x01)),

    FORWARD_TILT_STOP(byteArrayOf(0x01, 0x03, 0x00)),

    BACKWARD_TILT_START(byteArrayOf(0x01, 0x04, 0x01)),

    BACKWARD_TILT_STOP(byteArrayOf(0x01, 0x04, 0x00)),

    LATERAL_BENDING_LEFT_START(byteArrayOf(0x01, 0x05, 0x01)),

    LATERAL_BENDING_LEFT_STOP(byteArrayOf(0x01, 0x05, 0x00)),

    LATERAL_BENDING_RIGHT_START(byteArrayOf(0x01, 0x06, 0x01)),

    LATERAL_BENDING_RIGHT_STOP(byteArrayOf(0x01, 0x06, 0x00))
}

enum class JumpSettingAttr(val jumpConfig: ByteArray) {

    JUMP_FIRST_START(byteArrayOf(0x02, 0x01, 0x01)),

    JUMP_FIRST_STOP(byteArrayOf(0x02, 0x01, 0x00)),

    JUMP_SECOND_START(byteArrayOf(0x02, 0x02, 0x01)),

    JUMP_SECOND_STOP(byteArrayOf(0x02, 0x02, 0x00)),

    JUMP_THIRD_START(byteArrayOf(0x02, 0x03, 0x01)),

    JUMP_THIRD_STOP(byteArrayOf(0x02, 0x03, 0x00))
}

enum class SoaSportType(val type: Int) {
    OPEN_WATER_SWIMMING(0x01),
    JUMP_ROPE(0x02),
    RUNNING(0x03),
    POOL_SWIMMING(0x04),
}

fun getCustomizationButtonRequestData(
    isSync: Boolean = false,
    buttons: List<CustomizationButton> = emptyList()
): ByteArray {
    val configTypeData = DeviceConfigAttr.CUSTOMIZATION_BUTTON.configType
    if (isSync) {
        return configTypeData
    }
    if (buttons.isEmpty()) throw RuntimeException("custom buttons is empty")
    val configTypeLength = configTypeData.size
    val configValueLength = buttons.size * 2
    val configLength = configTypeLength + configValueLength
    val configByteBuffer = ByteBuffer.allocate(1 + configLength)
    configByteBuffer.put(configLength.toByte())
    configByteBuffer.put(configTypeData)
    buttons.forEach { button ->
        button.getButtonFunctionCode()?.let {
            val configValue =
                byteArrayOf(button.getButtonShortKeyCode().toByte(), it.toByte())
            configByteBuffer.put(configValue)
        }
    }
    return configByteBuffer.array()
}

fun getAppLanguageRequestData(appLanguage: String): ByteArray {
    val attr = when (appLanguage) {
        "ZH" -> AppLanguageAttr.ZH
        else -> AppLanguageAttr.ENGLISH
    }
    val configTypeData = DeviceConfigAttr.APP_LANGUAGE_SETTING.configType
    val configTypeLength = configTypeData.size
    val configValueLength = attr.languageConfig.size
    val configLength = configTypeLength + configValueLength
    val neckSettingBuffer = ByteBuffer.allocate(1 + configLength)
    neckSettingBuffer.put(configLength.toByte())
    neckSettingBuffer.put(configTypeData)
    neckSettingBuffer.put(attr.languageConfig)
    return neckSettingBuffer.array()
}

fun getNeckSettingRequestData(attr: NeckSettingAttr): ByteArray {
    val configTypeData = DeviceConfigAttr.NECK_JUMP_SWIM_SETTING.configType
    val configTypeLength = configTypeData.size
    val configValueLength = attr.neckConfig.size
    val configLength = configTypeLength + configValueLength
    val neckSettingBuffer = ByteBuffer.allocate(1 + configLength)
    neckSettingBuffer.put(configLength.toByte())
    neckSettingBuffer.put(configTypeData)
    neckSettingBuffer.put(attr.neckConfig)
    return neckSettingBuffer.array()
}

fun getJumpSettingRequestData(attr: JumpSettingAttr): ByteArray {
    val configTypeData = DeviceConfigAttr.NECK_JUMP_SWIM_SETTING.configType
    val configTypeLength = configTypeData.size
    val configValueLength = attr.jumpConfig.size
    val configLength = configTypeLength + configValueLength
    val jumpSettingBuffer = ByteBuffer.allocate(1 + configLength)
    jumpSettingBuffer.put(configLength.toByte())
    jumpSettingBuffer.put(configTypeData)
    jumpSettingBuffer.put(attr.jumpConfig)
    return jumpSettingBuffer.array()
}

fun getSportTypeSettingSyncData(): ByteArray {
    return DeviceConfigAttr.SPORT_TYPE_SETTING.configType
}

fun getSportTypeSettingRequestData(soaSportType: SoaSportType): ByteArray {
    val sportTypeData = DeviceConfigAttr.SPORT_TYPE_SETTING.configType
    val configLength = sportTypeData.size + 1
    val sportTypeSettingBuffer = ByteBuffer.allocate(1 + configLength)
    sportTypeSettingBuffer.put(configLength.toByte())
    sportTypeSettingBuffer.put(sportTypeData)
    sportTypeSettingBuffer.put(soaSportType.type.toByte())
    return sportTypeSettingBuffer.array()
}

fun getPoolDistanceSettingSyncData(): ByteArray {
    return DeviceConfigAttr.POOL_DISTANCE_SETTING.configType
}

fun getPoolDistanceSettingRequestData(poolDistance: SoaPoolDistance): ByteArray {
    val configTypeData = DeviceConfigAttr.POOL_DISTANCE_SETTING.configType
    val poolDistanceInMetric = poolDistance.distanceInMetric.to2Bytes()
    val poolDistanceInImperial = poolDistance.distanceInImperial.to2Bytes()
    val poolDistanceUnit = poolDistance.distanceUnit.value.toByte()
    val configLength = configTypeData.size + poolDistanceInMetric.size + poolDistanceInImperial.size + 1
    val poolDistanceSettingBuffer = ByteBuffer.allocate(1 + configLength)
    poolDistanceSettingBuffer.put(configLength.toByte())
    poolDistanceSettingBuffer.put(configTypeData)
    poolDistanceSettingBuffer.put(poolDistanceInMetric)
    poolDistanceSettingBuffer.put(poolDistanceInImperial)
    poolDistanceSettingBuffer.put(poolDistanceUnit)
    return poolDistanceSettingBuffer.array()
}
