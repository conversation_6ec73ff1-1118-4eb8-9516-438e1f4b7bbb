package com.suunto.soa.bluetooth.ota.response

import com.suunto.soa.command.Response
import com.suunto.soa.intAtOffset

class EnterUpdateModeResponse(bytesInfo: ByteArray) : Response<EnterUpdateMode>(bytesInfo) {

    override val data: EnterUpdateMode = EnterUpdateMode(dataBytes)
}

class EnterUpdateMode(data: ByteArray) {

    val isInUpdateMode: Boolean
    val offset: Int
    val offsetDataLength: Int
    val crc32Code: Boolean

    init {
        isInUpdateMode = data[0] == 0x00.toByte()
        if (isInUpdateMode) {
            offset = data.intAtOffset(1, 4)
            offsetDataLength = data.intAtOffset(5, 2)
            crc32Code = data[7] != 0x00.toByte()
        } else {
            offset = 0
            offsetDataLength = 0
            crc32Code = false
        }
    }
}
