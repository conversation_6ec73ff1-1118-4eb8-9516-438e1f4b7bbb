package com.stt.android.eventtracking.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.stt.android.eventtracking.database.entity.LocalKeyValue
import kotlinx.coroutines.flow.Flow

@Dao
internal interface KeyValueDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(keyValue: LocalKeyValue)

    @Query("SELECT * FROM ${LocalKeyValue.TABLE_NAME} WHERE ${LocalKeyValue.COLUMN_KEY} = :key LIMIT 1")
    fun keyValueUpdates(key: String): Flow<LocalKeyValue?>
}
