package com.stt.android.diary.summary

import android.content.SharedPreferences
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import androidx.core.content.edit
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.di.TrainingZoneSummaryPreferences
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.primaryGraphDataTypes
import com.stt.android.domain.diary.models.secondaryGraphDataTypes
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.channels.trySendBlocking
import kotlinx.coroutines.flow.callbackFlow
import timber.log.Timber
import javax.inject.Inject

class TrainingZoneSummarySelectionsRepository @Inject constructor(
    @TrainingZoneSummaryPreferences private val prefs: SharedPreferences,
    moshi: Moshi
) {
    companion object {
        private const val KEY_DEFAULT_FILTER_GROUPING = "KEY_DEFAULT_FILTER_GROUPING"
        private const val KEY_DEFAULT_FILTER_SPORTS = "KEY_DEFAULT_FILTER_SPORTS"
        private const val KEY_DEFAULT_FILTER_START_DATE = "KEY_DEFAULT_FILTER_START_DATE"
        private const val KEY_DEFAULT_FILTER_END_DATE = "KEY_DEFAULT_FILTER_END_DATE"
        private const val KEY_DEFAULT_FILTER_MIN_DISTANCE = "KEY_DEFAULT_FILTER_MIN_DISTANCE"
        private const val KEY_DEFAULT_FILTER_MAX_DISTANCE = "KEY_DEFAULT_FILTER_MAX_DISTANCE"
        private const val KEY_DEFAULT_FILTER_SUUNTO_TAGS = "KEY_DEFAULT_FILTER_SUUNTO_TAGS"
        private const val KEY_DEFAULT_FILTER_USER_TAGS = "KEY_DEFAULT_FILTER_USER_TAGS"
        private const val KEY_DEFAULT_FILTER_RECENT_TAGS = "KEY_DEFAULT_FILTER_RECENT_TAGS"
        private const val KEY_DEFAULT_SHOW_EMPTY_ROWS_CHECKED =
            "KEY_DEFAULT_SHOW_EMPTY_ROWS_CHECKED"
        private const val KEY_DEFAULT_FILTER_LAYOUT_TYPE = "KEY_DEFAULT_FILTER_LAYOUT_TYPE"
        private const val KEY_DEFAULT_PRIMARY_GRAPH_DATA_TYPE =
            "KEY_DEFAULT_PRIMARY_GRAPH_DATA_TYPE"
        private const val KEY_DEFAULT_SECONDARY_GRAPH_DATA_TYPE =
            "KEY_DEFAULT_SECONDARY_GRAPH_DATA_TYPE"

        private const val KEY_DEFAULT_SORTING_COLUMN = "KEY_DEFAULT_SORTING_COLUMN"
        private const val KEY_DEFAULT_SORTING_ORDER = "KEY_DEFAULT_SORTING_ORDER"

        private const val KEY_HIDDEN_SUMMARY_COLUMNS = "KEY_HIDDEN_SUMMARY_COLUMNS"
        private const val KEY_SORTED_SUMMARY_COLUMNS = "KEY_SORTED_SUMMARY_COLUMNS"

        private const val KEY_DEFAULT_TRAINING_GRAPH_TIME_RANGE =
            "KEY_DEFAULT_TRAINING_GRAPH_TIME_RANGE"
        private const val KEY_DEFAULT_PRIMARY_TRAINING_GRAPH_TYPE =
            "KEY_DEFAULT_PRIMARY_TRAINING_GRAPH_TYPE"
        private const val KEY_DEFAULT_SECONDARY_TRAINING_GRAPH_TYPE =
            "KEY_DEFAULT_SECONDARY_TRAINING_GRAPH_TYPE"

        val DEFAULT_FILTER_GROUPING = TrainingZoneSummaryGrouping.WEEKLY
        val DEFAULT_FILTER_SPORTS = emptyList<CoreActivityType>()
        val DEFAULT_FILTER_START_DATE: Long? = null
        val DEFAULT_FILTER_END_DATE: Long? = null
        val DEFAULT_FILTER_DISTANCE: DistanceUiState = DistanceUiState(
            minDistance = DistanceUiState.RangeValue.None,
            maxDistance = DistanceUiState.RangeValue.None
        )

        val DEFAULT_FILTER_SUMMARY_SUUNTO_TAGS = emptyList<SummaryTag.SummarySuuntoTag>()
        val DEFAULT_FILTER_SUMMARY_USER_TAGS = emptyList<SummaryTag.SummaryUserTag>()
        val DEFAULT_SHOW_EMPTY_ROWS_CHECKED = true
        val DEFAULT_LAYOUT_TYPE = TrainingZoneSummaryLayoutType.TABLE

        val DEFAULT_SORTING_COLUMN = TrainingZoneSummaryColumn.DATE
        val DEFAULT_SORTING_ORDER = TrainingZoneSummarySortingOrder.DESCENDING

        val DEFAULT_PRIMARY_GRAPH_DATA_TYPE = DiaryPage.SUMMARY.primaryGraphDataTypes.first()
        val DEFAULT_SECONDARY_GRAPH_DATA_TYPE = DiaryPage.SUMMARY.secondaryGraphDataTypes.first()
    }

    private val summarySuuntoTagsJsonAdapter = moshi.adapter<List<SummaryTag.SummarySuuntoTag>>(
        Types.newParameterizedType(
            List::class.java,
            SummaryTag.SummarySuuntoTag::class.java
        )
    )

    private val summaryUserTagsJsonAdapter = moshi.adapter<List<SummaryTag.SummaryUserTag>>(
        Types.newParameterizedType(
            List::class.java,
            SummaryTag.SummaryUserTag::class.java
        )
    )

    private val summaryTagsJsonAdapter = moshi
        .newBuilder()
        .add(SummaryTag.jsonAdapterFactory)
        .build()
        .adapter<List<SummaryTag>>(
            Types.newParameterizedType(
                List::class.java,
                SummaryTag::class.java
            )
        )

    private val summaryColumnListJsonAdapter = moshi.adapter<List<TrainingZoneSummaryColumn>>(
        Types.newParameterizedType(List::class.java, TrainingZoneSummaryColumn::class.java)
    )

    fun resetSelections() {
        defaultGrouping = DEFAULT_FILTER_GROUPING
        defaultSports = DEFAULT_FILTER_SPORTS
        defaultStartDate = DEFAULT_FILTER_START_DATE
        defaultEndDate = DEFAULT_FILTER_END_DATE
        defaultDistance = DEFAULT_FILTER_DISTANCE
        defaultSummarySuuntoTags = DEFAULT_FILTER_SUMMARY_SUUNTO_TAGS
        defaultSummaryUserTags = DEFAULT_FILTER_SUMMARY_USER_TAGS
        defaultShowEmptyRowsChecked = DEFAULT_SHOW_EMPTY_ROWS_CHECKED
        defaultLayoutType = DEFAULT_LAYOUT_TYPE
        defaultPrimaryGraphDataType = DEFAULT_PRIMARY_GRAPH_DATA_TYPE
        defaultSecondaryGraphDataType = DEFAULT_SECONDARY_GRAPH_DATA_TYPE
    }

    var defaultGrouping: TrainingZoneSummaryGrouping
        get() {
            return prefs.getString(KEY_DEFAULT_FILTER_GROUPING, null)
                ?.let {
                    try {
                        TrainingZoneSummaryGrouping.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_FILTER_GROUPING
        }
        set(value) = prefs.edit {
            putString(
                KEY_DEFAULT_FILTER_GROUPING,
                value.name
            )
        }

    var defaultSports: List<CoreActivityType>
        get() {
            return prefs
                .getStringSet(KEY_DEFAULT_FILTER_SPORTS, emptySet())
                ?.mapNotNull {
                    try {
                        CoreActivityType.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_FILTER_SPORTS
        }
        set(value) = prefs.edit {
            putStringSet(KEY_DEFAULT_FILTER_SPORTS, value.map { it.name }.toSet())
        }

    var defaultStartDate: Long?
        get() {
            return prefs.getLong(KEY_DEFAULT_FILTER_START_DATE, -1).let {
                if (it == -1L) {
                    DEFAULT_FILTER_START_DATE
                } else {
                    it
                }
            }
        }
        set(value) = prefs.edit {
            putLong(
                KEY_DEFAULT_FILTER_START_DATE,
                value ?: -1
            )
        }

    var defaultEndDate: Long?
        get() {
            return prefs.getLong(KEY_DEFAULT_FILTER_END_DATE, -1).let {
                if (it == -1L) {
                    DEFAULT_FILTER_END_DATE
                } else {
                    it
                }
            }
        }
        set(value) = prefs.edit {
            putLong(
                KEY_DEFAULT_FILTER_END_DATE,
                value ?: -1
            )
        }

    var defaultDistance: DistanceUiState
        get() {
            val minDistance = prefs.getFloat(KEY_DEFAULT_FILTER_MIN_DISTANCE, -1f)
            val maxDistance = prefs.getFloat(KEY_DEFAULT_FILTER_MAX_DISTANCE, -1f)
            if (minDistance == -1f || maxDistance == -1f) return DEFAULT_FILTER_DISTANCE

            return DistanceUiState.fromFilterRange(minDistance, maxDistance)
        }
        set(value) = prefs.edit {
            val (min, max) = DistanceUiState.toFilterRange(value)
            putFloat(KEY_DEFAULT_FILTER_MIN_DISTANCE, min)
            putFloat(KEY_DEFAULT_FILTER_MAX_DISTANCE, max)
        }

    var defaultSummarySuuntoTags: List<SummaryTag.SummarySuuntoTag>
        get() {
            return prefs
                .getString(KEY_DEFAULT_FILTER_SUUNTO_TAGS, null)
                ?.let {
                    try {
                        summarySuuntoTagsJsonAdapter.fromJson(it)
                    } catch (e: Exception) {
                        Timber.w("Error converting string $it to suunto tags $e")
                        null
                    }
                } ?: DEFAULT_FILTER_SUMMARY_SUUNTO_TAGS
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_FILTER_SUUNTO_TAGS, summarySuuntoTagsJsonAdapter.toJson(value))
        }

    var defaultSummaryUserTags: List<SummaryTag.SummaryUserTag>
        get() {
            return prefs
                .getString(KEY_DEFAULT_FILTER_USER_TAGS, null)
                ?.let {
                    try {
                        summaryUserTagsJsonAdapter.fromJson(it)
                    } catch (e: Exception) {
                        Timber.w("Error converting string $it to user tags $e")
                        null
                    }
                } ?: DEFAULT_FILTER_SUMMARY_USER_TAGS
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_FILTER_USER_TAGS, summaryUserTagsJsonAdapter.toJson(value))
        }

    var defaultRecentSummaryTags: List<SummaryTag>
        get() {
            return prefs
                .getString(KEY_DEFAULT_FILTER_RECENT_TAGS, null)
                ?.let {
                    try {
                        summaryTagsJsonAdapter.fromJson(it)
                    } catch (e: Exception) {
                        Timber.w("Error converting string $it to summary tags $e")
                        null
                    }
                } ?: DEFAULT_FILTER_SUMMARY_USER_TAGS
        }
        set(value) = prefs.edit {
            val recent = (value + defaultRecentSummaryTags).distinctBy {
                when (it) {
                    is SummaryTag.SummarySuuntoTag -> it.suuntoTag
                    is SummaryTag.SummaryUserTag -> it.userTag.id
                }
            }
            putString(KEY_DEFAULT_FILTER_RECENT_TAGS, summaryTagsJsonAdapter.toJson(recent))
        }

    var defaultShowEmptyRowsChecked: Boolean
        get() {
            return prefs.getBoolean(
                KEY_DEFAULT_SHOW_EMPTY_ROWS_CHECKED,
                DEFAULT_SHOW_EMPTY_ROWS_CHECKED
            )
        }
        set(value) {
            prefs.edit {
                putBoolean(KEY_DEFAULT_SHOW_EMPTY_ROWS_CHECKED, value)
            }
        }

    var defaultLayoutType: TrainingZoneSummaryLayoutType
        get() {
            return prefs
                .getString(KEY_DEFAULT_FILTER_LAYOUT_TYPE, null)
                ?.let {
                    try {
                        TrainingZoneSummaryLayoutType.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_LAYOUT_TYPE
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_FILTER_LAYOUT_TYPE, value.name)
        }

    var defaultSortingColumn: TrainingZoneSummaryColumn
        get() {
            return prefs
                .getString(KEY_DEFAULT_SORTING_COLUMN, null)
                ?.let {
                    try {
                        TrainingZoneSummaryColumn.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_SORTING_COLUMN
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_SORTING_COLUMN, value.name)
        }

    var defaultSortingOrder: TrainingZoneSummarySortingOrder
        get() {
            return prefs
                .getString(KEY_DEFAULT_SORTING_ORDER, null)
                ?.let {
                    try {
                        TrainingZoneSummarySortingOrder.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_SORTING_ORDER
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_SORTING_ORDER, value.name)
        }

    var defaultPrimaryGraphDataType: GraphDataType
        get() {
            return prefs
                .getString(KEY_DEFAULT_PRIMARY_GRAPH_DATA_TYPE, null)
                ?.let {
                    try {
                        GraphDataType.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_PRIMARY_GRAPH_DATA_TYPE
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_PRIMARY_GRAPH_DATA_TYPE, value.name)
        }

    var defaultSecondaryGraphDataType: GraphDataType
        get() {
            return prefs
                .getString(KEY_DEFAULT_SECONDARY_GRAPH_DATA_TYPE, null)
                ?.let {
                    try {
                        GraphDataType.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        Timber.w(e)
                        null
                    }
                } ?: DEFAULT_SECONDARY_GRAPH_DATA_TYPE
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_SECONDARY_GRAPH_DATA_TYPE, value.name)
        }

    private var hiddenSummaryColumns: Set<TrainingZoneSummaryColumn>
        get() = prefs.getStringSet(KEY_HIDDEN_SUMMARY_COLUMNS, null)?.map { column ->
            TrainingZoneSummaryColumn.valueOf(column)
        }?.toSet() ?: emptySet()
        set(value) = prefs.edit {
            putStringSet(KEY_HIDDEN_SUMMARY_COLUMNS, value.map { it.name }.toSet())
        }

    private var sortedSummaryColumns: List<TrainingZoneSummaryColumn>
        get() = prefs.getString(KEY_SORTED_SUMMARY_COLUMNS, null)?.let {
            summaryColumnListJsonAdapter.fromJson(it)
        } ?: (TrainingZoneSummaryColumn.entries.toList() - CANNOT_EDIT_COLUMNS)
        set(value) = prefs.edit {
            putString(KEY_SORTED_SUMMARY_COLUMNS, summaryColumnListJsonAdapter.toJson(value))
        }

    fun hideSummary(hide: Boolean, column: TrainingZoneSummaryColumn) {
        hiddenSummaryColumns = if (hide) {
            hiddenSummaryColumns + column
        } else {
            hiddenSummaryColumns - column
        }
    }

    fun hiddenSummaryColumnsFlow() = callbackFlow {
        trySendBlocking(hiddenSummaryColumns.toList())

        val listener = OnSharedPreferenceChangeListener { _, key ->
            if (key == KEY_HIDDEN_SUMMARY_COLUMNS) {
                trySendBlocking(hiddenSummaryColumns.toList())
            }
        }
        prefs.registerOnSharedPreferenceChangeListener(listener)

        awaitClose {
            prefs.unregisterOnSharedPreferenceChangeListener(listener)
        }
    }

    fun sortedSummaryColumnsFlow() = callbackFlow {
        trySendBlocking(sortedSummaryColumns)

        val listener = OnSharedPreferenceChangeListener { _, key ->
            if (key == KEY_SORTED_SUMMARY_COLUMNS) {
                trySendBlocking(sortedSummaryColumns)
            }
        }
        prefs.registerOnSharedPreferenceChangeListener(listener)

        awaitClose {
            prefs.unregisterOnSharedPreferenceChangeListener(listener)
        }
    }

    fun updateSortedSummaryColumns(sortedColumns: List<TrainingZoneSummaryColumn>) {
        sortedSummaryColumns = sortedColumns
    }

    var trainingGraphTimeRange: GraphTimeRange
        get() {
            return prefs.getString(KEY_DEFAULT_TRAINING_GRAPH_TIME_RANGE, null)
                ?.let { runCatching { GraphTimeRange.valueOf(it) }.getOrNull() }
                ?: GraphTimeRange.CURRENT_WEEK
        }
        set(value) {
            prefs.edit { putString(KEY_DEFAULT_TRAINING_GRAPH_TIME_RANGE, value.name) }
        }

    var primaryTrainingGraphType: TrainingGraphType
        get() {
            return prefs.getString(KEY_DEFAULT_PRIMARY_TRAINING_GRAPH_TYPE, null)
                ?.let { runCatching { TrainingGraphType.valueOf(it) }.getOrNull() }
                ?: TrainingGraphType.DURATION
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_PRIMARY_TRAINING_GRAPH_TYPE, value.name)
        }

    var secondaryTrainingGraphType: TrainingGraphType
        get() {
            return prefs.getString(KEY_DEFAULT_SECONDARY_TRAINING_GRAPH_TYPE, null)
                ?.let { runCatching { TrainingGraphType.valueOf(it) }.getOrNull() }
                ?: TrainingGraphType.DISTANCE
        }
        set(value) = prefs.edit {
            putString(KEY_DEFAULT_SECONDARY_TRAINING_GRAPH_TYPE, value.name)
        }
}
