package com.stt.android.diary.trainingv2.composables

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.GenericComparisonSegmentInfo
import com.stt.android.compose.widgets.GenericComparisonSelection
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.diary.trainingv2.summary.TrainingChartData
import com.stt.android.diary.trainingv2.summary.TrainingGraphEntryYFormatter
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.diary.trainingv2.summary.titleResId
import com.stt.android.chart.impl.R as ChartR

@Composable
internal fun TrainingGraph(
    chartData: TrainingChartData,
    entryYFormatter: TrainingGraphEntryYFormatter,
    chartHighlightedEvent: (Long?) -> Unit,
    onLongPressed: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                bottom = MaterialTheme.spacing.medium,
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium
            )
    ) {
        key(chartData) {
            TrainingChart(
                chartData = chartData,
                entryYFormatter = entryYFormatter,
                onEntrySelected = { chartHighlightedEvent(it) },
                onNoEntrySelected = { chartHighlightedEvent(null) },
                onLongPressed = onLongPressed,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 234.dp)
            )
        }

        chartData.averageLabelId?.let { averageLabelId ->
            Row(
                modifier = Modifier
                    .padding(top = MaterialTheme.spacing.small),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
            ) {
                Icon(
                    painter = painterResource(ChartR.drawable.ic_dashline),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small)
                )

                Text(
                    text = stringResource(averageLabelId),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            }
        }
    }
}

@Composable
internal fun GraphDataTypeListContent(
    selectedGraphType: TrainingGraphType,
    graphTypes: List<TrainingGraphType>,
    onGraphTypeSelected: (TrainingGraphType) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier
    ) {
        itemsIndexed(
            items = graphTypes,
            key = { _, graphType -> graphType.name }
        ) { index, graphType ->
            Column {
                Row(
                    modifier = Modifier
                        .clickable(
                            onClick = { onGraphTypeSelected(graphType) }
                        )
                        .heightIn(min = 56.dp)
                        .padding(horizontal = MaterialTheme.spacing.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(graphType.titleResId),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.weight(1f)
                    )

                    RadioButton(
                        selected = graphType == selectedGraphType,
                        onClick = null,
                        colors = RadioButtonDefaults.colors(
                            selectedColor = MaterialTheme.colorScheme.primary,
                            unselectedColor = MaterialTheme.colorScheme.mediumGrey
                        )
                    )
                }

                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            }
        }
    }
}

@Composable
internal fun TrainingGraphDataTypeSelection(
    dateRange: TrainingDateRange,
    primaryGraphType: TrainingGraphType,
    primaryGraphUnitResId: Int?,
    secondaryGraphType: TrainingGraphType,
    secondaryGraphUnitId: Int?,
    onPrimaryClick: () -> Unit,
    onSecondaryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val dateRangeString = when (dateRange) {
        is TrainingDateRange.CurrentYear -> stringResource(dateRange.value)
        is TrainingDateRange.CurrentMonth -> stringResource(dateRange.value)
        is TrainingDateRange.CurrentWeek -> stringResource(dateRange.value)
        is TrainingDateRange.CustomRange -> dateRange.value
    }
    GenericComparisonSelection(
        modifier = modifier
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium,
            ),
        primaryInfo = GenericComparisonSegmentInfo(
            title = stringResource(primaryGraphType.titleResId),
            subTitle = dateRangeString,
            summary = primaryGraphUnitResId?.let { stringResource(it) },
            color = MaterialTheme.colorScheme.primary,
        ),
        secondaryInfo = GenericComparisonSegmentInfo(
            title = stringResource(secondaryGraphType.titleResId),
            subTitle = dateRangeString,
            summary = secondaryGraphUnitId?.let { stringResource(it) },
            color = MaterialTheme.colorScheme.onSurface,
        ),
        onPrimaryClick = onPrimaryClick,
        onSecondaryClick = onSecondaryClick,
    )
}
