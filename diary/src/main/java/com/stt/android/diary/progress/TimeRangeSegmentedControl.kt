package com.stt.android.diary.progress

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.common.TimeRangeSegmentedControl
import com.stt.android.domain.diary.models.GraphTimeRange

@OptIn(ExperimentalFoundationApi::class)
internal fun LazyListScope.timeRangeSegmentedControl(
    timeRange: GraphTimeRange,
    onTimeRangeToggled: (GraphTimeRange) -> Unit,
    showTimeRangeDivider: <PERSON>olean,
) = stickyHeader(key = "time_range_segmented_control") {
    Column {
        TimeRangeSegmentedControl(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small
                ),
            timeRange = timeRange,
            timeRanges = listOf(
                GraphTimeRange.SIX_WEEKS,
                GraphTimeRange.SIX_MONTHS,
                GraphTimeRange.CURRENT_YEAR,
                GraphTimeRange.EIGHT_YEARS,
            ),
            moreTimeRanges = listOf(
                GraphTimeRange.CURRENT_WEEK,
                GraphTimeRange.CURRENT_MONTH,
            ),
            onTimeRangeToggled = onTimeRangeToggled,
        )
        if (showTimeRangeDivider) {
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}
