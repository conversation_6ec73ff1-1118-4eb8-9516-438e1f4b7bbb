package com.stt.android.diary.analytics

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty.SOURCE
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.TrendsAnalytics
import com.stt.android.di.DiaryPagePreferences
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.primaryGraphDataTypePrefsKey
import com.stt.android.domain.diary.models.primaryGraphDataTypes
import com.stt.android.domain.diary.models.secondaryGraphDataTypePrefsKey
import com.stt.android.domain.diary.models.secondaryGraphDataTypes
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DefaultTrendsAnalytics @Inject constructor(
    @DiaryPagePreferences private val diaryPagePreferences: SharedPreferences,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : TrendsAnalytics {
    override fun trackTrendsScreens(
        diaryPage: DiaryPage,
        timeRange: GraphTimeRange,
        source: String?
    ) {
        trackTrendsScreens(
            trendTab = diaryPage.analyticsTrendTab,
            mainGraphType = getPrimaryGraphDataType(diaryPage)?.analyticsGraphType,
            subGraphType = getSecondaryGraphDataType(diaryPage)?.analyticsGraphType,
            calendarLevel = timeRange.analyticsValue,
            source = source
        )
    }

    override fun trackTrendsViewChanged(
        diaryPage: DiaryPage,
        previousDiaryPage: DiaryPage?,
        timeRange: GraphTimeRange
    ) {
        trackTrendsViewChanged(
            newView = diaryPage.analyticsTrendTab,
            previousView = previousDiaryPage?.analyticsTrendTab,
            mainGraphType = getPrimaryGraphDataType(diaryPage)?.analyticsGraphType,
            subGraphType = getSecondaryGraphDataType(diaryPage)?.analyticsGraphType,
            calendarLevel = timeRange.analyticsValue
        )
    }

    override fun trackTrendsGraphSetupChanged(
        diaryPage: DiaryPage,
        primaryGraphTypeChanged: Boolean,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    ) {
        trackTrendsGraphSetupChanged(
            trendTab = diaryPage.analyticsTrendTab,
            graphTypeChanged = if (primaryGraphTypeChanged) VAL_CHANGE_MAIN_GRAPH_TYPE else VAL_CHANGE_SUB_GRAPH_TYPE,
            mainGraphType = primaryGraphType?.analyticsGraphType,
            subGraphType = secondaryGraphType?.analyticsGraphType,
            calendarLevel = timeRange.analyticsValue
        )
    }

    override fun trackTrendsGraphPeriodChanged(
        diaryPage: DiaryPage,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    ) {
        trackTrendsGraphPeriodChanged(
            trendTab = diaryPage.analyticsTrendTab,
            mainGraphType = primaryGraphType?.analyticsGraphType,
            subGraphType = secondaryGraphType?.analyticsGraphType,
            calendarLevel = timeRange.analyticsValue
        )
    }

    override fun trackTrendsGraphValueTapped(
        diaryPage: DiaryPage,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    ) {
        trackTrendsGraphValueTapped(
            trendTab = diaryPage.analyticsTrendTab,
            mainGraphType = primaryGraphType?.analyticsGraphType,
            subGraphType = secondaryGraphType?.analyticsGraphType,
            calendarLevel = timeRange.analyticsValue
        )
    }

    override fun trackDiaryGraphValueTapped(analyticsProperties: AnalyticsProperties) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.DIARY_GRAPH_VALUE_TAPPED,
            analyticsProperties
        )
    }

    private fun trackTrendsScreens(
        trendTab: String,
        mainGraphType: String?,
        subGraphType: String?,
        calendarLevel: String,
        source: String?
    ) {
        AnalyticsProperties().apply {
            put(PROPERTY_TREND_TAB, trendTab)
            if (mainGraphType != null) put(PROPERTY_MAIN_GRAPH_TYPE, mainGraphType)
            if (subGraphType != null) put(PROPERTY_SUB_GRAPH_TYPE, subGraphType)
            if (source != null) put(SOURCE, source)
            put(PROPERTY_CALENDAR_LEVEL, calendarLevel)
            emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.TRENDS_SCREEN, this.map)
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRENDS_SCREEN, this)
        }
    }

    private fun trackTrendsViewChanged(
        newView: String,
        previousView: String?,
        mainGraphType: String?,
        subGraphType: String?,
        calendarLevel: String
    ) {
        AnalyticsProperties().apply {
            put(PROPERTY_NEW_VIEW, newView)
            if (previousView != null) put(PROPERTY_PREVIOUS_VIEW, previousView)
            if (mainGraphType != null) put(PROPERTY_MAIN_GRAPH_TYPE, mainGraphType)
            if (subGraphType != null) put(PROPERTY_SUB_GRAPH_TYPE, subGraphType)
            put(PROPERTY_CALENDAR_LEVEL, calendarLevel)
            // Agreed not to send to Braze
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRENDS_VIEW_CHANGED, this)
        }
    }

    private fun trackTrendsGraphSetupChanged(
        trendTab: String,
        graphTypeChanged: String,
        mainGraphType: String?,
        subGraphType: String?,
        calendarLevel: String
    ) {
        AnalyticsProperties().apply {
            put(PROPERTY_TREND_TAB, trendTab)
            put(PROPERTY_GRAPH_TYPE_CHANGED, graphTypeChanged)
            if (mainGraphType != null) put(PROPERTY_MAIN_GRAPH_TYPE, mainGraphType)
            if (subGraphType != null) put(PROPERTY_SUB_GRAPH_TYPE, subGraphType)
            put(PROPERTY_CALENDAR_LEVEL, calendarLevel)
            // Agreed not to send to Braze
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRENDS_GRAPH_SETUP_CHANGED, this)
        }
    }

    private fun trackTrendsGraphPeriodChanged(
        trendTab: String,
        mainGraphType: String?,
        subGraphType: String?,
        calendarLevel: String
    ) {
        AnalyticsProperties().apply {
            put(PROPERTY_TREND_TAB, trendTab)
            if (mainGraphType != null) put(PROPERTY_MAIN_GRAPH_TYPE, mainGraphType)
            if (subGraphType != null) put(PROPERTY_SUB_GRAPH_TYPE, subGraphType)
            put(PROPERTY_CALENDAR_LEVEL, calendarLevel)
            // Agreed not to send to Braze
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRENDS_GRAPH_PERIOD_CHANGED, this)
        }
    }

    private fun trackTrendsGraphValueTapped(
        trendTab: String,
        mainGraphType: String?,
        subGraphType: String?,
        calendarLevel: String
    ) {
        AnalyticsProperties().apply {
            put(PROPERTY_TREND_TAB, trendTab)
            if (mainGraphType != null) put(PROPERTY_MAIN_GRAPH_TYPE, mainGraphType)
            if (subGraphType != null) put(PROPERTY_SUB_GRAPH_TYPE, subGraphType)
            put(PROPERTY_CALENDAR_LEVEL, calendarLevel)
            // Agreed not to send to Braze
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRENDS_GRAPH_VALUE_TAPPED, this)
        }
    }

    override fun getPrimaryGraphDataType(diaryPage: DiaryPage): GraphDataType? {
        return getGraphDataType(diaryPage.primaryGraphDataTypePrefsKey)
            ?: diaryPage.primaryGraphDataTypes.firstOrNull()
    }

    override fun getSecondaryGraphDataType(diaryPage: DiaryPage): GraphDataType? {
        return getGraphDataType(diaryPage.secondaryGraphDataTypePrefsKey)
            ?: diaryPage.secondaryGraphDataTypes.firstOrNull()
    }

    private fun getGraphDataType(prefsKey: String): GraphDataType? {
        return diaryPagePreferences.getString(prefsKey, null)?.let {
            if (it in GraphDataType.entries.map { v -> v.toString() }) {
                GraphDataType.valueOf(it)
            } else {
                null
            }
        }
    }

    companion object {
        private const val PROPERTY_TREND_TAB = "TrendTab"
        private const val PROPERTY_MAIN_GRAPH_TYPE = "MainGraphType"
        private const val PROPERTY_SUB_GRAPH_TYPE = "SubGraphType"
        private const val PROPERTY_CALENDAR_LEVEL = "CalendarLevel"
        private const val PROPERTY_NEW_VIEW = "NewView"
        private const val PROPERTY_PREVIOUS_VIEW = "PreviousView"
        private const val PROPERTY_GRAPH_TYPE_CHANGED = "GraphTypeChanged"

        private const val VAL_CHANGE_MAIN_GRAPH_TYPE = "ChangeMainGraphType"
        private const val VAL_CHANGE_SUB_GRAPH_TYPE = "ChangeSubGraphType"
    }
}

val DiaryPage.analyticsTrendTab: String
    get() = when (this) {
        DiaryPage.TRAINING -> "Training"
        DiaryPage.SCUBA_DIVING -> "ScubaDiving"
        DiaryPage.FREE_DIVING -> "FreeDiving"
        DiaryPage.PROGRESS -> "Progress"
        DiaryPage.RECOVERY -> "Recovery"
        DiaryPage.DAILY_ACTIVITY -> "Activity"
        DiaryPage.SLEEP -> "Sleep"
        DiaryPage.OVERVIEW -> "Overview"
        DiaryPage.SUMMARY -> "Summary"
        DiaryPage.STATISTICS -> "Statistics"
    }

val GraphDataType.analyticsGraphType: String
    get() = when (this) {
        GraphDataType.DURATION -> "WorkoutDuration"
        GraphDataType.DISTANCE -> "WorkoutDistance"
        GraphDataType.TSS -> "TSS"
        GraphDataType.STEPS -> "Steps"
        GraphDataType.CALORIES -> "Calories"
        GraphDataType.SLEEP_QUALITY -> "SleepQuality"
        GraphDataType.EXERCISE_FEEL -> "ExerciseMood"
        GraphDataType.AVERAGE_HEART_RATE -> "AvgWorkoutHR"
        GraphDataType.ASCENT -> "TotalAscent"
        GraphDataType.FITNESS_LEVEL -> "FitnessLevel"
        GraphDataType.SLEEP_DURATION -> "SleepDuration"
        GraphDataType.SLEEP_REGULARITY -> "SleepRegularity"
        GraphDataType.BLOOD_OXYGEN -> "SleepSpO2Max"
        GraphDataType.TRAINING -> "Training"
        GraphDataType.AVG_HR_DURING_SLEEP -> "AvgSleepHR"
        GraphDataType.MORNING_RESOURCES -> "MorningResources"
        GraphDataType.FREE_DIVE_COUNT -> "DiveCount"
        GraphDataType.SCUBA_DIVE_COUNT -> "DiveCount"
        GraphDataType.HRV -> "SleepHRV"
        GraphDataType.NONE -> "NoGraph"
        GraphDataType.SLEEP_TOTAL -> "SleepTotalDuration"
        GraphDataType.SLEEP_NAP -> "NapDuration"
        GraphDataType.AVG_SPEED -> "AvgSpeed"
        GraphDataType.AVG_PACE -> "AvgPace"
        GraphDataType.AVG_POWER -> "AvgPower"
        GraphDataType.NORMALIZED_POWER -> "NormalizedPower"
        GraphDataType.AVG_SWIM_PACE -> "AvgSwimPace"
        GraphDataType.RECOVERY_STATE -> "RecoveryState"
    }
