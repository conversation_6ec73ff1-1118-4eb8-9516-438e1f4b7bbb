package com.stt.android.diary.progress

import android.content.Context
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.home.diary.R
import com.stt.android.utils.CustomTabsUtils

@Composable
internal fun ProgressScreen(
    viewModel: ProgressViewModel,
    modifier: Modifier = Modifier,
) {
    ContentCenteringColumn(modifier = modifier) {
        val viewData = viewModel.viewData.collectAsState().value
        when (viewData) {
            ProgressViewData.Initial -> Unit

            is ProgressViewData.Loaded -> {
                ProgressLoaded(
                    viewData = viewData,
                    onEvent = { viewModel.onEvent(it) },
                )
            }
        }
    }
}

@Composable
private fun ProgressLoaded(
    viewData: ProgressViewData.Loaded,
    onEvent: (ProgressViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    val fitnessViewData by viewData.fitnessViewDataMap[viewData.fitnessPageIndex]!!.collectAsState(initial = null)
    val ctlRampRateViewData by viewData.ctlRampRateViewData.collectAsState(initial = null)
    val vo2MaxViewData by viewData.vo2MaxViewDataMap[viewData.vo2MaxPageIndex]!!.collectAsState(initial = null)
    val fitnessHighlightedViewData by viewData.fitnessHighlightedViewData.collectAsState(initial = null)
    val vo2MaxHighlightedViewData by viewData.vo2MaxHighlightedViewData.collectAsState(initial = null)

    var showFitnessInfo by rememberSaveable { mutableStateOf(false) }
    var showVo2MaxInfo by rememberSaveable { mutableStateOf(false) }

    var containerPositionInRootY by remember { mutableIntStateOf(0) }
    var fitnessChartPositionInRootY by remember { mutableIntStateOf(0) }
    var fitnessHighlightedHeight by remember { mutableIntStateOf(0) }
    var vo2MaxChartPositionInRootY by remember { mutableIntStateOf(0) }
    var vo2MaxHighlightedHeight by remember { mutableIntStateOf(0) }
    val listState = rememberLazyListState()
    val showTimeRangeDivider by remember(listState) {
        derivedStateOf { listState.firstVisibleItemIndex > 2 } // Header, FitnessFatigueForm, Space
    }

    Box(
        modifier = modifier
            .onLayoutRectChanged(debounceMillis = 0L) {
                containerPositionInRootY = it.positionInRoot.y
            }
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxSize(),
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize()
        ) {
            fitnessItems(
                timeRange = viewData.timeRange,
                viewData = fitnessViewData,
                fitnessViewDataMap = viewData.fitnessViewDataMap,
                fitnessPageIndex = viewData.fitnessPageIndex,
                fitnessPageCount = viewData.fitnessPageCount,
                fitnessPageStartDate = viewData.fitnessPageStartDate,
                fitnessPageEndDate = viewData.fitnessPageEndDate,
                highlightedIndex = viewData.fitnessHighlightedIndex,
                onEvent = onEvent,
                onInfoClicked = { showFitnessInfo = true },
                onPositionInRootYChanged = { fitnessChartPositionInRootY = it },
                showTimeRangeDivider = showTimeRangeDivider,
            )
            ctlRampRateItems(ctlRampRateViewData)
            vo2MaxItems(
                timeRange = viewData.timeRange,
                viewData = vo2MaxViewData,
                vo2MaxViewDataMap = viewData.vo2MaxViewDataMap,
                vo2MaxPageIndex = viewData.vo2MaxPageIndex,
                vo2MaxPageCount = viewData.vo2MaxPageCount,
                vo2MaxPageStartDate = viewData.vo2MaxPageStartDate,
                vo2MaxPageEndDate = viewData.vo2MaxPageEndDate,
                highlightedIndex = viewData.vo2MaxHighlightedIndex,
                onEvent = onEvent,
                onInfoClicked = { showVo2MaxInfo = true },
                onPositionInRootYChanged = { vo2MaxChartPositionInRootY = it },
            )
        }
        FitnessHighlightedView(
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (fitnessChartPositionInRootY - containerPositionInRootY - fitnessHighlightedHeight)
                            .coerceAtLeast(0),
                    )
                }
                .onSizeChanged { fitnessHighlightedHeight = it.height },
            viewData = fitnessHighlightedViewData,
        )
        Vo2MaxHighlightedView(
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (vo2MaxChartPositionInRootY - containerPositionInRootY - vo2MaxHighlightedHeight)
                            .coerceAtLeast(0),
                    )
                }
                .onSizeChanged { vo2MaxHighlightedHeight = it.height },
            viewData = vo2MaxHighlightedViewData,
        )
    }

    if (showFitnessInfo) {
        FitnessLevelInfoBottomSheet(
            onInfoProgressionClicked = {
                launchCustomTab(context, R.string.tss_info_progression_url)
            },
            onInfoValuesClicked = {
                launchCustomTab(context, R.string.tss_info_values_url)
            },
            onTrainingPeaksClick = {
                launchCustomTab(context, R.string.tss_info_training_peaks_url)
            },
            onDismiss = { showFitnessInfo = false },
        )
    }
    if (showVo2MaxInfo) {
        Vo2MaxInfoBottomSheet(onDismiss = { showVo2MaxInfo = false })
    }
}

private fun launchCustomTab(context: Context, @StringRes urlResId: Int) {
    CustomTabsUtils.launchCustomTab(context, context.getString(urlResId))
}
