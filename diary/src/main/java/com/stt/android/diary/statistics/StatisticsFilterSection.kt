package com.stt.android.diary.statistics

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.TiledSegmentedControl
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.TrainingZoneSummaryLayoutType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.extensions.intervalDescriptionRes
import com.stt.android.extensions.shortStringRes
import com.stt.android.extensions.stringRes
import com.stt.android.home.diary.R
import com.stt.android.chart.impl.R as ChartR

@Composable
internal fun StatisticsFilterSection(
    layoutType: TrainingZoneSummaryLayoutType,
    onLayoutTypeClick: () -> Unit,
    onFilterClick: () -> Unit,
    grouping: TrainingZoneSummaryGrouping,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    workoutsCount: Int,
    showFilterResetIcon: Boolean,
    onFilterResetClick: () -> Unit,
    onSelectActivitiesClick: () -> Unit,
    graphTimeRange: GraphTimeRange,
    onGraphTimeRangeToggled: (GraphTimeRange) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Max)
                .horizontalScroll(rememberScrollState())
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.small,
                ),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            LayoutType(
                layoutType = layoutType,
                onLayoutTypeClick = onLayoutTypeClick,
                modifier = Modifier.fillMaxHeight(),
            )

            when (layoutType) {
                TrainingZoneSummaryLayoutType.TABLE -> {
                    Box(
                        modifier = Modifier
                            .clip(CircleShape)
                            .border(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.dividerColor,
                                shape = CircleShape,
                            )
                            .clickable(onClick = onFilterClick)
                            .fillMaxHeight(),
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.filter_outline),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier
                                .padding(horizontal = MaterialTheme.spacing.medium)
                                .align(Alignment.Center)
                                .size(MaterialTheme.iconSizes.small)
                        )
                    }

                    GroupBy(
                        grouping = grouping,
                        onGroupingChange = onGroupingChange,
                        modifier = Modifier.fillMaxHeight(),
                    )
                }

                TrainingZoneSummaryLayoutType.GRAPH -> GraphTimeRange(
                    graphTimeRange = graphTimeRange,
                    onGraphTimeRangeToggled = onGraphTimeRangeToggled,
                    modifier = Modifier.fillMaxHeight(),
                )
            }
        }

        SportsFilter(
            countWorkouts = workoutsCount,
            showResetIcon = showFilterResetIcon,
            onResetClick = onFilterResetClick,
            onSelectActivitiesClick = onSelectActivitiesClick,
            modifier = Modifier
                .padding(
                    start = MaterialTheme.spacing.small,
                    top = MaterialTheme.spacing.small,
                ),
        )
    }
}

@Composable
private fun GraphTimeRange(
    graphTimeRange: GraphTimeRange,
    onGraphTimeRangeToggled: (GraphTimeRange) -> Unit,
    modifier: Modifier = Modifier,
) {
    TiledSegmentedControl(
        segment = graphTimeRange,
        segments = listOf(
            GraphTimeRange.CURRENT_WEEK,
            GraphTimeRange.CURRENT_MONTH,
            GraphTimeRange.CURRENT_YEAR,
        ),
        moreSegments = listOf(
            GraphTimeRange.SEVEN_DAYS,
            GraphTimeRange.THIRTY_DAYS,
            GraphTimeRange.SIX_WEEKS,
            GraphTimeRange.SIX_MONTHS,
            GraphTimeRange.EIGHT_YEARS,
        ),
        onSegmentToggled = onGraphTimeRangeToggled,
        shortName = { stringResource(shortStringRes) },
        longName = { stringResource(stringRes) },
        description = { stringResource(intervalDescriptionRes) },
        bottomSheetTitle = {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
            ) {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                Text(
                    text = stringResource(ChartR.string.chart_granularity_time_range_title),
                    color = MaterialTheme.colorScheme.onSurface,
                    style = MaterialTheme.typography.bodyXLargeBold,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                )
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                Text(
                    text = stringResource(ChartR.string.chart_granularity_time_range_desc),
                    color = MaterialTheme.colorScheme.secondary,
                    style = MaterialTheme.typography.body,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                )
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            }
        },
        modifier = modifier,
    )
}

@Composable
private fun LayoutType(
    layoutType: TrainingZoneSummaryLayoutType,
    onLayoutTypeClick: () -> Unit,
    modifier: Modifier = Modifier,
) {

    TiledSegmentedControl(
        segment = layoutType,
        segments = listOf(
            TrainingZoneSummaryLayoutType.TABLE,
            TrainingZoneSummaryLayoutType.GRAPH,
        ),
        moreSegments = emptyList(),
        onSegmentToggled = { onLayoutTypeClick() },
        shortName = { stringResource(getLabelResId()) },
        longName = { "" },
        description = { "" },
        bottomSheetTitle = {},
        modifier = modifier,
    )
}

@Composable
private fun GroupBy(
    grouping: TrainingZoneSummaryGrouping,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    modifier: Modifier = Modifier,
) {
    TiledSegmentedControl(
        segment = grouping,
        segments = TrainingZoneSummaryGrouping.entries,
        moreSegments = emptyList(),
        onSegmentToggled = onGroupingChange,
        shortName = { stringResource(getLabelResId()) },
        longName = { "" },
        description = { "" },
        bottomSheetTitle = {},
        modifier = modifier,
    )
}

@Composable
private fun SportsFilter(
    countWorkouts: Int,
    showResetIcon: Boolean,
    onResetClick: () -> Unit,
    onSelectActivitiesClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        TextButton(
            onClick = onSelectActivitiesClick
        ) {
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.training_v2_training_all_sports),
                        style = MaterialTheme.typography.bodyXLargeBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Icon(
                        painter = painterResource(R.drawable.ic_trend_down),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier
                            .size(MaterialTheme.iconSizes.small)
                    )
                }
                Text(
                    text = pluralStringResource(
                        R.plurals.training_v2_training_data_analysis_count_workouts,
                        countWorkouts,
                        countWorkouts
                    ),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.darkGrey
                )
            }
        }

        if (showResetIcon) {
            IconButton(onClick = onResetClick) {
                Icon(
                    painter = painterResource(R.drawable.reset_outline),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun StatisticsFilterSectionPreview() = M3AppTheme {
    StatisticsFilterSection(
        grouping = TrainingZoneSummaryGrouping.BY_ACTIVITY,
        onGroupingChange = {},
        onFilterClick = {},
        layoutType = TrainingZoneSummaryLayoutType.TABLE,
        onLayoutTypeClick = {},
        workoutsCount = 10,
        showFilterResetIcon = true,
        onFilterResetClick = {},
        onSelectActivitiesClick = {},
        graphTimeRange = GraphTimeRange.CURRENT_WEEK,
        onGraphTimeRangeToggled = {},
    )
}
