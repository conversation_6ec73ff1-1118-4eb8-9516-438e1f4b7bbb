package com.stt.android.diary.progress

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.progress.ProgressViewEvent.FitnessHighlighted
import com.stt.android.diary.progress.ProgressViewEvent.FitnessPageUpdated
import com.stt.android.diary.progress.ProgressViewEvent.TimeRangeToggled
import com.stt.android.domain.diary.models.GraphTimeRange
import kotlinx.coroutines.flow.StateFlow

internal fun LazyListScope.fitnessItems(
    timeRange: GraphTimeRange,
    viewData: FitnessViewData?,
    fitnessViewDataMap: Map<Int, StateFlow<FitnessViewData>>,
    fitnessPageIndex: Int,
    fitnessPageCount: Int,
    fitnessPageStartDate: String,
    fitnessPageEndDate: String,
    highlightedIndex: Int?,
    onEvent: (ProgressViewEvent) -> Unit,
    onInfoClicked: () -> Unit,
    onPositionInRootYChanged: (Int) -> Unit,
    showTimeRangeDivider: Boolean,
) {
    if (viewData !is FitnessViewData.Loaded) return

    tssAnalysisHeader(viewData)
    tssAnalysisFitnessFatigueForm(
        viewData = viewData,
        onInfoClicked = onInfoClicked,
    )
    item(key = "space_before_segmented_control") {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
    }
    timeRangeSegmentedControl(
        timeRange = viewData.timeRange,
        onTimeRangeToggled = { onEvent(TimeRangeToggled(it)) },
        showTimeRangeDivider = showTimeRangeDivider,
    )
    item(key = "space_after_segmented_control") {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
    }
    tssAnalysisFitnessCharts(
        timeRange = timeRange,
        fitnessViewDataMap = fitnessViewDataMap,
        fitnessPageIndex = fitnessPageIndex,
        fitnessPageCount = fitnessPageCount,
        fitnessPageStartDate = fitnessPageStartDate,
        fitnessPageEndDate = fitnessPageEndDate,
        onFitnessPageUpdated = { page -> onEvent(FitnessPageUpdated(page)) },
        highlightedIndex = highlightedIndex,
        onHighlighted = { page, index -> onEvent(FitnessHighlighted(page, index)) },
        onPositionInRootYChanged = onPositionInRootYChanged,
    )
    tssAnalysisPhaseDescriptions(viewData)
}
