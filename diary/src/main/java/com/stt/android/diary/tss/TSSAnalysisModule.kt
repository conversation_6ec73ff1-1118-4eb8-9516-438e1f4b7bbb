package com.stt.android.diary.tss

import com.stt.android.common.ui.ViewPagerFragmentCreator
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.data.workout.tss.WorkoutTSSSummaryRepositoryImpl
import com.stt.android.domain.workouts.tss.WorkoutTSSSummaryRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class TSSAnalysisModule {
    @Binds
    @Named("TSS_FRAGMENT")
    abstract fun bindTSSAnalysisFragmentCreator(tssAnalysisFragmentCreator: TSSAnalysisFragmentCreator): ViewPagerFragmentCreator

    @Binds
    abstract fun bindController(controller: TSSAnalysisEpoxyController): ViewStateEpoxyController<TSSAnalysisData>

    @Binds
    abstract fun bindWorkoutTSSSummaryRepository(repository: WorkoutTSSSummaryRepositoryImpl): WorkoutTSSSummaryRepository
}
