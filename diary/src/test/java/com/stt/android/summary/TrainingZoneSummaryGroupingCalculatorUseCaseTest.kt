package com.stt.android.summary

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import java.time.DayOfWeek
import java.time.LocalDate
import com.google.common.truth.Truth.assertThat
import com.stt.android.diary.summary.TrainingZoneSummaryDataAggregatorUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryFormatter
import com.stt.android.diary.summary.TrainingZoneSummaryGroupingCalculatorUseCase

class TrainingZoneSummaryGroupingCalculatorUseCaseTest {
    @get:Rule
    val coroutinesTestRule = NewCoroutinesTestRule()

    private val trainingZoneSummaryFormatter: TrainingZoneSummaryFormatter =
        TrainingZoneSummaryFormatterFake()

    private val coroutinesDispatchers = object : CoroutinesDispatchers {
        override val main = coroutinesTestRule.testDispatcher
        override val computation = coroutinesTestRule.testDispatcher
        override val io = coroutinesTestRule.testDispatcher
    }

    private val currentLocalDate = LocalDate.of(2023, 6, 10)
    private val summaryGroupingCalculatorUseCase = TrainingZoneSummaryGroupingCalculatorUseCase(
        aggregator = TrainingZoneSummaryDataAggregatorUseCase(),
        coroutinesDispatchers = coroutinesDispatchers,
        localDate = currentLocalDate,
        trainingZoneSummaryFormatter = trainingZoneSummaryFormatter
    )

    @Test
    fun calculateYearlyV3() = runTest {
        val result = summaryGroupingCalculatorUseCase.calculateYearly(
            filteredWorkouts = listOf(
                makeSummaryWorkoutHeader(
                    id = 1,
                    totalDistance = 100.0,
                    date = LocalDate.of(2023, 6, 6),
                    totalTime = 160.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 2,
                    totalDistance = 100.0,
                    date = LocalDate.of(2023, 3, 6),
                    totalTime = 160.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 3,
                    totalDistance = 100.0,
                    date = LocalDate.of(2021, 6, 6),
                    totalTime = 260.0,
                )
            ),
            firstDayOfTheWeek = DayOfWeek.MONDAY,
        )

        assertThat(result).hasSize(3)
        assertThat(result[0].year).isEqualTo(2023)
        assertThat(result[0].totalDuration).isEqualTo(160.0 + 160.0)
        assertThat(result[1].year).isEqualTo(2022)
        assertThat(result[1].totalDuration).isEqualTo(0.0)
        assertThat(result[1].isEmpty).isTrue()
        assertThat(result[2].year).isEqualTo(2021)
        assertThat(result[2].totalDuration).isEqualTo(260.0)
    }

    @Test
    fun calculateMonthlyV3() = runTest {
        val result = summaryGroupingCalculatorUseCase.calculateMonthly(
            filteredWorkouts = listOf(
                makeSummaryWorkoutHeader(
                    id = 1,
                    totalDistance = 100.0,
                    date = LocalDate.of(2023, 6, 6),
                    totalTime = 100.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 10,
                    totalDistance = 1000.0,
                    date = LocalDate.of(2023, 6, 16),
                    totalTime = 1000.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 2,
                    totalDistance = 200.0,
                    date = LocalDate.of(2023, 5, 6),
                    totalTime = 200.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 3,
                    totalDistance = 300.0,
                    date = LocalDate.of(2023, 2, 6),
                    totalTime = 300.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 4,
                    totalDistance = 400.0,
                    date = LocalDate.of(2023, 1, 6),
                    totalTime = 400.0,
                )
            ),
        )

        assertThat(result).hasSize(6)
        assertThat(result[0].formattedDate).isEqualTo("Jun")
        assertThat(result[1].formattedDate).isEqualTo("May")
        assertThat(result[2].formattedDate).isEqualTo("Apr")
        assertThat(result[3].formattedDate).isEqualTo("Mar")
        assertThat(result[4].formattedDate).isEqualTo("Feb")
        assertThat(result[5].formattedDate).isEqualTo("Jan")

        assertThat(result[0].totalDuration).isEqualTo(100.0 + 1000.0)
        assertThat(result[1].totalDuration).isEqualTo(200.0)
        assertThat(result[2].totalDuration).isEqualTo(0.0)
        assertThat(result[3].totalDuration).isEqualTo(0.0)
        assertThat(result[4].totalDuration).isEqualTo(300.0)
        assertThat(result[5].totalDuration).isEqualTo(400.0)
    }

    @Test
    fun calculateWeeklyV3() = runTest {
        val result = summaryGroupingCalculatorUseCase.calculateWeekly(
            filteredWorkouts = listOf(
                makeSummaryWorkoutHeader(
                    id = 1,
                    totalDistance = 100.0,
                    date = LocalDate.of(2023, 6, 6), // This is Tuesday
                    totalTime = 100.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 10,
                    totalDistance = 1000.0,
                    date = LocalDate.of(2023, 6, 9),
                    totalTime = 1000.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 2,
                    totalDistance = 200.0,
                    date = LocalDate.of(2023, 5, 31),
                    totalTime = 200.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 3,
                    totalDistance = 300.0,
                    date = LocalDate.of(2023, 5, 24),
                    totalTime = 300.0,
                ),
                makeSummaryWorkoutHeader(
                    id = 4,
                    totalDistance = 400.0,
                    date = LocalDate.of(2023, 5, 3),
                    totalTime = 400.0,
                )
            ),
            firstDayOfTheWeek = DayOfWeek.MONDAY,
        )

        // 1 and 10 belongs to same week [5.6-11.6]
        // 2 in own week [29.5-4.6]
        // 3 in own week [22.5-28.5]
        // empty [15.5-21.5]
        // empty [8.5-14.5]
        // 4 in own week [1.5-7.5]
        assertThat(result).hasSize(6)

        assertThat(result[0].totalDuration).isEqualTo(100.0 + 1000.0)
        assertThat(result[1].totalDuration).isEqualTo(200.0)
        assertThat(result[2].totalDuration).isEqualTo(300.0)
        assertThat(result[3].totalDuration).isEqualTo(0.0)
        assertThat(result[4].totalDuration).isEqualTo(0.0)
        assertThat(result[5].totalDuration).isEqualTo(400.0)
    }

    companion object {
        fun makeSummaryWorkoutHeader(
            id: Int,
            totalDistance: Double,
            date: LocalDate,
            totalTime: Double
        ) = SummaryWorkoutHeader(
            id = id,
            key = null,
            username = "",
            totalDistance = totalDistance,
            activityTypeId = 0,
            avgSpeed = 0.0,
            startTime = date.atEndOfDay().toEpochMilli(),
            totalTime = totalTime,
            heartRateAverage = 0.0,
            totalAscent = 0.0,
            energyConsumption = 0.0,
            tss = null,
            vo2Max = null,
            supportsDistance = false,
            supportsAvgSpeed = false,
            supportsAvgPace = false,
            supportsAvgHeartRate = false,
            supportsAscent = false,
            supportsEnergyConsumption = false,
            supportsTss = false,
            supportsVo2Max = false,
            avgPower = null,
            normalizedPower = null,
            supportsAvgPower = false,
            supportsNormalizedPower = false,
            supportsAvgSwimPace = false,
        )
    }
}
