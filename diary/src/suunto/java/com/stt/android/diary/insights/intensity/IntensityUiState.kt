package com.stt.android.diary.insights.intensity

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Immutable
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.ImmutableList
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Immutable
data class IntensityUiState(
    val selectedType: IntensityType,
    val heartRateZones: ImmutableList<IntensityZoneInfoUiState>,
    val paceZones: ImmutableList<IntensityZoneInfoUiState>,
    val runningPowerZones: ImmutableList<IntensityZoneInfoUiState>,
    val cyclingPowerZones: ImmutableList<IntensityZoneInfoUiState>,
    val supportComparison: Boolean,
) {
    val intensityZones: ImmutableList<IntensityZoneInfoUiState> = when (selectedType) {
        IntensityType.HEART_RATE -> heartRateZones
        IntensityType.PACE -> paceZones
        IntensityType.RUNNING_POWER -> runningPowerZones
        IntensityType.CYCLING_POWER -> cyclingPowerZones
    }
    /* used in the donut we have removed for now
    val currentGraphData: List<GraphData> = heartRateZones.map {
        GraphData(
            value = it.currentValue.toInt(),
            color = it.color
        )
    }

    val comparisonGraphData: List<GraphData> = heartRateZones.map {
        GraphData(
            value = it.comparisonValue.toInt(),
            color = it.color
        )
    }
     */
}

@Immutable
data class IntensityZoneInfoUiState(
    val number: Int,
    val currentValue: Float,
    val comparisonValue: Float?,
    val currentValueFormatted: String,
    val comparisonValueFormatted: String?,
    val currentFraction: Float,
    val comparisonFraction: Float?
) {
    @ColorRes val color = when (number) {
        1 -> CR.color.heart_rate_1
        2 -> CR.color.heart_rate_2
        3 -> CR.color.heart_rate_3
        4 -> CR.color.heart_rate_4
        5 -> CR.color.heart_rate_5
        else -> throw IndexOutOfBoundsException("$number is not in 1..5 range")
    }
    companion object {
        fun empty(
            number: Int,
            valueFormatted: String // To not depend on a formatter
        ): IntensityZoneInfoUiState {
            return IntensityZoneInfoUiState(
                number = number,
                currentValue = 0.0f,
                comparisonValue = 0.0f,
                currentValueFormatted = valueFormatted,
                comparisonValueFormatted = valueFormatted,
                currentFraction = 0.0f,
                comparisonFraction = 0.0f
            )
        }
    }
}

enum class IntensityType(@DrawableRes val iconRes: Int, @StringRes val titleRes: Int) {
    HEART_RATE(R.drawable.heart_rate_training_zones_fill, R.string.training_hub_intensity_type_heart_rate),
    PACE(BaseR.drawable.gauge_outline, R.string.training_hub_intensity_type_pace),
    RUNNING_POWER(R.drawable.power_outline, R.string.training_hub_intensity_type_running_power),
    CYCLING_POWER(R.drawable.power_outline, R.string.training_hub_intensity_type_cycling_power),
}
