package com.stt.android.diary.recovery.v2

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.recovery.composables.CommonChart
import com.stt.android.diary.recovery.composables.RecoveryStateHeader
import com.stt.android.diary.recovery.composables.recoveryStateContributorsChartItems
import com.stt.android.domain.diary.models.RecoveryZone

internal fun LazyListScope.recoveryV2ChartContent(
    recoveryChartStateData: RecoveryChartStateData,
    onEvent: (RecoveryV2Event) -> Unit,
) {
    if (recoveryChartStateData is RecoveryChartStateData.Loaded) {
        recoveryChartStateData.recoveryChartData?.let { recoveryChartData ->
            item {
                RecoveryStateHeader(
                    recoveryScore = recoveryChartData.recoveryScore ?: 0,
                    recoveryZone = recoveryChartData.recoveryZone ?: RecoveryZone.NO_DATA,
                    onEvent = onEvent,
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium
                    ),
                    isDaily = false,
                )
            }
        }

        item {
            CommonChart(
                chartData = recoveryChartStateData.recoveryChartData?.recoveryChart,
                height = 250.dp,
                onEntrySelected = { },
                onNoEntrySelected = { },
                modifier = Modifier.fillMaxWidth()
            )
        }

        recoveryChartStateData.recoveryChartContributors?.let {
            recoveryStateContributorsChartItems(
                chartContributors = it,
                onEvent = onEvent,
                onInfoClick = { infoBottomSheet ->
                    onEvent(RecoveryV2Event.ShowRecoveryStateInfoSheet(infoBottomSheet))
                }
            )
        }
    }
}
