package com.stt.android.diary.recovery.usecases

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.diary.recovery.data.SleepComparisonChartDataLoader
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.diary.recovery.v2.SleepComparisonChartData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject

class LoadSleepComparisonChartDataUseCase @Inject constructor(
    private val sleepComparisonChartDataLoader: SleepComparisonChartDataLoader,
    private val coroutinesDispatchers: CoroutinesDispatchers
) {

     operator fun invoke(
        chartGranularity: ChartGranularity,
        leftSelectionType: SleepChartSelectionType,
        rightSelectionType: SleepChartSelectionType,
        from: LocalDate,
        to: LocalDate,
        timeRange: String
    ): Flow<SleepComparisonChartData?> {
        
        return sleepComparisonChartDataLoader
            .loadComparisonChartData(
                chartGranularity = chartGranularity,
                primaryGraphType = leftSelectionType,
                secondaryGraphType = rightSelectionType,
                from = from,
                to = to,
                timeRange = timeRange
            )
            .flowOn(coroutinesDispatchers.io)
    }
} 
