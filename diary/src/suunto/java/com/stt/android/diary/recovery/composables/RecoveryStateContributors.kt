package com.stt.android.diary.recovery.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.GenericStackedProgress
import com.stt.android.compose.widgets.StackedProgressItem
import com.stt.android.diary.recovery.model.getColorRes
import com.stt.android.diary.recovery.model.getEmptyContentRes
import com.stt.android.diary.recovery.model.getHeaderRes
import com.stt.android.diary.recovery.model.getIconRes
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.domain.diary.models.RecoveryStateContributors
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.R
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

fun LazyListScope.recoveryStateContributorsItems(
    contributors: RecoveryStateContributors,
    onInfoClick: (InfoBottomSheet) -> Unit,
    onContributorClick: (RecoveryStateContributor) -> Unit,
) {
    item {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.xsmall,
                ),
        ) {
            Text(
                text = stringResource(BaseR.string.recovery_state_contributors),
                style = MaterialTheme.typography.bodyXLargeBold,
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = MaterialTheme.spacing.medium),
            )
        }
    }

    item {
        RecoveryStateContributor(
            contributor = contributors.sleepDuration, 
            type = ContributorType.SLEEP,
            onClick = contributors.sleepDuration?.let { { onContributorClick(it) } }
        )
    }

    item {
        RecoveryStateContributor(
            contributor = contributors.hrv,
            type = ContributorType.HRV,
            onClick = contributors.hrv?.let { { onContributorClick(it) } },
        )
    }

    item {
        RecoveryStateContributor(
            contributor = contributors.restHr,
            type = ContributorType.REST_HR,
            onClick = contributors.restHr?.let { { onContributorClick(it) } },
        )
    }

    item {
        RecoveryStateContributor(
            contributor = contributors.trainingFatigue,
            type = ContributorType.TRAINING_FATIGUE,
            onInfoClick = {
                onInfoClick(InfoBottomSheet.RECOVERY_INFO_TRAINING_FATIGUE)
            }
        )
    }

    contributors.resources?.let {
        item {
            RecoveryStateContributor(
                contributor = it,
                type = ContributorType.RESOURCES,
                onClick = { onContributorClick(it) },
            )
        }
    }
}



@Composable
private fun RecoveryStateContributor(
    contributor: RecoveryStateContributor?,
    type: ContributorType,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    onInfoClick: (() -> Unit)? = null,
) {
    if (contributor == null) {
        EmptyRecoveryStateContributor(type = type, modifier = modifier)
    } else {
        CommonRecoveryStateContributor(
            headerRes = type.getHeaderRes(),
            iconRes = type.getIconRes(),
            colorRes = type.getColorRes(),
            onClick = onClick,
            onInfoClick = onInfoClick,
            modifier = modifier,
        ) {
            RecoveryStateContributorContent(contributor)
        }
    }
}

@Composable
private fun EmptyRecoveryStateContributor(
    type: ContributorType,
    modifier: Modifier = Modifier
) {
    val headerRes = type.getHeaderRes()
    val iconRes = type.getIconRes()
    val colorRes = type.getColorRes()
    
    CommonRecoveryStateContributor(
        headerRes = headerRes,
        iconRes = iconRes,
        colorRes = colorRes,
        onClick = null,
        onInfoClick = null,
        modifier = modifier,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            val emptyTextRes = type.getEmptyContentRes()
            Text(
                text = if (emptyTextRes != null) stringResource(emptyTextRes) else "",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
private fun RecoveryStateContributorContent(
    contributor: RecoveryStateContributor,
    modifier: Modifier = Modifier,
) {
    when (contributor) {
        is RecoveryStateContributor.SleepDuration -> SleepDurationContent(contributor, modifier)
        is RecoveryStateContributor.Hrv -> HrvContent(contributor, modifier)
        is RecoveryStateContributor.RestHr -> RestHrContent(contributor, modifier)
        is RecoveryStateContributor.TrainingFatigue -> TrainingFatigueContent(contributor, modifier)
        is RecoveryStateContributor.Resources -> ResourcesContent(contributor, modifier)
    }
}

@Composable
private fun SleepDurationContent(
    contributor: RecoveryStateContributor.SleepDuration,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {

        Column(modifier = Modifier.weight(1f)) {
            if (contributor.lastNight > 0) {
                Text(
                    text = "${contributor.lastNight} h",
                    style = MaterialTheme.typography.bodyLargeBold
                )
            } else {
                Text(
                    text = "-- h",
                    style = MaterialTheme.typography.bodyLargeBold
                )
            }
            Text(
                text = stringResource(BaseR.string.widget_last_night),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "${contributor.avg} h",
                style = MaterialTheme.typography.bodyLargeBold,
            )

            Text(
                text = "7-day avg.",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
private fun RestHrContent(
    contributor: RecoveryStateContributor.RestHr,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = if (contributor.today > 0) "${contributor.today} bpm" else "-- bpm",
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = stringResource(BaseR.string.today),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "${contributor.avg} bpm",
                style = MaterialTheme.typography.bodyLargeBold,
            )
            Text(
                text = "7-day avg.",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
private fun HrvContent(
    contributor: RecoveryStateContributor.Hrv,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = if (contributor.lastNight > 0) "${contributor.lastNight}" else "--",
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = stringResource(R.string.hrv_last_night),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
            
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
            
            HrvProgressBar(
                value = contributor.lastNight.toFloat().coerceIn(30f, 120f),
                lowRange = contributor.lowRange,
                highRange = contributor.highRange,
                modifier = Modifier.fillMaxWidth(0.9f)
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            val hasBaselineRange = contributor.lowRange > 0 && contributor.highRange > 0
            
            val statusText = if (hasBaselineRange) {
                when {
                    contributor.avg > contributor.highRange -> stringResource(R.string.hrv_status_high)
                    contributor.avg < contributor.lowRange -> stringResource(R.string.hrv_status_low)
                    else -> stringResource(R.string.hrv_status_normal)
                }
            } else {
                when {
                    contributor.avg > 70 -> stringResource(R.string.hrv_status_high)
                    contributor.avg > 50 -> stringResource(R.string.hrv_status_normal)
                    else -> stringResource(R.string.hrv_status_low)
                }
            }
            
            Text(
                text = "${contributor.avg} / $statusText",
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = stringResource(R.string.hrv_seven_day_avg),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
            
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
            
            HrvProgressBar(
                value = contributor.avg.toFloat().coerceIn(0f, 300f),
                lowRange = contributor.lowRange,
                highRange = contributor.highRange,
                modifier = Modifier.fillMaxWidth(0.9f)
            )
        }
    }
}

@Composable
private fun HrvProgressBar(
    value: Float,
    lowRange: Int,
    highRange: Int,
    modifier: Modifier = Modifier
) {
    val lowColor = colorResource(id = CR.color.diary_calendar_color)
    val mediumColor = colorResource(id = CR.color.st_outdoor_adventures)
    val highColor = colorResource(id = CR.color.suunto_running)
    val noBaseLineNormal = colorResource(id = BaseR.color.near_white)
    val noBaseLineRange = colorResource(id = BaseR.color.cloudy_grey)
    
    val hasBaselineRange = lowRange > 0 && highRange > 0
    
    val items = remember {
        if (hasBaselineRange) {
            listOf(
                StackedProgressItem(0f, lowRange.toFloat(), lowColor),
                StackedProgressItem(lowRange.toFloat(), highRange.toFloat(), mediumColor),
                StackedProgressItem(highRange.toFloat(), 300f, highColor)
            )
        } else {
            listOf(
                StackedProgressItem(0f, 35f, noBaseLineNormal),
                StackedProgressItem(35f, 65f, noBaseLineRange),
                StackedProgressItem(65f, 100f, noBaseLineNormal)
            )
        }
    }
    
    GenericStackedProgress(
        value = value,
        items = items,
        modifier = modifier,
        showMarkers = false
    )
}

@Composable
private fun TrainingFatigueContent(
    contributor: RecoveryStateContributor.TrainingFatigue,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceAround,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${contributor.todayTSS} / ${stringResource(R.string.recovery)}",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(com.stt.android.home.diary.R.string.tss_today),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val items = listOf(
                    StackedProgressItem(0f, 20f, colorResource(id = R.color.recovery_tss_zone_1)),
                    StackedProgressItem(20f, 40f, colorResource(id = CR.color.suunto_diving)),
                    StackedProgressItem(40f, 60f, colorResource(id = CR.color.suunto_performance)),
                    StackedProgressItem(60f, 80f, colorResource(id = R.color.recovery_tss_zone_4)),
                    StackedProgressItem(80f, 100f, colorResource(id = CR.color.heart_rate_2))
                )
                
                GenericStackedProgress(
                    value = contributor.todayTSS.toFloat().coerceIn(0f, 100f),
                    items = items,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false
                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${contributor.yesterdayTSS} / ${stringResource(R.string.gain)}",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(com.stt.android.home.diary.R.string.tss_yesterday),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val items = listOf(
                    StackedProgressItem(0f, 20f, colorResource(id = R.color.recovery_tss_zone_1)),
                    StackedProgressItem(20f, 40f, colorResource(id = CR.color.suunto_diving)),
                    StackedProgressItem(40f, 60f, colorResource(id = CR.color.suunto_performance)),
                    StackedProgressItem(60f, 80f, colorResource(id = R.color.recovery_tss_zone_4)),
                    StackedProgressItem(80f, 100f, colorResource(id = CR.color.heart_rate_2))
                )
                
                GenericStackedProgress(
                    value = contributor.yesterdayTSS.toFloat().coerceIn(0f, 100f),
                    items = items,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false
                )
            }
        }
        
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        
        Row(
            horizontalArrangement = Arrangement.SpaceAround,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                val tsbRating = when {
                    contributor.todayTSB < -30 -> stringResource(R.string.tsb_rating_exhausted)
                    contributor.todayTSB < -10 -> stringResource(R.string.tsb_rating_fatigued_gaining_fitness)
                    contributor.todayTSB < 15 -> stringResource(R.string.tsb_rating_balanced)
                    else -> stringResource(R.string.tsb_rating_ready_for_more)
                }
                
                Text(
                    text = "${contributor.todayTSB} / $tsbRating",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(com.stt.android.home.diary.R.string.tsb),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val items = listOf(
                    StackedProgressItem(-40f, -30f, colorResource(id = CR.color.st_cycling)),
                    StackedProgressItem(-30f, -10f, colorResource(id = CR.color.suunto_running)),
                    StackedProgressItem(-10f, 15f, colorResource(id = CR.color.confirmation_color)),
                    StackedProgressItem(15f, 25f, colorResource(id = CR.color.st_diving)),
                )
                
                GenericStackedProgress(
                    value = contributor.todayTSB.toFloat().coerceIn(-40f, 25f),
                    items = items,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false
                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                val feelingText = when {
                    contributor.last7DaysFeeling >= 4.5 -> stringResource(R.string.feeling_excellent)
                    contributor.last7DaysFeeling >= 3.5 -> stringResource(R.string.feeling_good)
                    contributor.last7DaysFeeling >= 2.5 -> stringResource(R.string.feeling_normal)
                    contributor.last7DaysFeeling >= 1.5 -> stringResource(R.string.feeling_poor)
                    else -> stringResource(R.string.feeling_very_poor)
                }
                
                Text(
                    text = feelingText,
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(R.string.seven_day_avg_feelings),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val feelingDistribution = contributor.feelingDistribution.ifEmpty {
                    (1..5).associateWith { 20.0 }
                }
                
                val colorList = listOf(
                    colorResource(id = CR.color.bright_red),
                    colorResource(id = CR.color.st_cycling),
                    colorResource(id = CR.color.suunto_running),
                    colorResource(id = BaseR.color.dashboard_widget_resources),
                    colorResource(id = CR.color.confirmation_color)
                )
                
                val feelingItems = mutableListOf<StackedProgressItem>()
                var startPosition = 0f
                
                (1..5).forEach { feeling ->
                    val percentage = feelingDistribution[feeling] ?: 0.0
                    val endPosition = startPosition + percentage.toFloat()
                    
                    if (percentage > 0) {
                        feelingItems.add(
                            StackedProgressItem(
                                min = startPosition, 
                                max = endPosition,
                                color = colorList[feeling - 1]
                            )
                        )
                    }
                    startPosition = endPosition
                }
                
                GenericStackedProgress(
                    value = 100f,
                    items = feelingItems,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false,
                    showTick = false
                )
            }
        }
    }
}

@Composable
private fun ResourcesContent(
    contributor: RecoveryStateContributor.Resources,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        val resourcesLevel = when {
            contributor.today >= 80 -> stringResource(R.string.resources_level_very_high)
            contributor.today >= 51 -> stringResource(R.string.resources_level_high)
            contributor.today >= 20 -> stringResource(R.string.resources_level_moderate)
            else -> stringResource(R.string.resources_level_low)
        }
        
        Text(
            text = "${contributor.today}% / $resourcesLevel",
            style = MaterialTheme.typography.bodyLargeBold
        )
        
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(10.dp)
                .clip(RoundedCornerShape(5.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(contributor.today / 100f)
                    .height(10.dp)
                    .clip(RoundedCornerShape(5.dp))
                    .background(
                        when {
                            contributor.today >= 80 -> colorResource(id = CR.color.confirmation_color)
                            contributor.today >= 60 -> colorResource(id = CR.color.st_outdoor_adventures)
                            contributor.today >= 40 -> colorResource(id = CR.color.suunto_running)
                            contributor.today >= 20 -> colorResource(id = CR.color.st_cycling)
                            else -> colorResource(id = CR.color.bright_red)
                        }
                    )
            )
        }
    }
}

@Preview
@Composable
private fun RecoveryStateContributorsPreview() {
    M3AppTheme {
        LazyColumn {
            recoveryStateContributorsItems(
                RecoveryStateContributors(
                    last1DaySleepDuration = 7.1,
                    last7DaysSleepDuration = 6.0,
                    last1DayHrv = 41,
                    last7DaysHrv = 32,
                    last1DayRestHR = 46,
                    last7DaysRestHR = 55,
                    yesterdayTSS = 43,
                    todayTSS = 34,
                    todayTSB = -2,
                    last7DaysFeeling = 3.6,
                    todayResources = 34,
                ),
                {},
            ) {}
        }
    }
}

