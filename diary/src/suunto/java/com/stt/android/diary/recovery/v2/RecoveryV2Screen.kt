package com.stt.android.diary.recovery.v2

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.compose.modifiers.rememberColumnNestScrollConnection
import com.stt.android.compose.theme.dividerColor
import com.stt.android.diary.recovery.composables.RecoverySegmentedControl
import com.stt.android.diary.recovery.composables.SleepComparisonGraphSelectionBottomSheet
import com.stt.android.diary.trainingv2.composables.TrainingDatePicker
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.home.diary.InfoBottomSheet

@Composable
fun RecoveryV2Screen(
    onShowRecoveryStateInfoSheet: (InfoBottomSheet) -> Unit,
    modifier: Modifier = Modifier,
    onContributorClick: (RecoveryStateContributor) -> Unit = {},
    viewModel: RecoveryV2ViewModel = hiltViewModel(),
) {
    val recoveryV2State by viewModel.viewState.collectAsState()

    viewModel.setShowRecoveryStateInfoSheetCallback(onShowRecoveryStateInfoSheet)
    viewModel.setContributorClickCallback(onContributorClick)

    var containerHeightPx by remember { mutableIntStateOf(0) }
    var datePickerHeightPx by remember { mutableIntStateOf(0) }
    val density = LocalDensity.current
    val contentHeightDp by remember(containerHeightPx, datePickerHeightPx) {
        mutableStateOf(density.run { (containerHeightPx - datePickerHeightPx).toDp() })
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .onSizeChanged { size ->
                containerHeightPx = size.height
            }
    ) {
        when (recoveryV2State) {
            is RecoveryV2State.Loading -> {
                RecoveryV2Loading(modifier = Modifier.fillMaxSize())
            }

            is RecoveryV2State.Loaded -> {
                val loadedState = recoveryV2State as RecoveryV2State.Loaded
                val datePickerData = loadedState.datePickerData
                val listState = rememberLazyListState()
                val containerListState = rememberScrollState()
                val containerNestedScrollConnection =
                    rememberColumnNestScrollConnection(containerListState)

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .nestedScroll(containerNestedScrollConnection)
                        .verticalScroll(containerListState)
                ) {
                    RecoverySegmentedControl(
                        currentTimeGranularity = loadedState.currentTimeGranularity,
                        mainTimeGranularities = loadedState.mainTimeGranularities,
                        extraTimeGranularities = loadedState.extraTimeGranularities,
                        onTimeGranularityToggled = { granularity ->
                            viewModel.onEvent(RecoveryV2Event.UpdateTimeGranularity(granularity))
                        },
                    )

                    Column(
                        modifier = Modifier
                            .onSizeChanged { size ->
                                datePickerHeightPx = size.height
                            }
                    ) {
                        Column {
                            TrainingDatePicker(
                                trainingDateRange = datePickerData.trainingDateRange,
                                onNextClick = { viewModel.onEvent(RecoveryV2Event.NavigateNext) },
                                onPreviousClick = { viewModel.onEvent(RecoveryV2Event.NavigatePrevious) },
                                isNextEnabled = datePickerData.canNavigateForward,
                                isPreviousEnabled = datePickerData.canNavigateBack,
                                isLoading = loadedState.isLoading,
                            )
                            if (listState.canScrollBackward) {
                                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
                            }
                        }
                    }

                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .height(contentHeightDp)
                    ) {
                        recoveryV2DailyContent(
                            recoveryDailyStateData = loadedState.recoveryDailyStateData,
                            onEvent = viewModel::onEvent,
                        )

                        recoveryV2ChartContent(
                            recoveryChartStateData = loadedState.recoveryChartStateData,
                            onEvent = viewModel::onEvent,
                        )
                    }
                }

                SleepComparisonGraphSelectionBottomSheet(
                    state = loadedState,
                    onEvent = viewModel::onEvent
                )
            }
        }
    }
}

@Composable
private fun RecoveryV2Loading(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(32.dp),
            color = color,
        )
    }
}

