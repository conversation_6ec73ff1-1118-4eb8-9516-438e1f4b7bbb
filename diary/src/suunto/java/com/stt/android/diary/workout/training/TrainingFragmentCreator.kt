package com.stt.android.diary.workout.training

import android.os.Bundle
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.ViewPagerFragmentCreator
import javax.inject.Inject

class TrainingFragmentCreator @Inject constructor() : ViewPagerFragmentCreator {
    override fun create(args: Bundle?) = TrainingFragment()
    override fun getIconId() = R.drawable.ic_diary_tab_workouts
    override fun getTabStyleId() = R.style.Theme_DiaryWorkouts
    override fun getTabTypeForAnalytics() = AnalyticsPropertyValue.SuuntoDiaryType.TRAINING
    override fun getTabContentDescriptionStringResId() = R.string.workout_graph_title

    override fun hasTimeRange(): Boolean = true
}
