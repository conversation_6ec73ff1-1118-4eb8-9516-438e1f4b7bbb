package com.stt.android.diary.dailyhealth

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stt.android.compose.theme.dividerColor
import com.stt.android.diary.dailyhealth.composables.DailyHealthItem
import com.stt.android.diary.recovery.composables.RecoverySegmentedControl
import com.stt.android.home.diary.InfoBottomSheet

@Composable
fun DailyHealthScreen(
    onShowInfoSheet: (InfoBottomSheet) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: DailyHealthViewModel = hiltViewModel()
) {
    val viewData by viewModel.viewData.collectAsStateWithLifecycle()

    when (viewData) {
        is DailyHealthViewData.Initial -> {
            Box(
                modifier = modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }

        is DailyHealthViewData.Loaded -> {
            DailyHealthContent(
                viewData = viewData as DailyHealthViewData.Loaded,
                onEvent = viewModel::onEvent,
                onShowInfoSheet = onShowInfoSheet,
                modifier = modifier.fillMaxSize()
            )
        }
    }
}

@Composable
private fun DailyHealthContent(
    viewData: DailyHealthViewData.Loaded,
    onEvent: (DailyHealthEvent) -> Unit,
    onShowInfoSheet: (InfoBottomSheet) -> Unit,
    modifier: Modifier = Modifier,
) {
    val listState = rememberLazyListState()
    LazyColumn(
        state = listState,
        modifier = modifier.padding(bottom = 55.dp)
    ) {
        stickyHeader {
            Column {
                RecoverySegmentedControl(
                    currentTimeGranularity = viewData.currentTimeGranularity,
                    mainTimeGranularities = viewData.mainTimeGranularities,
                    extraTimeGranularities = viewData.extraTimeGranularities,
                    onTimeGranularityToggled = { granularity ->
                        onEvent(DailyHealthEvent.UpdateTimeGranularity(granularity))
                    },
                )
                if (listState.canScrollBackward) {
                    HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
                }
            }
        }

        items(viewData.healthItems) { healthItem ->
            DailyHealthItem(
                healthItem = healthItem,
                chartHighlight = healthItem.chartHighlight,
                infoClick = if (healthItem.hasInfoButton){
                    { onShowInfoSheet(it) }
                } else {
                    null
                },
                onEntrySelected = {
                    onEvent(DailyHealthEvent.ShowHighlight(healthItem.chartType,it))
                },
                onNoEntrySelected = {
                    onEvent(DailyHealthEvent.HideHighlight)
                },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

