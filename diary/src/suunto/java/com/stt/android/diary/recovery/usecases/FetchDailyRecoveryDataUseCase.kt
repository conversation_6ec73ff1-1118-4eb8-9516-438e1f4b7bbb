package com.stt.android.diary.recovery.usecases

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.diary.recovery.v2.RecoveryDailyStateData
import com.stt.android.domain.diary.insights.FetchRecoveryStateScoreUseCase
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.domain.diary.models.RecoveryStateContributors
import com.stt.android.domain.diary.models.RecoveryStateData
import com.stt.android.domain.diary.models.RecoveryZone
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.LocalTime
import javax.inject.Inject
import kotlin.math.roundToInt

class FetchDailyRecoveryDataUseCase @Inject constructor(
    private val fetchRecoveryStateScoreUseCase: FetchRecoveryStateScoreUseCase,
    private val recoveryDataRepository: RecoveryDataRepository,
    private val coroutinesDispatchers: CoroutinesDispatchers
) {
    operator fun invoke(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<RecoveryDailyStateData> {
        val recoveryStateFlow = fetchRecoveryStateScoreUseCase(
            fromDate = fromDate,
            toDate = toDate,
            includeContributors = true,
        ).map { list ->
            list.lastOrNull()?.let { (_, recoveryStateData) ->
                recoveryStateData
            }
        }
        
        val resourcesBalanceFlow = recoveryDataRepository.fetchRecoveryDataForDateRange(
            fromDate.atStartOfDay().toEpochMilli(),
            toDate.atTime(LocalTime.MAX).toEpochMilli()
        )
        .onEmpty { emit(emptyList()) }
        .map { recoveryDataList ->
            if (recoveryDataList.isEmpty()) {
                0
            } else {
                val latestData = recoveryDataList.maxByOrNull { it.timestamp }
                latestData?.let { 
                    (it.balance * 100).roundToInt()
                } ?: 0
            }
        }
        
        return combine(recoveryStateFlow, resourcesBalanceFlow) { recoveryStateDataResult, latestResourcesBalance ->
            val resourcesContributor = createResourcesContributor(latestResourcesBalance)
            val finalRecoveryStateData = createRecoveryStateData(recoveryStateDataResult, resourcesContributor)
            
            RecoveryDailyStateData.Loaded(finalRecoveryStateData)
        }.flowOn(coroutinesDispatchers.io)
    }
    
    private fun createResourcesContributor(latestResourcesBalance: Int): RecoveryStateContributor.Resources? {
        return if (latestResourcesBalance > 0) {
            RecoveryStateContributor.Resources(today = latestResourcesBalance)
        } else {
            null
        }
    }
    
    private fun createRecoveryStateData(
        existingData: RecoveryStateData?,
        resourcesContributor: RecoveryStateContributor.Resources?
    ): RecoveryStateData {
        return existingData?.copy(
            contributors = createRecoveryStateContributors(existingData.contributors, resourcesContributor)
        ) ?: createDefaultRecoveryStateData(resourcesContributor)
    }
    
    private fun createRecoveryStateContributors(
        existingContributors: RecoveryStateContributors?,
        resourcesContributor: RecoveryStateContributor.Resources?
    ): RecoveryStateContributors {
        return existingContributors?.copy(
            resources = resourcesContributor
        ) ?: RecoveryStateContributors(
            sleepDuration = null,
            hrv = null,
            restHr = null,
            trainingFatigue = null,
            resources = resourcesContributor,
        )
    }
    
    private fun createDefaultRecoveryStateData(
        resourcesContributor: RecoveryStateContributor.Resources?
    ): RecoveryStateData {
        return RecoveryStateData(
            recoveryScore = 0,
            recoveryZone = RecoveryZone.NO_DATA,
            contributors = RecoveryStateContributors(
                sleepDuration = null,
                hrv = null,
                restHr = null,
                trainingFatigue = null,
                resources = resourcesContributor,
            ),
        )
    }
} 
