package com.stt.android.diary.trainingv2

import androidx.compose.runtime.Immutable
import com.soy.algorithms.coach.CoachPhrase
import com.soy.algorithms.coach.SuuntoCoachInsightType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.training.TrainingPeriodAnalysis
import kotlinx.collections.immutable.ImmutableMap
import java.time.LocalDate

@Immutable
data class TrainingUiState(
    val firstPeriod: ClosedRange<LocalDate>? = null,
    val firstAnalysis: TrainingPeriodAnalysis? = null,
    val secondPeriod: ClosedRange<LocalDate>? = null,
    val secondAnalysis: TrainingPeriodAnalysis? = null,
    val coachFeedback: ImmutableMap<SuuntoCoachInsightType, CoachPhrase>? = null,
    val graphTimeRange: GraphTimeRange = GraphTimeRange.CURRENT_WEEK,
    val dateRange: TrainingDateRange = TrainingDateRange.CurrentWeek
)
