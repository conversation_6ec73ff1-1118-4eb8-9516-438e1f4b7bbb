package com.stt.android.diary.recovery.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.soy.algorithms.recovery.calculateRestHeartRate
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.controllers.UserSettingsController
import com.stt.android.diary.recovery.v2.CommonChartContributor
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.diary.R
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.takeIfNotNaN
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

class RestingHeartRateChartDataLoader @Inject constructor(
    private val context: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val trendDataRepository: TrendDataRepository,
    private val userSettingsController: UserSettingsController
) {
    
    companion object {
        private const val NO_DATA_VALUE = -1
        private const val MIN_Y = 40.0
        private const val DEFAULT_MAX_Y = 100.0
        private const val LOW_HEART_RATE_THRESHOLD = 50
        
        private const val GRID_STEP_MULTIPLE = 10.0
        private const val CHART_DIVISIONS = 3.0
        
        private const val LINE_CHART_EXPANSION_FACTOR = 1.2
        
        private val SUPPORTED_GRANULARITIES = setOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.MONTHLY,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY
        )
    }
    
    private val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
    
    private val LocalDate.epochMonth: Int get() = (year - 1970) * 12 + monthValue - 1
    
    fun loadChartContributor(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Flow<CommonChartContributor> {
        if (chartGranularity !in SUPPORTED_GRANULARITIES) {
            throw IllegalArgumentException("This loader does not support ChartGranularity.$chartGranularity")
        }
        
        val sleepTimeRange = getSleepRange(from, to)
        val fromMillis = sleepTimeRange.first
        val toMillis = sleepTimeRange.second
        
        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        )
        
        val sleepFlow = fetchSleepUseCase.fetchSleeps(from = from, to = to)
        
        return combine(trendDataFlow, sleepFlow) { trendDataList, sleepList ->
            val chartData = seriesStrategies[chartGranularity]?.createChartData(chartGranularity, from, to, trendDataList, sleepList)
                ?: DailyRestingHrSeriesStrategy().createChartData(chartGranularity, from, to, trendDataList, sleepList)

            CommonChartContributor(
                value = chartData.series.firstOrNull()?.value ?: AnnotatedString("--"),
                valueType = context.getString(R.string.recovery_daily_average),
                chartData = chartData,
                contributorType = ContributorType.REST_HR
            )
        }
    }
    
    private fun getSleepRange(fromDate: LocalDate, toDate: LocalDate): Pair<Long, Long> {
        val fromMillis = fromDate.minusDays(1).atTime(12, 0).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val toMillis = toDate.atTime(12, 0).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return Pair(fromMillis, toMillis)
    }

    private fun getRestingHeartRate(
        dayTrendData: List<TrendData>,
        daySleepData: List<Sleep>
    ): Int? {
        val sleepTimeRanges = daySleepData.mapNotNull { sleep ->
            sleep.longSleep?.let { it.fellAsleep..it.wokeUp }
        }
        
        if (sleepTimeRanges.isEmpty()) {
            return null
        }
        
        val trendDataListByRange = mutableMapOf<LongRange, MutableList<TrendData>>()
        dayTrendData.forEach { trendData ->
            sleepTimeRanges.forEach { range ->
                if (trendData.timestamp in range) {
                    val list = trendDataListByRange.getOrPut(range) { mutableListOf() }
                    list.add(trendData)
                }
            }
        }
        
        if (trendDataListByRange.isEmpty()) {
            return null
        }
        
        val restHeartRates = trendDataListByRange.map { (_, trendList) ->
            calculateRestHeartRate(trendList.mapNotNull { it.hr?.hz })
        }
        
        return restHeartRates.averageOfDouble(HeartRate::inBpm)
            .takeIfNotNaN()
            ?.roundToInt()
            ?: 0
    }
    
    private fun calculateDailyRestingHrs(
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>,
        from: LocalDate,
        to: LocalDate
    ): List<Int> {
        val trendDataListByDate = groupTrendDataByDate(trendDataList)
        val sleepListByDate = groupSleepByDate(sleepList)
        
        val dailyRestingHrs = mutableListOf<Int>()

        var currentDate = from
        while (currentDate <= to) {
            val dataForDay = trendDataListByDate[currentDate] ?: emptyList()
            val sleepsForDay = sleepListByDate[currentDate] ?: emptyList()
            
            val restingHr = getRestingHeartRate(dataForDay, sleepsForDay) ?: NO_DATA_VALUE
            
            dailyRestingHrs.add(restingHr)
            currentDate = currentDate.plusDays(1)
        }

        return dailyRestingHrs
    }
    
    private fun groupTrendDataByDate(trendDataList: List<TrendData>): Map<LocalDate, List<TrendData>> {
        return trendDataList.groupBy { trendData ->
            Instant.ofEpochMilli(trendData.timestamp + TimeUnit.HOURS.toMillis(12))
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
    }

    private fun groupSleepByDate(sleepList: List<Sleep>): Map<LocalDate, List<Sleep>> {
        return sleepList.groupBy { sleep ->
            Instant.ofEpochMilli(sleep.timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
    }
    
    private fun adjustYAxisRangeForLineChart(minHeartRate: Int, maxHeartRate: Int): Pair<Double, Double> {
        if (maxHeartRate <= LOW_HEART_RATE_THRESHOLD) {
            return Pair(MIN_Y, DEFAULT_MAX_Y)
        }
        
        var expandedMaxY = maxHeartRate.toDouble() * LINE_CHART_EXPANSION_FACTOR
        expandedMaxY = ceil(expandedMaxY / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
        
        var step = (expandedMaxY - MIN_Y) / CHART_DIVISIONS
        val remainder = step % GRID_STEP_MULTIPLE
        
        if (remainder != 0.0) {
            step = ceil(step / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
            expandedMaxY = MIN_Y + step * CHART_DIVISIONS
        }
        
        return Pair(MIN_Y, expandedMaxY)
    }

    private interface RestingHrDataSeriesStrategy {
        fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>
        ): ChartData
    }
    
    private val seriesStrategies: Map<ChartGranularity, RestingHrDataSeriesStrategy> = mapOf(
        ChartGranularity.WEEKLY to DailyRestingHrSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DailyRestingHrSeriesStrategy(),
        ChartGranularity.MONTHLY to DailyRestingHrSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DailyRestingHrSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyRestingHrSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyRestingHrSeriesStrategy()
    )
    
    private inner class DailyRestingHrSeriesStrategy : RestingHrDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>
        ): ChartData {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            var currentDate = from
            
            var totalValue = 0f
            var totalDays = 0
            
            dailyRestingHrs.forEach { hr ->
                val x = currentDate.toEpochDay()
                
                if (hr != NO_DATA_VALUE && hr > 0) {
                    entries.add(ChartData.Entry(x = x, y = hr.toDouble()))
                    totalValue += hr
                    totalDays++
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            val value = buildAnnotatedString {
                withStyle(SpanStyle(fontSize = 24.sp)) {
                    append("${averageValue.roundToInt()}")
                }
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(context.getString(CR.string.bpm))
                }
            }
            
            val axisRange = ChartData.AxisRange(
                minX = from.toEpochDay().toDouble(),
                maxX = to.toEpochDay().toDouble(),
                minY = minY,
                maxY = maxY
            )
            
            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_minimum_heart_rate),
                axisRange = axisRange,
                entries = entriesImmutable,
                value = value
            )
            
            return ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.RESTING_HEART_RATE,
                colorIndicator = null,
            )
        }
    }
    
    private inner class WeeklyRestingHrSeriesStrategy : RestingHrDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>
        ): ChartData {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailyRestingHrs.indices) {
                dailyData.add(currentDate to dailyRestingHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByWeek = dailyData.groupBy { (date, _) ->
                date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            }
            
            val weeks = dataByWeek.keys.sortedBy { it.toEpochDay() }
            weeks.forEach { weekStartDate ->
                val weekData = dataByWeek[weekStartDate] ?: emptyList()
                
                val weekHrValues = weekData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE && hr > 0) hr else null }
                
                if (weekHrValues.isNotEmpty()) {
                    val weekMinHr = weekHrValues.minOrNull() ?: 0
                    
                    if (weekMinHr > 0) {
                        entries.add(ChartData.Entry(x = weekStartDate.toEpochDay(), y = weekMinHr.toDouble()))
                        totalValue += weekMinHr
                        totalDays++
                    }
                }
            }
            
            val entriesImmutable = entries.toImmutableList()
            
            val fromWeekStart = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            val toWeekStart = to.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            val value = buildAnnotatedString {
                withStyle(SpanStyle(fontSize = 24.sp)) {
                    append("${averageValue.roundToInt()}")
                }
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(context.getString(CR.string.bpm))
                }
            }
            
            val axisRange = ChartData.AxisRange(
                minX = fromWeekStart.toEpochDay().toDouble(),
                maxX = toWeekStart.toEpochDay().toDouble(),
                minY = minY,
                maxY = maxY
            )
            
            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_minimum_heart_rate),
                axisRange = axisRange,
                entries = entriesImmutable,
                value = value
            )
            
            return ChartData(
                chartGranularity = ChartGranularity.SIX_MONTHS,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.RESTING_HEART_RATE,
                colorIndicator = null,
            )
        }
    }
    
    private inner class MonthlyRestingHrSeriesStrategy : RestingHrDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>
        ): ChartData {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailyRestingHrs.indices) {
                dailyData.add(currentDate to dailyRestingHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByMonth = dailyData.groupBy { (date, _) ->
                date.withDayOfMonth(1)
            }
            
            val months = dataByMonth.keys.sortedBy { it.epochMonth }
            months.forEach { monthStartDate ->
                val monthData = dataByMonth[monthStartDate] ?: emptyList()
                
                val monthHrValues = monthData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE && hr > 0) hr else null }
                
                if (monthHrValues.isNotEmpty()) {
                    val monthMinHr = monthHrValues.minOrNull() ?: 0
                    
                    if (monthMinHr > 0) {
                        entries.add(ChartData.Entry(x = monthStartDate.epochMonth.toLong(), y = monthMinHr.toDouble()))
                        totalValue += monthMinHr
                        totalDays++
                    }
                }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            val value = buildAnnotatedString {
                withStyle(SpanStyle(fontSize = 24.sp)) {
                    append("${averageValue.roundToInt()}")
                }
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(context.getString(CR.string.bpm))
                }
            }
            
            val axisRange = ChartData.AxisRange(
                minX = from.epochMonth.toDouble(),
                maxX = to.epochMonth.toDouble(),
                minY = minY,
                maxY = maxY
            )
            
            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_minimum_heart_rate),
                axisRange = axisRange,
                entries = entriesImmutable,
                value = value
            )
            
            return ChartData(
                chartGranularity = ChartGranularity.YEARLY,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.RESTING_HEART_RATE,
                colorIndicator = null,
            )
        }
    }
    
    private fun createSeries(
        color: Int,
        axisRange: ChartData.AxisRange,
        entries: kotlinx.collections.immutable.ImmutableList<ChartData.Entry>,
        value: AnnotatedString = AnnotatedString("")
    ): ChartData.Series {
        val lineChartConfig = LineChartConfig(
            isSmoothCurve = false,
            isPointFilled = false,
            showAreaFill = true,
            showPoints = true,
            pointSizeDP = 8f,
        )
        return ChartData.Series(
            chartType = ChartType.LINE,
            color = color,
            axisRange = axisRange,
            entries = entries,
            value = value,
            candlestickEntries = persistentListOf(),
            lineConfig = lineChartConfig,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }
} 
