package com.stt.android.diary.trainingv2.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.modifiers.rememberColumnNestScrollConnection
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.insights.coach.TrainingCoach
import com.stt.android.diary.insights.coach.TrainingCoachUiState
import com.stt.android.diary.insights.impact.ImpactUiState
import com.stt.android.diary.insights.intensity.IntensityType
import com.stt.android.diary.insights.intensity.IntensityUiState
import com.stt.android.diary.insights.volume.TrainingHighlighted
import com.stt.android.diary.insights.volume.TrainingVolumeUiState
import com.stt.android.diary.insights.volume.VolumeWorkoutSummaryType
import com.stt.android.diary.summary.composables.OverlappingRow
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.R
import kotlin.math.roundToInt

@Composable
internal fun TrainingScreen(
    isLoading: Boolean,
    graphTimeRange: GraphTimeRange,
    onGraphTimeRangeToggled: (GraphTimeRange) -> Unit,
    hasNext: Boolean,
    hasPrevious: Boolean,
    trainingDateRange: TrainingDateRange,
    onNextClick: () -> Unit,
    onPreviousClick: () -> Unit,
    selectedActivityGroupings: List<CoreActivityGrouping>,
    selectedActivityTypes: List<CoreActivityType>,
    allDoneActivityTypes: List<CoreActivityType>,
    onSelectSportsClick: () -> Unit,
    trainingCoachUiState: TrainingCoachUiState,
    trainingVolumeUiState: TrainingVolumeUiState,
    onVolumeWorkoutSummaryClick: (VolumeWorkoutSummaryType) -> Unit,
    onVolumeInfoClick: () -> Unit,
    trainingChartHighlighted: TrainingHighlighted?,
    trainingVolumeChartHighlightedEvent: (Long?) -> Unit,
    intensityUiState: IntensityUiState,
    onIntensityInfoClick: () -> Unit,
    onIntensityTypeClick: (IntensityType) -> Unit,
    impactUiState: ImpactUiState,
    onImpactInfoClick: (WorkoutImpactType) -> Unit,
    onWorkoutImpactTypeClick: (WorkoutImpactType) -> Unit,
    onTrainingModelInfoClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var trainingHighlightedViewHeight by remember { mutableIntStateOf(0) }
    var trainingChartPositionInRootY by remember { mutableFloatStateOf(0f) }
    var containerPositionInRootY by remember { mutableFloatStateOf(0f) }
    val columnListState = rememberLazyListState()
    var containerHeightPx by remember { mutableIntStateOf(0) }
    var datePickerHeightPx by remember { mutableIntStateOf(0) }
    val density = LocalDensity.current
    val contentHeightDp by remember(containerHeightPx, datePickerHeightPx) {
        mutableStateOf(density.run { (containerHeightPx - datePickerHeightPx).toDp() })
    }
    val containerListState = rememberScrollState()
    val containerNestedScrollConnection = rememberColumnNestScrollConnection(containerListState)

    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                outerBackgroundColor = MaterialTheme.colors.background,
                backgroundColor = MaterialTheme.colors.surface,
            )
            .onGloballyPositioned {
                containerPositionInRootY = it.boundsInRoot().top
                containerHeightPx = it.size.height
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(containerNestedScrollConnection)
                .verticalScroll(containerListState)
        ) {
            M3AppTheme {
                TrainingSegmentedControl(
                    timeRange = graphTimeRange,
                    onTimeRangeToggled = onGraphTimeRangeToggled,
                )
            }

            Column(
                modifier = Modifier
                    .onSizeChanged { size ->
                        datePickerHeightPx = size.height
                    }
            ) {
                TrainingDatePicker(
                    trainingDateRange = trainingDateRange,
                    onNextClick = onNextClick,
                    onPreviousClick = onPreviousClick,
                    isNextEnabled = hasNext,
                    isPreviousEnabled = hasPrevious,
                    isLoading = isLoading
                )
                if (columnListState.canScrollBackward) {
                    Divider(color = MaterialTheme.colors.lightGrey)
                }
            }

            LazyColumn(
                state = columnListState,
                modifier = Modifier
                    .height(contentHeightDp)
            ) {
                // The chart will affect the scrolling of the LazyColumn, so assemble the following into an item
                item(key = "training_container") {
                    Column(
                        modifier = Modifier.padding(bottom = MaterialTheme.spacing.xlarge)
                    ) {
                        if (trainingCoachUiState.trainingPhrasesIds.isNotEmpty()) {
                            TrainingCoach(
                                phrasesIds = trainingCoachUiState.trainingPhrasesIds,
                                forceExpanded = false,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(MaterialTheme.spacing.medium),
                            )
                            Spacer(modifier = Modifier.padding(MaterialTheme.spacing.small))
                        }

                        TrainingSportsFilter(
                            selectedActivityGroupings = selectedActivityGroupings,
                            allDoneActivityTypes = allDoneActivityTypes,
                            onSelectSportsClick = onSelectSportsClick,
                        )

                        TrainingVolume(
                            trainingVolumeUiState = trainingVolumeUiState,
                            onShowInfoClick = onVolumeInfoClick,
                            onWorkoutSummaryClick = onVolumeWorkoutSummaryClick,
                            onChartPositionInRootYChanged = { trainingChartPositionInRootY = it },
                            chartHighlightedEvent = trainingVolumeChartHighlightedEvent,
                        )

                        TrainingIntensityZone(
                            intensityUiState = intensityUiState,
                            onIntensityTypeClick = onIntensityTypeClick,
                            onShowInfoClicked = onIntensityInfoClick,
                            intensityCoachPhrasesIds = trainingCoachUiState.intensityPhrasesIds,
                            selectedActivityTypes = selectedActivityTypes,
                            allDoneActivityTypes = allDoneActivityTypes,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.large)
                        )

                        TrainingImpact(
                            impactUiState = impactUiState,
                            onWorkoutImpactTypeClick = onWorkoutImpactTypeClick,
                            onShowInfoClicked = onImpactInfoClick,
                            onTrainingModelInfoClicked = onTrainingModelInfoClick,
                            impactCoachPhraseIds = trainingCoachUiState.impactPhrasesIds,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.large)
                        )
                    }
                }
            }
        }

        TrainingHighlightedView(
            highlighted = trainingChartHighlighted,
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (trainingChartPositionInRootY - containerPositionInRootY - trainingHighlightedViewHeight)
                            .roundToInt().coerceAtLeast(0)
                    )
                }
                .onSizeChanged { trainingHighlightedViewHeight = it.height },
        )

    }
}

@Composable
private fun TrainingSportsFilter(
    selectedActivityGroupings: List<CoreActivityGrouping>,
    allDoneActivityTypes: List<CoreActivityType>,
    onSelectSportsClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val isAllSelected = remember(selectedActivityGroupings) { selectedActivityGroupings.isEmpty() }
    val title = if (isAllSelected) {
        stringResource(R.string.training_v2_training_all_sports)
    } else if (selectedActivityGroupings.size == 1) {
        stringResource(selectedActivityGroupings.first().nameRes)
    } else {
        stringResource(R.string.training_v2_training_multiple_sports)
    }
    TextButton(
        onClick = onSelectSportsClick,
        modifier = modifier
            .background(MaterialTheme.colors.surface),
        shape = RectangleShape,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMegaBold,
                    color = MaterialTheme.colors.onSurface
                )
                Icon(
                    painter = painterResource(R.drawable.ic_trend_down),
                    contentDescription = null,
                    tint = MaterialTheme.colors.onSurface,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.small)
                )
            }

            ActivityTypeRow(
                selectedActivityGroupings = if (isAllSelected) allDoneActivityTypes else selectedActivityGroupings,
            )
        }
    }
}

@Composable
private fun ActivityTypeRow(
    selectedActivityGroupings: List<CoreActivityGrouping>,
    modifier: Modifier = Modifier,
    maxIconCount: Int = 5
) {
    if (selectedActivityGroupings.isEmpty()) return
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically
    ) {
        OverlappingRow(
            overlapFactor = 5f / 6
        ) {
            selectedActivityGroupings.take(maxIconCount).forEach { sport ->
                if (sport is CoreActivityGroup) {
                    SuuntoActivityIcon(
                        iconRes = sport.icon,
                        tint = MaterialTheme.colors.surface,
                        background = colorResource(sport.activityTypes.first().color),
                        iconSize = MaterialTheme.iconSizes.small,
                    )
                } else if (sport is CoreActivityType) {
                    SuuntoActivityIcon(
                        coreActivityType = sport,
                        iconSize = MaterialTheme.iconSizes.small
                    )
                }
            }
        }
        Text(
            text = if (selectedActivityGroupings.size < maxIconCount) {
                ""
            } else {
                "+${selectedActivityGroupings.size - maxIconCount}"
            },
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.onSurface
        )
    }
}
