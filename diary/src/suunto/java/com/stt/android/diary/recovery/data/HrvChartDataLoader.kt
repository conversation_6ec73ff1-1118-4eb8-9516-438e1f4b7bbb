package com.stt.android.diary.recovery.data

import android.content.Context
import android.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.controllers.UserSettingsController
import com.stt.android.diary.recovery.v2.CommonChartContributor
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

class HrvChartDataLoader @Inject constructor(
    private val context: Context,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val userSettingsController: UserSettingsController
) {
    companion object {

        private val SUPPORTED_GRANULARITIES = setOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.MONTHLY,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY
        )
    }

    private val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek

    fun loadChartContributor(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Flow<CommonChartContributor> {
        if (chartGranularity !in SUPPORTED_GRANULARITIES) {
            throw IllegalArgumentException("This loader does not support ChartGranularity.$chartGranularity")
        }
        
        val today = LocalDate.now()
        val normalRangeFrom = today.minusDays(60)
        
        return combine(
            fetchSleepHrvUseCase.fetchAvgHrv(from, to),
            fetchSleepHrvUseCase.fetchAvgHrv(from = normalRangeFrom, to = today)
        ) { hrvList, normalRangeHrvList ->
            val chartData = seriesStrategies[chartGranularity]?.createChartData(chartGranularity, from, to, hrvList, normalRangeHrvList)
                ?: DailyHrvSeriesStrategy().createChartData(chartGranularity, from, to, hrvList, normalRangeHrvList)
            
            CommonChartContributor(
                value = chartData.series.firstOrNull()?.value ?: AnnotatedString("--"),
                valueType = context.getString(R.string.recovery_daily_average),
                chartData = chartData,
                contributorType = ContributorType.HRV
            )
        }
    }


    private fun getHrvGradeColor(hrvValue: Float?, normalRange: ClosedFloatingPointRange<Float>?): Int {
        if (hrvValue == null || normalRange == null) {
            return context.getColor(BaseR.color.dashboard_widget_hrv)
        }
        
        return when {
            hrvValue.roundToInt() > normalRange.endInclusive.roundToInt() -> context.getColor(CR.color.dark_yellow)
            hrvValue.roundToInt() < normalRange.start.roundToInt() -> context.getColor(BaseR.color.alert_color_red)
            else -> context.getColor(BaseR.color.dashboard_widget_hrv)
        }
    }

    private fun createColorWithAlpha(color: Int): Int {
        return Color.argb(26, Color.red(color), Color.green(color), Color.blue(color))
    }

    private fun roundUpToNearest10(value: Float): Int {
        return ((value + 9) / 10).toInt() * 10
    }

    private fun calculateAxisRange(
        entryYValues: List<Float>,
        normalRange: ClosedFloatingPointRange<Float>?,
        minX: Double,
        maxX: Double
    ): ChartData.AxisRange {
        if (entryYValues.isEmpty() && normalRange == null) {
            return ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = 60.0
            )
        }

        val entryMaxY = entryYValues.maxOrNull()
        val normalMaxY = normalRange?.endInclusive

        val dataMaxY = listOfNotNull(entryMaxY, normalMaxY).maxOrNull() ?: 160f

        val roundedMinY = 0
        val roundedMaxY = roundUpToNearest10(dataMaxY)

        val rangeInterval = roundUpToNearest10((roundedMaxY - roundedMinY).toFloat() / 3f)

        val axisMaxY = roundedMinY + rangeInterval * 3

        return ChartData.AxisRange(
            minX = minX,
            maxX = maxX,
            minY = roundedMinY.toDouble(),
            maxY = axisMaxY.toDouble()
        )
    }

    private interface HrvDataSeriesStrategy {
        fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            hrvList: List<SleepHrv>,
            normalRangeHrvList: List<SleepHrv>
        ): ChartData
    }
    
    private val seriesStrategies: Map<ChartGranularity, HrvDataSeriesStrategy> = mapOf(
        ChartGranularity.WEEKLY to DailyHrvSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DailyHrvSeriesStrategy(),
        ChartGranularity.MONTHLY to DailyHrvSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DailyHrvSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyHrvSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyHrvSeriesStrategy()
    )
    
    private inner class DailyHrvSeriesStrategy : HrvDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            hrvList: List<SleepHrv>,
            normalRangeHrvList: List<SleepHrv>
        ): ChartData {
            val entries = buildList {
                if (hrvList.isNotEmpty()) {
                    hrvList.forEach { hrv ->
                        val value = hrv.avgHrv
                        if (value != null && value > 0) {
                            add(
                                ChartData.Entry(
                                    x = hrv.date.toEpochDay(),
                                    y = value
                                )
                            )
                        }
                    }
                }
            }
            
            val fixedNormalRange = normalRangeHrvList.maxByOrNull { it.date }?.normalRange
            
            val gradientEntries = buildList {
                if (hrvList.isNotEmpty()) {
                    hrvList.forEach { hrv ->
                        val value = hrv.avgHrv
                        if (value != null && value > 0) {
                            val color = getHrvGradeColor(value, fixedNormalRange)
                            add(
                                ChartData.GradientLineEntry(
                                    x = hrv.date.toEpochDay(),
                                    y = value,
                                    color = color
                                )
                            )
                        }
                    }
                }
            }

            val value = if (entries.isEmpty()) {
                AnnotatedString("--")
            } else {
                val avgValue = entries.map { it.y.toFloat() }.average().toFloat()
                buildAnnotatedString {
                    withStyle(SpanStyle(fontSize = 24.sp)) {
                        append("${avgValue.roundToInt()}")
                    }
                    withStyle(SpanStyle(fontSize = 12.sp)) {
                        append(" ")
                        append(context.getString(CR.string.ms))
                    }
                }
            }

            val axisRange = calculateAxisRange(
                entryYValues = entries.map { it.y.toFloat() },
                normalRange = fixedNormalRange,
                minX = from.toEpochDay().toDouble(),
                maxX = to.toEpochDay().toDouble()
            )

            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_hrv),
                axisRange = axisRange,
                entries = persistentListOf(),
                value = value,
                candlestickEntries = persistentListOf(),
                gradientEntries = gradientEntries.toImmutableList(),
                backgroundRegion = createBackgroundRegionFromNormalRanges(normalRangeHrvList, from, to),
            )

            return ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.HRV,
                colorIndicator = null,
            )
        }
    }
    
    private inner class WeeklyHrvSeriesStrategy : HrvDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            hrvList: List<SleepHrv>,
            normalRangeHrvList: List<SleepHrv>
        ): ChartData {
            val weeklyData = buildMap<Long, List<SleepHrv>> {
                hrvList.forEach { hrv ->
                    val weekStart = hrv.date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    val weekKey = weekStart.toEpochDay()
                    getOrPut(weekKey) { mutableListOf() }.also { 
                        (it as MutableList<SleepHrv>).add(hrv)
                    }
                }
            }
            
            val fixedNormalRange = normalRangeHrvList.maxByOrNull { it.date }?.normalRange
            
            val gradientEntries = buildList {
                if (weeklyData.isNotEmpty()) {
                    weeklyData.forEach { (weekStart, weekHrvs) ->
                        val avgHrvValues = weekHrvs.mapNotNull { it.avgHrv }.filter { it > 0 }
                        if (avgHrvValues.isNotEmpty()) {
                            val avgValue = avgHrvValues.average().toFloat()
                            val color = getHrvGradeColor(avgValue, fixedNormalRange)
                            
                            add(
                                ChartData.GradientLineEntry(
                                    x = weekStart,
                                    y = avgValue,
                                    color = color
                                )
                            )
                        }
                    }
                }
            }

            val value = if (gradientEntries.isEmpty()) {
                AnnotatedString("--")
            } else {
                val avgValue = gradientEntries.map { it.y.toFloat() }.average().toFloat()
                buildAnnotatedString {
                    withStyle(SpanStyle(fontSize = 24.sp)) {
                        append("${avgValue.roundToInt()}")
                    }
                    withStyle(SpanStyle(fontSize = 12.sp)) {
                        append(" ")
                        append(context.getString(CR.string.ms))
                    }
                }
            }

            val axisRange = calculateAxisRange(
                entryYValues = gradientEntries.map { it.y.toFloat() },
                normalRange = fixedNormalRange,
                minX = from.toEpochDay().toDouble(),
                maxX = to.toEpochDay().toDouble()
            )

            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_hrv),
                axisRange = axisRange,
                entries = persistentListOf(),
                value = value,
                candlestickEntries = persistentListOf(),
                gradientEntries = gradientEntries.toImmutableList(),
                backgroundRegion = createWeeklyBackgroundRegion(normalRangeHrvList, from, to),
            )

            return ChartData(
                chartGranularity = ChartGranularity.SIX_MONTHS,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.HRV,
                colorIndicator = null,
            )
        }
    }
    
    private inner class MonthlyHrvSeriesStrategy : HrvDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            hrvList: List<SleepHrv>,
            normalRangeHrvList: List<SleepHrv>
        ): ChartData {
            val monthlyData = buildMap<Long, List<SleepHrv>> {
                hrvList.forEach { hrv ->
                    val month = hrv.date.epochMonth.toLong()
                    getOrPut(month) { mutableListOf() }.also { 
                        (it as MutableList<SleepHrv>).add(hrv)
                    }
                }
            }
            
            val fixedNormalRange = normalRangeHrvList.maxByOrNull { it.date }?.normalRange
            
            val gradientEntries = buildList {
                if (monthlyData.isNotEmpty()) {
                    monthlyData.forEach { (month, monthHrvs) ->
                        val avgHrvValues = monthHrvs.mapNotNull { it.avgHrv }.filter { it > 0 }
                        if (avgHrvValues.isNotEmpty()) {
                            val avgValue = avgHrvValues.average().toFloat()
                            val color = getHrvGradeColor(avgValue, fixedNormalRange)
                            
                            add(
                                ChartData.GradientLineEntry(
                                    x = month,
                                    y = avgValue,
                                    color = color
                                )
                            )
                        }
                    }
                }
            }

            val value = if (gradientEntries.isEmpty()) {
                AnnotatedString("--")
            } else {
                val avgValue = gradientEntries.map { it.y.toFloat() }.average().toFloat()
                buildAnnotatedString {
                    withStyle(SpanStyle(fontSize = 24.sp)) {
                        append("${avgValue.roundToInt()}")
                    }
                    withStyle(SpanStyle(fontSize = 12.sp)) {
                        append(" ")
                        append(context.getString(CR.string.ms))
                    }
                }
            }

            val axisRange = calculateAxisRange(
                entryYValues = gradientEntries.map { it.y.toFloat() },
                normalRange = fixedNormalRange,
                minX = from.epochMonth.toDouble(),
                maxX = to.epochMonth.toDouble()
            )

            val series = createSeries(
                color = context.getColor(BaseR.color.dashboard_widget_hrv),
                axisRange = axisRange,
                entries = persistentListOf(),
                value = value,
                candlestickEntries = persistentListOf(),
                gradientEntries = gradientEntries.toImmutableList(),
                backgroundRegion = createMonthlyBackgroundRegion(normalRangeHrvList, from, to),
            )

            return ChartData(
                chartGranularity = ChartGranularity.YEARLY,
                series = persistentListOf(series),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = ChartContent.HRV,
                colorIndicator = null,
            )
        }
    }
    
    private fun createSeries(
        color: Int,
        axisRange: ChartData.AxisRange,
        entries: ImmutableList<ChartData.Entry>,
        value: AnnotatedString = AnnotatedString(""),
        candlestickEntries: ImmutableList<ChartData.CandlestickEntry> = persistentListOf(),
        gradientEntries: ImmutableList<ChartData.GradientLineEntry> = persistentListOf(),
        backgroundRegion: ChartData.BackgroundRegion? = null,
    ): ChartData.Series {
        val lineChartConfig = LineChartConfig(
            isSmoothCurve = false,
            isPointFilled = false,
            showAreaFill = false,
            showPoints = true,
            pointSizeDP = 8f,
        )
        return ChartData.Series(
            chartType = ChartType.LINE,
            color = color,
            axisRange = axisRange,
            entries = entries,
            value = value,
            candlestickEntries = candlestickEntries,
            lineConfig = lineChartConfig,
            gradientEntries = gradientEntries,
            backgroundRegion = backgroundRegion,
            groupStackBarStyle = null,
        )
    }
    
    private fun createBackgroundRegionFromNormalRanges(
        normalRangeHrvList: List<SleepHrv>,
        from: LocalDate,
        to: LocalDate
    ): ChartData.BackgroundRegion? {
        val latestHrv = normalRangeHrvList.maxByOrNull { it.date }
        val normalRange = latestHrv?.normalRange
        
        return if (normalRange != null) {
            val xValues = mutableListOf<Long>()
            var currentDate = from
            while (!currentDate.isAfter(to)) {
                xValues.add(currentDate.toEpochDay())
                currentDate = currentDate.plusDays(1)
            }
            
            val backgroundRegionEntries = xValues.map { x ->
                ChartData.BackgroundRegionEntry(
                    x = x,
                    startY = normalRange.start,
                    endY = normalRange.endInclusive
                )
            }
            
            val baseColor = context.getColor(BaseR.color.dashboard_widget_hrv)
            val alphaColor = createColorWithAlpha(baseColor)
            ChartData.BackgroundRegion(
                backgroundRegions = backgroundRegionEntries.toImmutableList(),
                backgroundColorInt = alphaColor
            )
        } else {
            null
        }
    }

    private fun createWeeklyBackgroundRegion(
        normalRangeHrvList: List<SleepHrv>,
        from: LocalDate,
        to: LocalDate
    ): ChartData.BackgroundRegion? {
        val latestHrv = normalRangeHrvList.maxByOrNull { it.date }
        val normalRange = latestHrv?.normalRange
        
        return if (normalRange != null) {
            val xValues = mutableListOf<Long>()
            var currentDate = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            val endDate = to.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            
            while (!currentDate.isAfter(endDate)) {
                xValues.add(currentDate.toEpochDay())
                currentDate = currentDate.plusWeeks(1)
            }
            
            val backgroundRegionEntries = xValues.map { x ->
                ChartData.BackgroundRegionEntry(
                    x = x,
                    startY = normalRange.start,
                    endY = normalRange.endInclusive
                )
            }
            
            val baseColor = context.getColor(BaseR.color.dashboard_widget_hrv)
            val alphaColor = createColorWithAlpha(baseColor)
            ChartData.BackgroundRegion(
                backgroundRegions = backgroundRegionEntries.toImmutableList(),
                backgroundColorInt = alphaColor
            )
        } else {
            null
        }
    }

    private fun createMonthlyBackgroundRegion(
        normalRangeHrvList: List<SleepHrv>,
        from: LocalDate,
        to: LocalDate
    ): ChartData.BackgroundRegion? {
        val latestHrv = normalRangeHrvList.maxByOrNull { it.date }
        val normalRange = latestHrv?.normalRange
        
        return if (normalRange != null) {
            val xValues = mutableListOf<Long>()
            var currentDate = from.withDayOfMonth(1)
            val endDate = to.withDayOfMonth(1)
            
            while (!currentDate.isAfter(endDate)) {
                xValues.add(currentDate.epochMonth.toLong())
                currentDate = currentDate.plusMonths(1)
            }
            
            val backgroundRegionEntries = xValues.map { x ->
                ChartData.BackgroundRegionEntry(
                    x = x,
                    startY = normalRange.start,
                    endY = normalRange.endInclusive
                )
            }
            
            val baseColor = context.getColor(BaseR.color.dashboard_widget_hrv)
            val alphaColor = createColorWithAlpha(baseColor)
            ChartData.BackgroundRegion(
                backgroundRegions = backgroundRegionEntries.toImmutableList(),
                backgroundColorInt = alphaColor
            )
        } else {
            null
        }
    }
} 
