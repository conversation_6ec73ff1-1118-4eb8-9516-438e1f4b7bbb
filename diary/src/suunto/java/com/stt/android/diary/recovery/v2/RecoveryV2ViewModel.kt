package com.stt.android.diary.recovery.v2

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.usecases.CalculateDateRangeUseCase
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.diary.recovery.usecases.CreateRecoveryDatePickerDataUseCase
import com.stt.android.diary.recovery.usecases.FetchRecoveryChartDataUseCase
import com.stt.android.diary.recovery.usecases.FetchDailyRecoveryDataUseCase
import com.stt.android.diary.recovery.usecases.LoadSleepComparisonChartDataUseCase
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.home.diary.InfoBottomSheet
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class RecoveryV2ViewModel @Inject constructor(
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val fetchRecoveryChartDataUseCase: FetchRecoveryChartDataUseCase,
    private val createDatePickerDataUseCase: CreateRecoveryDatePickerDataUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val loadSleepComparisonChartDataUseCase: LoadSleepComparisonChartDataUseCase,
    private val loadDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
) : ViewModel() {
    private val _currentDate = MutableStateFlow(LocalDate.now())
    
    private val _currentTimeGranularity = MutableStateFlow(ChartGranularity.DAILY)
    
    private val _leftSleepSelectionType = MutableStateFlow(SleepChartSelectionType.TOTAL_TIME)
    private val _rightSleepSelectionType = MutableStateFlow(SleepChartSelectionType.WAKE_UP_RESOURCES)
    
    private val _onShowRecoveryStateInfoSheet = MutableStateFlow<((InfoBottomSheet) -> Unit)?>(null)
    private val _onContributorClick = MutableStateFlow<((RecoveryStateContributor) -> Unit)?>(null)
    
    private val _viewState = MutableStateFlow<RecoveryV2State>(RecoveryV2State.Loading)
    
    val viewState: StateFlow<RecoveryV2State> = _viewState.asStateFlow()
    
    init {
        combine(
            _currentDate,
            _currentTimeGranularity,
            ::updateViewData
        ).flowOn(coroutinesDispatchers.io)
        .launchIn(viewModelScope)
    }
    
    private suspend fun updateViewData(
        date: LocalDate,
        currentTimeGranularity: ChartGranularity
    ) {
        _viewState.update { current ->
            if (current is RecoveryV2State.Loaded) {
                current.copy(isLoading = true)
            } else {
                current
            }
        }
        
        val timeRange = calculateDateRangeUseCase(
            chartGranularity = currentTimeGranularity,
            chartPageIndex = 0,
            chartPageCount = 1,
            today = date
        )
        
        val datePickerData = createDatePickerDataUseCase(date, currentTimeGranularity)
        val (mainTimeGranularities, extraTimeGranularities) = getDefaultTimeGranularities()
        
        if (currentTimeGranularity == ChartGranularity.DAILY) {
            val dailyData = loadDailyRecoveryDataUseCase(
                fromDate = timeRange.start,
                toDate = timeRange.endInclusive
            ).first()

            _viewState.value = RecoveryV2State.Loaded(
                mainTimeGranularities = mainTimeGranularities,
                extraTimeGranularities = extraTimeGranularities,
                currentTimeGranularity = currentTimeGranularity,
                datePickerData = datePickerData,
                isLoading = false,
                recoveryDailyStateData = dailyData,
                recoveryChartStateData = RecoveryChartStateData.None
            )
        } else {
            _viewState.value = RecoveryV2State.Loaded(
                mainTimeGranularities = mainTimeGranularities,
                extraTimeGranularities = extraTimeGranularities,
                currentTimeGranularity = currentTimeGranularity,
                datePickerData = datePickerData,
                isLoading = true,
                recoveryDailyStateData = RecoveryDailyStateData.None,
                recoveryChartStateData = createDefaultRecoveryChartStateLoaded()
            )
            
            val chartStateData = fetchRecoveryChartDataUseCase(
                chartGranularity = currentTimeGranularity,
                from = timeRange.start,
                to = timeRange.endInclusive,
                leftSleepSelectionType = _leftSleepSelectionType.value,
                rightSleepSelectionType = _rightSleepSelectionType.value,
                displayTitle = datePickerData.displayTitle
            ).first()
            
            _viewState.update { current ->
                if (current is RecoveryV2State.Loaded) {
                    current.copy(
                        recoveryChartStateData = chartStateData,
                        isLoading = false
                    )
                } else {
                    current
                }
            }
        }
    }
    
    private suspend fun updateSleepChartData(
        leftSleepSelectionType: SleepChartSelectionType,
        rightSleepSelectionType: SleepChartSelectionType,
        date: LocalDate,
        currentTimeGranularity: ChartGranularity
    ) {
        if (currentTimeGranularity == ChartGranularity.DAILY) return
        
        val timeRange = calculateDateRangeUseCase(
            chartGranularity = currentTimeGranularity,
            chartPageIndex = 0,
            chartPageCount = 1,
            today = date
        )
        
        val datePickerData = createDatePickerDataUseCase(date, currentTimeGranularity)
        
        val updatedSleepComparisonChartData = loadSleepComparisonChartDataUseCase(
            chartGranularity = currentTimeGranularity,
            leftSelectionType = leftSleepSelectionType,
            rightSelectionType = rightSleepSelectionType,
            from = timeRange.start,
            to = timeRange.endInclusive,
            timeRange = datePickerData.displayTitle
        ).first()
        
        _viewState.update { current ->
            if (current !is RecoveryV2State.Loaded) return@update current
            
            val currentChartState = current.recoveryChartStateData as? RecoveryChartStateData.Loaded
                ?: return@update current
            
            val updatedChartStateData = currentChartState.copy(
                recoveryChartContributors = currentChartState.recoveryChartContributors?.copy(
                    sleepComparisonChartData = updatedSleepComparisonChartData
                )
            )
            
            current.copy(recoveryChartStateData = updatedChartStateData)
        }
    }

    fun onEvent(event: RecoveryV2Event) {
        when (event) {
            is RecoveryV2Event.NavigatePrevious -> onPreviousClick()
            is RecoveryV2Event.NavigateNext -> onNextClick()
            
            is RecoveryV2Event.UpdateTimeGranularity -> {
                _currentTimeGranularity.value = event.timeGranularity
                _currentDate.value = LocalDate.now()
            }

            is RecoveryV2Event.ShowRecoveryStateInfoSheet -> {
                _onShowRecoveryStateInfoSheet.value?.invoke(event.infoBottomSheet)
            }
            is RecoveryV2Event.ContributorClicked -> {
                _onContributorClick.value?.invoke(event.contributor)
            }
            
            is RecoveryV2Event.ShowSleepComparisonPrimaryGraphSelection -> {
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = true)
                    } else it
                }
            }
            is RecoveryV2Event.HideSleepComparisonPrimaryGraphSelection -> {
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = false)
                    } else it
                }
            }
            is RecoveryV2Event.ShowSleepComparisonSecondaryGraphSelection -> {
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = true)
                    } else it
                }
            }
            is RecoveryV2Event.HideSleepComparisonSecondaryGraphSelection -> {
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = false)
                    } else it
                }
            }
            is RecoveryV2Event.UpdateSleepLeftSelectionType -> {
                _leftSleepSelectionType.value = event.type
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = false)
                    } else it
                }
                viewModelScope.launch {
                    updateSleepChartData(
                        leftSleepSelectionType = event.type,
                        rightSleepSelectionType = _rightSleepSelectionType.value,
                        date = _currentDate.value,
                        currentTimeGranularity = _currentTimeGranularity.value
                    )
                }
            }
            is RecoveryV2Event.UpdateSleepRightSelectionType -> {
                _rightSleepSelectionType.value = event.type
                _viewState.update { 
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = false)
                    } else it
                }
                viewModelScope.launch {
                    updateSleepChartData(
                        leftSleepSelectionType = _leftSleepSelectionType.value,
                        rightSleepSelectionType = event.type,
                        date = _currentDate.value,
                        currentTimeGranularity = _currentTimeGranularity.value
                    )
                }
            }
        }
    }
    
    fun setShowRecoveryStateInfoSheetCallback(callback: (InfoBottomSheet) -> Unit) {
        _onShowRecoveryStateInfoSheet.value = callback
    }
    
    fun setContributorClickCallback(callback: (RecoveryStateContributor) -> Unit) {
        _onContributorClick.value = callback
    }

    private fun onPreviousClick() {
        _currentDate.update { 
            when (_currentTimeGranularity.value) {
                ChartGranularity.DAILY -> it.minusDays(1)
                ChartGranularity.WEEKLY -> it.minusWeeks(1)
                ChartGranularity.MONTHLY -> it.minusMonths(1)
                ChartGranularity.SIX_MONTHS -> it.minusMonths(6)
                ChartGranularity.SEVEN_DAYS -> it.minusDays(7)
                ChartGranularity.THIRTY_DAYS -> it.minusDays(30)
                ChartGranularity.YEARLY -> it.minusYears(1)
                else -> it.minusDays(1)
            }
        }
    }

    private fun onNextClick() {
        _currentDate.update { 
            val newDate = when (_currentTimeGranularity.value) {
                ChartGranularity.DAILY -> it.plusDays(1)
                ChartGranularity.WEEKLY -> it.plusWeeks(1)
                ChartGranularity.MONTHLY -> it.plusMonths(1)
                ChartGranularity.SIX_MONTHS -> it.plusMonths(6)
                ChartGranularity.SEVEN_DAYS -> it.plusDays(7)
                ChartGranularity.THIRTY_DAYS -> it.plusDays(30)
                ChartGranularity.YEARLY -> it.plusYears(1)
                else -> it.plusDays(1)
            }
            if (newDate.isAfter(LocalDate.now())) LocalDate.now() else newDate
        }
    }
    

    private companion object {
        

        fun getDefaultTimeGranularities(): Pair<ImmutableList<ChartGranularity>, ImmutableList<ChartGranularity>> {
            val mainTimeGranularities = persistentListOf(
                ChartGranularity.DAILY, 
                ChartGranularity.WEEKLY, 
                ChartGranularity.MONTHLY,
                ChartGranularity.SIX_MONTHS,
            )
            val extraTimeGranularities = persistentListOf(
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.YEARLY
            )
            return Pair(mainTimeGranularities, extraTimeGranularities)
        }
        

        fun createDefaultRecoveryChartStateLoaded(): RecoveryChartStateData.Loaded {
            return RecoveryChartStateData.Loaded(
                recoveryChartData = RecoveryChartData(
                    recoveryScore = null,
                    recoveryZone = null,
                    recoveryChart = null
                ),
                recoveryChartContributors = RecoveryChartContributors(
                    sleepComparisonChartData = SleepComparisonChartData(
                        timeRange = null,
                        leftSelectionType = SleepChartSelectionType.SLEEP_DURATION,
                        leftValue = null,
                        rightSelectionType = SleepChartSelectionType.SLEEP_REGULARITY,
                        rightValue = null,
                        value = null,
                        valueType = null,
                        contributorType = ContributorType.SLEEP,
                        sleepChartData = null
                    ),
                    commonChartContributors = persistentListOf(
                        CommonChartContributor(
                            contributorType = ContributorType.HRV,
                            value = null,
                            valueType = null,
                            chartData = null,
                        ),
                        CommonChartContributor(
                            contributorType = ContributorType.REST_HR,
                            value = null,
                            valueType = null,
                            chartData = null,
                        ),
                        CommonChartContributor(
                            contributorType = ContributorType.RESOURCES,
                            value = null,
                            valueType = null,
                            chartData = null,
                        )
                    )
                )
            )
        }
    }
}
