package com.stt.android.diary.recovery.usecases

import com.soy.algorithms.recovery.calculateDailyScore
import com.soy.algorithms.recovery.calculateRecoveryStateScore
import com.stt.android.coroutines.combine
import com.stt.android.data.TimeUtils
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.diary.GetTrainingProgressDataUseCase
import com.stt.android.domain.diary.models.TrainingProgressData
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.restheartrate.FetchRestHeartRateUseCase
import com.stt.android.domain.restheartrate.RestHeartRate
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.domain.workouts.feeling.DailyFeeling
import com.stt.android.domain.workouts.feeling.FetchDailyFeelingUseCase
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.averageOrNull
import com.stt.android.utils.takeIfNotNaN
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class CalculateRecoveryScoreUseCase @Inject constructor(
    private val userSettingsDataSource: UserSettingsDataSource,
    private val currentUserDataSource: CurrentUserDataSource,
    private val sleepRepository: SleepRepository,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val trendDataRepository: TrendDataRepository,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val getTrainingProgressDataUseCase: GetTrainingProgressDataUseCase,
    private val fetchRestHeartRateUseCase: FetchRestHeartRateUseCase,
    private val fetchDailyFeelingUseCase: FetchDailyFeelingUseCase
) {
    companion object {
        private const val MIN_VALUES_FOR_7_DAY_AVG = 3
    }

    @IoThread
    operator fun invoke(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<LocalDate, Int>> {
        val today = LocalDate.now()
        val limitedToDate = if (toDate.isAfter(today)) today else toDate
        
        return combine(
            getSleepHrvListByDateFlow(fromDate, limitedToDate),
            getSleepListFlow(fromDate.minusDays(6), limitedToDate),
            getTrendDataListFlow(fromDate, limitedToDate),
            getProgressByDateFlow(fromDate, limitedToDate),
            getRestHeartRateByDateFlow(fromDate, limitedToDate),
            getRecoveryDataListFlow(fromDate, limitedToDate),
            getFeelingByDateFlow(fromDate, limitedToDate),
            getFeelingDistributionFlow(fromDate, limitedToDate)
        ) { sleepHrvList, sleepList, trendDataList, progressByDate, restHeartRateByDate, recoveryDataList, feelingByDate, feelingDistribution ->
            calculateDailyRecoveryScores(
                sleepHrvList, sleepList, trendDataList, progressByDate,
                restHeartRateByDate, recoveryDataList, feelingByDate, fromDate, limitedToDate
            )
        }
    }

    private fun calculateDailyRecoveryScores(
        sleepHrvList: Map<LocalDate, List<SleepHrv>>,
        sleepList: List<Sleep>,
        trendDataList: List<TrendData>,
        progressByDate: Map<LocalDate, TrainingProgressData>,
        restHeartRateByDate: Map<LocalDate, RestHeartRate>,
        recoveryDataList: List<RecoveryData>,
        feelingByDate: Map<LocalDate, DailyFeeling>,
        fromDate: LocalDate,
        toDate: LocalDate
    ): Map<LocalDate, Int> {
        val sleepListByDate = sleepList.groupBy { sleep -> sleep.timestamp.toLocalDate() }
        val trendDataListByDate = trendDataList.groupBy { trend -> trend.timestamp.toLocalDate() }
        val recoveryDataListByDate = recoveryDataList.groupBy { recovery -> 
            TimeUtils.epochToLocalZonedDateTime(recovery.timestamp).toLocalDate() 
        }
        
        val days = java.time.temporal.ChronoUnit.DAYS.between(fromDate, toDate).toInt()
        val result = mutableMapOf<LocalDate, Int>()
        
        for (plusDays in 0..days) {
            val date = fromDate.plusDays(plusDays.toLong())
            
            val last1DaySleep = sleepListByDate.getOrDefault(date, emptyList())
            val last7DaysSleep = (0..6).mapNotNull {
                sleepListByDate.getOrDefault(
                    date.minusDays(it.toLong()),
                    emptyList(),
                )
            }.flatten()
            
            val sleepHrv = sleepHrvList[date]?.firstOrNull()
            val hrvNormalRange = sleepHrv?.normalRange?.let { range ->
                range.start.roundToInt()..range.endInclusive.roundToInt()
            } ?: (0..0)
            
            val last1DayHrv = sleepHrv?.avgHrv?.roundToInt() ?: 0
            val last7DaysHrv = sleepHrv?.avg7DayHrv?.roundToInt() ?: 0
            
            val hrvBaselineAvailable = hrvNormalRange.first > 0 && hrvNormalRange.last > 0
            
            val last1DayRestHR = restHeartRateByDate[date]?.restHeartRate?.inBpm?.roundToInt() ?: 0
            
            val last7DaysRestHRList = (0..6).mapNotNull {
                restHeartRateByDate[date.minusDays(it.toLong())]
                    .takeIf { rhr -> (rhr?.restHeartRate?.inBpm?.roundToInt() ?: 0) > 0 }
            }
            
            val last7DaysRestHR =
                if (last7DaysRestHRList.size >= MIN_VALUES_FOR_7_DAY_AVG) {
                    last7DaysRestHRList.averageOfDouble { hrRate -> hrRate.restHeartRate.inBpm }
                        .takeIfNotNaN()
                        ?.roundToInt()
                        ?: 0
                } else {
                    0
                }
            
            val todayProgress = progressByDate[date]
            val yesterdayProgress = progressByDate[date.minusDays(1)]
            
            val feeling7Days = calculateAvgFeelingForTimeRange(feelingByDate, date, 7)
            val feeling42Days = calculateAvgFeelingForTimeRange(feelingByDate, date, 42)
            
            val dailyResources = recoveryDataListByDate
                .getOrDefault(date, emptyList())
                .map { recoveryData -> recoveryData.balance }
                .averageOrNull()
                ?.let { avg -> (avg * 100).roundToInt() }
                ?: 0
            
            val dailySteps = trendDataListByDate
                .getOrDefault(date, emptyList())
                .sumOf { trendData -> trendData.steps }
            
            val dailyScore = getDailyScore(
                dailyResources = dailyResources,
                dailySteps = dailySteps,
            )
            
            val sd1 = getAvgSleepDuration(last1DaySleep)
            val sd7 = getAvgSleepDuration(last7DaysSleep)
            val hrv1 = if (hrvBaselineAvailable) last1DayHrv else 0
            val hrv7 = if (hrvBaselineAvailable) last7DaysHrv else 0
            val tssy = yesterdayProgress?.exactTss?.roundToInt() ?: 0
            val tss1 = todayProgress?.exactTss?.roundToInt() ?: 0
            val tsb1 = todayProgress?.form?.roundToInt() ?: 0
            
            val score = calculateRecoveryStateScore(
                SD1 = sd1,
                SD7 = sd7,
                RHR1 = last1DayRestHR,
                RHR7 = last7DaysRestHR,
                HRV1 = hrv1,
                HRV7 = hrv7,
                HRVL = hrvNormalRange.first,
                HRVU = hrvNormalRange.last,
                TSSY = tssy,
                TSS1 = tss1,
                ATL = todayProgress?.fatigue?.roundToInt() ?: 0,
                CTL = todayProgress?.fitness?.roundToInt() ?: 0,
                F7 = feeling7Days,
                F42 = feeling42Days,
                dailyScore = dailyScore,
            )
            
            if (score.suuntoScore > 0) {
                result[date] = score.suuntoScore
            }
        }
        
        return result
    }
    

    private fun getSleepHrvListByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<LocalDate, List<SleepHrv>>> = 
        fetchSleepHrvUseCase.fetchAvgHrv(
            from = fromDate,
            to = toDate,
        ).map { sleepHrvList ->
            sleepHrvList.groupBy { it.date }
        }.catch {
            Timber.w(it, "Failed to fetch sleep HRV data")
            emit(emptyMap())
        }

    private fun getSleepListFlow(fromDate: LocalDate, toDate: LocalDate): Flow<List<Sleep>> =
        sleepRepository.fetchSleeps(
            from = fromDate,
            to = toDate,
        ).catch {
            Timber.w(it, "Failed to fetch sleep data")
            emit(emptyList())
        }

    private fun getTrendDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<List<TrendData>> = 
        trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli(),
            toTimestamp = toDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli(),
            aggregated = false,
        ).catch {
            Timber.w(it, "Failed to fetch trend data")
            emit(emptyList())
        }

    private fun getRecoveryDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<List<RecoveryData>> = 
        fetchRecoveryDataUseCase.fetchRecoveryData(
            from = fromDate,
            to = toDate,
        ).catch {
            Timber.w(it, "Failed to fetch recovery data")
            emit(emptyList())
        }

    private fun getProgressByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<LocalDate, TrainingProgressData>> = flow {
        val username = currentUserDataSource.getCurrentUser().username
        val result = getTrainingProgressDataUseCase.invoke(
            GetTrainingProgressDataUseCase.Params(
                username = username,
                firstDay = fromDate.minusDays(1),
                lastDay = toDate,
                addZeroValuesBeforeFirstRecordedTssDate = false,
            )
        ).groupBy { it.day }.mapValues { it.value.first() }
        emit(result)
    }.catch {
        Timber.w(it, "Failed to fetch training progress data")
        emit(emptyMap())
    }

    private fun getRestHeartRateByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate,
    ): Flow<Map<LocalDate, RestHeartRate>> = 
        fetchRestHeartRateUseCase.fetchRestHeartRateForDateRange(fromDate.minusDays(6), toDate)
            .map { list ->
                list.groupBy { it.localDate }.mapValues { it.value.first() }
            }
            .catch {
                Timber.w(it, "Failed to fetch resting heart rate data")
                emit(emptyMap())
            }

    private fun getFeelingByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<LocalDate, DailyFeeling>> = 
        fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(fromDate.minusDays(41), toDate)
            .map { list ->
                list.groupBy { it.localDate }.mapValues { it.value.first() }
            }
            .catch {
                Timber.w(it, "Failed to fetch feeling data")
                emit(emptyMap())
            }

    private fun getFeelingDistributionFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<Int, Double>> = flow {
        try {
            val startDate = fromDate.minusDays(6)
            val dailyFeelings = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(
                startDate, toDate
            ).first()
            val rawFeelings = dailyFeelings.map { dailyFeeling -> dailyFeeling.averageValue.toInt() }
            val validFeelings = rawFeelings.filter { value -> value in 1..5 }
            val countMap = (1..5).associateWith { feeling ->
                validFeelings.count { value -> value == feeling }
            }
            val totalCount = validFeelings.size
            val result = if (totalCount > 0) {
                countMap.mapValues { entry -> 
                    val count = entry.value
                    val percentage = count * 100.0 / totalCount
                    (percentage * 10).roundToInt() / 10.0
                }
            } else {
                (1..5).associateWith { 20.0 }
            }
            emit(result)
        } catch (e: Exception) {
            Timber.w(e, "Calculate feeling distribution error, returning default 20%% values")
            emit((1..5).associateWith { 20.0 })
        }
    }
    

    private fun getDailyScore(dailyResources: Int, dailySteps: Int): Int {
        val userSettings = userSettingsDataSource.getUserSettings()
        val gender = if ("male".equals(userSettings.gender, true)) 1 else 0
        val age = calculateAge(userSettings.birthDate)
        val weightInKilogram = userSettings.weight / 1000.0
        val heightInMeter = userSettings.height / 100.0
        return calculateDailyScore(
            age = age,
            gender = gender,
            weightInKilogram = weightInKilogram,
            heightInMeter = heightInMeter,
            dailyResources = dailyResources,
            dailySteps = dailySteps,
        )
    }
    

    private fun calculateAge(birthDate: Long): Int {
        val now = LocalDate.now()
        val birthday = LocalDate.ofEpochDay(birthDate / TimeUnit.DAYS.toMillis(1))
        var age = now.year - birthday.year
        if (now.dayOfYear < birthday.dayOfYear) {
            age--
        }
        return age
    }

    private fun getAvgSleepDuration(sleepList: List<Sleep>): Double {
        val dailySleeps =
            sleepList.groupBy { sleep -> sleep.timestamp.toLocalDate() }
                .filter { (_, sleeps) -> sleeps.isNotEmpty() && sleeps.any { sleep -> sleep.hasLongSleep } }
        if (dailySleeps.none()) return 0.0
        val seconds = dailySleeps.flatMap { it.value }
            .sumOf { it.longSleep?.sleepDuration?.inWholeMilliseconds?.div(1000.0) ?: 0.0 }
        val avgSeconds = seconds / dailySleeps.size
        val hours = avgSeconds / TimeUnit.HOURS.toSeconds(1)
        return (hours * 10).roundToInt() / 10.0
    }
    

    private fun calculateAvgFeelingForTimeRange(
        feelingByDate: Map<LocalDate, DailyFeeling>,
        date: LocalDate,
        daysCount: Int
    ): Double {
        val feelingList = (0 until daysCount).mapNotNull {
            val feeling = feelingByDate[date.minusDays(it.toLong())]
            if ((feeling?.averageValue ?: 0.0) > 0) feeling else null
        }
        
        val avg = feelingList.flatMap { it.values }.average().takeIfNotNaN() ?: 0.0
        return (avg * 10).roundToInt() / 10.0
    }
} 
