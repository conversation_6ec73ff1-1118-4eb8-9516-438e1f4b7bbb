package com.stt.android.diary.recovery.data

import android.content.Context
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.diary.recovery.data.sleep.ChartDataCreator.Average
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.formatSleepTime
import com.stt.android.diary.recovery.data.sleep.RecoveryChartDataCreator
import com.stt.android.diary.recovery.data.sleep.SleepChartDataCreator
import com.stt.android.diary.recovery.data.sleep.SleepDataRepository
import com.stt.android.diary.recovery.data.sleep.WorkoutChartDataCreator
import com.stt.android.diary.recovery.model.SleepChartData
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.diary.recovery.v2.SleepComparisonChartData
import com.stt.android.domain.sleep.Sleep
import com.stt.android.home.diary.R
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToLong
import kotlin.time.Duration

class SleepComparisonChartDataLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val sleepDataRepository: SleepDataRepository,
    private val sleepChartDataCreator: SleepChartDataCreator,
    private val workoutChartDataCreator: WorkoutChartDataCreator,
    private val recoveryChartDataCreator: RecoveryChartDataCreator,
) {

    @OptIn(ExperimentalCoroutinesApi::class)
    fun loadComparisonChartData(
        chartGranularity: ChartGranularity,
        primaryGraphType: SleepChartSelectionType,
        secondaryGraphType: SleepChartSelectionType,
        from: LocalDate,
        to: LocalDate,
        timeRange: String,
    ): Flow<SleepComparisonChartData?> {
        return sleepDataRepository.sleepForDateRangeFlow(from, to).flatMapLatest { sleepList ->
            if (sleepList.isNotEmpty()) {
                combine(
                    createGraphData(sleepList, chartGranularity, primaryGraphType, from, to),
                    createGraphData(sleepList, chartGranularity, secondaryGraphType, from, to),
                ) { primaryGraphData, secondaryGraphData ->
                    val sleepChartData = SleepChartData(
                        chartGranularity = chartGranularity,
                        primarySeries = primaryGraphData!!.copy(selectionType = primaryGraphType),
                        secondarySeries = secondaryGraphData?.copy(selectionType = secondaryGraphType),
                    )
                    
                    val totalSleepAverage = Average()
                    sleepList.forEach { sleep ->
                        sleep.totalSleepDuration.takeIf { it > Duration.ZERO }
                            ?.inWholeSeconds
                            ?.toFloat()
                            ?.let { value ->
                                totalSleepAverage.feed(value)
                            }
                    }
                    val value = totalSleepAverage.result.roundToLong().formatSleepTime(context)
                    
                    val leftValue = ChartDataFormatterUtils.formatByGraphType(context, primaryGraphType, primaryGraphData.average)
                    val rightValue = secondaryGraphData?.let { 
                        ChartDataFormatterUtils.formatByGraphType(context, secondaryGraphType, it.average)
                    }
                    
                    SleepComparisonChartData(
                        value = value,
                        valueType = context.getString(
                            if (chartGranularity == ChartGranularity.WEEKLY) {
                                R.string.recovery_average
                            } else {
                                R.string.recovery_daily_average
                            }
                        ),
                        contributorType = ContributorType.SLEEP,
                        sleepChartData = sleepChartData,
                        timeRange = timeRange,
                        leftSelectionType = primaryGraphType,
                        leftValue = leftValue,
                        rightSelectionType = secondaryGraphType,
                        rightValue = rightValue,
                    )
                }
            } else flowOf(null)
        }
    }

    private fun createGraphData(
        sleepList: List<Sleep>,
        chartGranularity: ChartGranularity,
        graphType: SleepChartSelectionType,
        from: LocalDate,
        to: LocalDate,
    ): Flow<SleepChartData.Series?> {
        return when (graphType) {
            SleepChartSelectionType.SLEEP_DURATION ->
                flowOf(sleepChartDataCreator.createSleepDurationGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.SLEEP_REGULARITY ->
                flowOf(sleepChartDataCreator.createSleepRegularityGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.NAP_DURATION ->
                flowOf(sleepChartDataCreator.createSleepNapGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.TOTAL_TIME ->
                flowOf(sleepChartDataCreator.createSleepTotalGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.MAX_SLEEP_SPO2 ->
                flowOf(sleepChartDataCreator.createBloodOxygenGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.TRAINING_DURATION -> 
                workoutChartDataCreator.workoutForDateRangeFlow(from, to).map {
                    workoutChartDataCreator.createTrainingGraphData(chartGranularity, from, to, it)
                }

            SleepChartSelectionType.MIN_SLEEP_HR ->
                flowOf(sleepChartDataCreator.createMinHrDuringSleepGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.AVG_SLEEP_HR ->
                flowOf(sleepChartDataCreator.createAvgHrDuringSleepGraphData(chartGranularity, from, to, sleepList))

            SleepChartSelectionType.WAKE_UP_RESOURCES -> 
                recoveryChartDataCreator.recoveryForDateRangeFlow(from, to).map {
                    recoveryChartDataCreator.createMorningResourcesGraphData(chartGranularity, from, to, sleepList, it)
                }

            SleepChartSelectionType.NONE -> flowOf(null)
        }
    }
    @OptIn(ExperimentalCoroutinesApi::class)
    fun loadSingleSeriesWithSleepData(
        chartGranularity: ChartGranularity,
        selectionType: SleepChartSelectionType,
        from: LocalDate,
        to: LocalDate,
    ): Flow<SleepChartData.Series?> {
        return sleepDataRepository.sleepForDateRangeFlow(from, to).flatMapLatest { sleepList ->
            createGraphData(sleepList, chartGranularity, selectionType, from, to).map { series ->
                series?.copy(selectionType = selectionType)
            }
        }
    }
} 
