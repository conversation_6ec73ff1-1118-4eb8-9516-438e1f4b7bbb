<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="total_energy">Total %d kcal</string>


    <!-- Training HUB -->
    <string name="training_hub_comparison_avg">Avg. %s</string>
    <string name="training_hub_volume_title">Volume</string>
    <string name="training_hub_ascent">Ascent</string>
    <string name="training_hub_distance">Distance</string>
    <string name="training_hub_load">Load</string>
    <string name="training_hub_no_activities">No activities</string>
    <plurals name="training_hub_weeks_avg">
        <item quantity="one">%d week avg.</item>
        <item quantity="few">%d weeks avg.</item>
        <item quantity="many">%d weeks avg.</item>
        <item quantity="other">%d weeks avg.</item>
    </plurals>
    <plurals name="training_hub_category_subtitle">
        <item quantity="one">%1$d activity</item>
        <item quantity="few">%1$d activities</item>
        <item quantity="many">%1$d activities</item>
        <item quantity="other">%1$d activities</item>
    </plurals>
    <string name="training_hub_training_load_title">Training Load</string>
    <string name="training_hub_total_activities">Activities</string>
    <string name="training_hub_total_duration">Duration</string>
    <string name="training_hub_training_stress">Training Stress</string>
    <string name="training_hub_avg">Avg.</string>
    <string name="training_hub_this_week">This week</string>
    <string name="training_hub_intensity_title">Intensity</string>
    <string name="training_hub_volume_no_data">There is no data to show.</string>
    <string name="training_hub_intensity_type_heart_rate">Heart rate</string>
    <string name="training_hub_intensity_type_pace">Pace</string>
    <string name="training_hub_intensity_type_running_power">Running power</string>
    <string name="training_hub_intensity_type_cycling_power">Cycling power</string>
    <string name="training_hub_view_more">View more</string>
    <string name="training_hub_view_less">View less</string>
    <string name="training_hub_training">Training</string>
    <string name="training_hub_sleep_start">Sleep start</string>
    <string name="training_hub_recovery">Recovery</string>
    <string name="training_hub_form">Form</string>
    <string name="training_hub_form_sub_title">Training Stress Balance</string>
    <string name="training_hub_tsb_trend">TSB trend</string>
    <string name="training_hub_tsb">TSB</string>
    <string name="training_hub_sleep">Sleep</string>
    <string name="training_hub_sleep_trend">Sleep trend</string>
    <string name="training_hub_no_form_data">Complete activities to get insights.</string>
    <string name="training_hub_no_sleep_data">Measure sleep to get insights.</string>
    <string name="training_hub_no_feeling_data">Record your feeling after activities to gain insight on training progression.</string>
    <string name="training_hub_coach_feedback_title">Suunto Coach</string>
    <string name="training_hub_feeling">Feeling</string>
    <string name="training_hub_impact">Impact</string>
    <string name="training_hub_sub_section_training_model">Training model</string>
    <string name="training_hub_sub_section_impacts">Impacts</string>
    <plurals name="training_hub_impact_unclassified_workouts_count">
        <item quantity="one">You have %d unclassified workout</item>
        <item quantity="few">You have %d unclassified workouts</item>
        <item quantity="many">You have %d unclassified workouts</item>
        <item quantity="other">You have %d unclassified workouts</item>
    </plurals>
    <string name="training_hub_no_data">No Data</string>
    <string name="training_hub_no_data_to_show">There’s no data to show.</string>
    <string name="training_hub_impact_number_of_workouts">No. of workouts</string>
    <string name="training_hub_progress">Progress</string>
    <string name="training_hub_progress_fitness_change">Fitness change</string>
    <string name="training_hub_progress_fitness_change_sub_title">Chronic Training Load</string>
    <string name="training_hub_progress_ctl_abbreviation">CTL</string>
    <string name="training_hub_progress_ctl_ramp_rate">CTL Ramp Rate</string>
    <string name="training_hub_progress_ctl_fitness_loss">Fitness loss</string>
    <string name="training_hub_progress_ctl_fitness_gain">Fitness gain</string>
    <string name="training_hub_progress_ctl_ramp_rate_six_weeks">6 weeks range</string>
    <string name="training_hub_view_progress">View progress</string>
    <string name="training_hub_previous_six_weeks">Prev. 6 weeks</string>

    <!--  HRV-->
    <string name="sleep_hrv_sub_title">Heart Rate Variability</string>
    <string name="sleep_hrv_today_label">Today ms</string>
    <string name="sleep_hrv_yesterday_label">Yesterday ms</string>
    <string name="sleep_hrv_7_day_avg_missing">Track sleep 3 times per week for an effective avg HRV</string>
    <string name="sleep_hrv_coach_requires_normal_range">Determine your HRV range first to get insights</string>
    <string name="recovery_tab_description">Heart Rate Variability (HRV) implies how ready your body is to train. Optimal HRV is within Your normal range.</string>
    <string name="recovery_tab_sub_header">Current Status</string>

    <string name="training_hub_info_sheet_volume_and_load_title">Load/Volume</string>
    <string name="training_hub_info_sheet_volume_and_load_md">
        ## Training volume\n
        The total amount of training or exercise that an athlete does over a certain period. It is typically measured in terms of distance or time.\n

        ## Training load\n
        The amount of stress or strain from the training volume and intensity that an athlete\'s body is subjected to during training.\n\n

        ## Training Stress Score (TSS)\n
        A measurement of the training load used to determine whether an athlete is training at an appropriate level to meet their goals. TSS calculation uses the duration and intensity of each workout. The intensity is based on an athlete\'s an-aerobic threshold, which is defined in Suunto as Zone 4/5 limit in heart rate, pace, and power zones.\n\n

        Training load has a significant impact on an athlete\'s performance and ability to recover from training. Increasing training load can help an athlete to improve their endurance and overall fitness. However, it is important to be mindful of excessive training load as it can lead to burnout, injury, and other negative outcomes. As a result, it is generally recommended to gradually increase training load over time, rather than making sudden or drastic increases.\n\n
    </string>
    <string name="training_hub_info_sheet_intensity_title">Intensity</string>
    <string name="training_hub_info_sheet_intensity_md" formatted="false">
        Training on different intensities stresses your body in different ways and assists physiological adaptation.\n\n

        During light or moderate efforts, the energy is supplied by the oxidative system, burning fat and carbs, and your blood lactate levels remain the same as at rest (0,8–1,5 mmol/L).\n\n

        As the intensity increases, lactic acid starts to build in your muscles. Your body is still able to flush it out, but the lactate levels rise above your resting levels. In training terminology this is your aerobic threshold (usually marked at 1,5–2,0 mmol/L).\n\n

        If the intensity of the exercise intensifies even further, at some point your cardiovascular system can’t supply your muscles with enough oxygen, and lactic acid starts to build in your muscles faster than it can be removed. This point is called your anaerobic threshold (usually at around 4,0 mmol/L).\n\n

        ## Zone 5: Maximal\n
        Zone 5 is above your anaerobic threshold and ends at your max heart rate.\n\n

        Within this zone, the training will feel extremely hard. Lactic acid will build up in your system much faster than it can be removed and you may need stop after a few minutes.\n\n

        ## Zone 4: Very hard\n
        94–99% of your anaerobic threshold. The upper limit of zone 4 is your anaerobic threshold.\n\n

        Exercising in zone 4 will prepare your system for competition type events and high speeds. High-intensity training develops your fitness level quickly and effectively but done too often or at too high intensity may lead to overtraining, which may force you to take a long break from your training program.\n\n

        ## Zone 3: Hard\n
        89-93% of your anaerobic threshold.\n\n

        Exercising at zone 3 will improve your ability to move quickly and economically. In this zone, lactic acid begins to form in your system, but your body is still able to completely flush it out. You should train at this intensity at most a couple of times per week, as it puts your body under a lot of stress.\n\n

        ## Zone 2: Moderate\n
        83-88% of your anaerobic threshold. The upper limit of zone 2 is your aerobic threshold.\n\n

        Exercising at zone 2 feels easy, but workouts with a long duration can have a very high training effect. The majority of cardiovascular conditioning training should be performed within this zone. Long duration workouts at this zone consume a lot of energy, especially from your body’s stored fat.\n\n

        ## Zone 1: Easy\n
        &lt; 82% of your anaerobic threshold. Zone 1 starts at your resting heart rate.\n\n

        Exercising in zone 1 is relatively easy on your body. It is beneficial in restorative training and improving your basic fitness when you are beginning to exercise or after a long break.\n\n

        ## Set the intensity zones\n
        Use the SuuntoPlus™ sport app **Anaerobic Threshold** for running and **FTP Test** for cycling to find your anaerobic threshold levels.\n\n

        To set the zones, go to Training settings in your Suunto watch and update the intensity zones for all sports or specific sports.\n\n
    </string>

    <string name="training_hub_info_sheet_impact_title">Impacts</string>
    <string name="training_hub_info_sheet_cardio_impact_md">
        The impacts on your physiology are identified based on activity type, workout duration, and workout intensity. One workout can have cardio and muscular impacts. Effective training programs mix various workouts to reach variance of training impacts.\n\n

        # CARDIO IMPACTS\n
        ## VO₂max\n
        VO₂max workouts are designed to improve an individual\'s maximum oxygen uptake. These workouts involve short bursts of intense exercise followed by periods of recovery. They can be an effective way to improve cardiovascular fitness and athletic performance, and are typically performed in lower quantities during the week.\n\n

        ## Anaerobic\n
        Anaerobic training is characterized by short, but high-intensity exercises. It is important to know that anaerobic training should be balanced with aerobic exercise to achieve overall physical fitness and health.\n\n

        ## Anaerobic - hard\n
        These workouts are performed in longer, harder efforts and done close to an-aerobic threshold. Racing events are typically categorized as hard an-aerobic workouts.\n\n

        ## Aerobic/Anaerobic\n
        The workouts done in this impact can be sustained for longer periods of time. They can still feel a bit hard as lactic acid is building during this phase.\n\n

        # AEROBIC\n
        ## Aerobic\n
        Aerobic training involves repetitive, low-to moderate-intensity physical activity that is performed for an extended period of time. The goal of aerobic training is to increase the body\'s ability to use oxygen. This improves the efficiency of the cardiovascular system and overall physical endurance. Examples of aerobic exercise include running, cycling, swimming, and walking.\n\n

        ## Long aerobic\n
        Long aerobic sessions will help improve an individual\'s stamina and ability to sustain physical activity for extended periods of time. This can be beneficial for sports and activities that require sustained effort. Usually, long aerobic sessions need constant fueling with hydration and nutrition.\n\n

        ## Heavy Aerobic\n
        Harder long aerobic effort refers to workouts that have higher training stress and usually last several hours. These can be long races or harder training sessions which can have a mixture of intensity.\n\n

        ## Recovery\n
        Recovery workouts aid the recovery process. They are lower in intensity and duration compared to a typical workout. They may also focus more on technique such as stretching, foam rolling, and massage to help the body recover.\n\n
    </string>

    <string name="training_hub_info_sheet_muscular_impact_md">
        The impacts on your physiology are identified based on activity type, workout duration, and workout intensity. One workout can have cardio and muscular impacts. Effective training programs mix various workouts to reach variance of training impacts.\n\n

        # MUSCULAR IMPACTS\n
        ## Strength\n
        This type of training can help to increase muscle strength and tone, which can improve overall physical function and performance. It can include workouts using free weights (such as dumbbells and barbells), weight machines, resistance bands, and body weight exercises.\n
        Overall, strength training is an important part of a well-rounded exercise routine.\n\n

        ## Speed and agility\n
        Workouts within this impact improve the body\'s ability to move efficiently, coordination, and balance. These skills are important in many activities and can have a number of benefits for both athletes and non-athletes.\n\n

        ## Flexibility\n
        Flexibility training helps to increase the range of motion around a joint, making it easier to perform everyday tasks and activities. It can help improve performance in sports that require a wide range of motion such as gymnastics, dance, and martial arts.\n\n
    </string>

    <string name="training_hub_info_sheet_training_models_title">Training models</string>
    <string name="training_hub_info_sheet_training_models_md">
        The training model an athlete is following is usually mixed with different workout intensities. The model identified is based on classifying each workout\'s cardio impact.\n\n

        ## Base endurance\n
        Base endurance training refers to a period of training that is focused on performing a high volume of low- to moderate-intensity workouts to build endurance and improve the body\'s ability to use oxygen efficiently. It is typically the first phase of training for endurance athletes, such as runners, cyclists, and triathletes.\n\n

        ## Polarized\n
        Polarized training is a type of training method that involves performing most of your workouts at low intensity with some workouts on high intensity spectrum. This approach is focused on improving your endurance and high-intensity performance, while minimizing the risk of overtraining and injury.\n\n

        ## Sweetspot\n
        Sweetspot training is a type of training that involves performing workouts at a moderate intensity, which is often referred to as the "sweetspot" intensity. This intensity is typically defined as the point at which the body is working hard enough to challenge itself, but not so hard that it becomes overly fatigued.\n\n

        ## High Intensity\n
        High-intensity training (HIT) refers to a type of physical exercise that is performed at a high level of intensity, typically for short periods of time. HIT workouts are designed to improve muscle strength, power, and endurance, and can be an effective way to improve physical fitness and performance.\n\n

        ## Pyramid\n
        Pyramid training intensity model refers to a training program where most of the workouts are done in low aerobic intensity, some amount in mid intensities and low volume in high intensity. This intensity model is quite typical for athletes to follow, where every aspect of the intensity spectrum is used, but the higher efforts are always less of the volume to minimize risks of overtraining and injury.\n\n
    </string>

    <string name="training_hub_info_sheet_recovery_hrv_title">HRV and its measuring explained</string>
    <string name="training_hub_info_sheet_recovery_hrv_md">
        ## What is HRV?\n
        HRV (Heart Rate Variability) is a measure of the variation in time intervals between heartbeats. It reflects the balance of the autonomic nervous system (ANS) and provides insights into overall health and stress levels. HRV is a powerful tool for understanding autonomic function and promoting well-being.\n\n

        ## How should I read the data?\n
        For optimal HRV, the value should stay within your normal range and a bit closer to the “Too high” limit. Higher HRV is generally considered to be better, but HRV should always be compared to your normal range. Different situations and conditions such as a relaxed holiday, physical and mental exertion or developing flu can lead to changes in HRV.\n\n

        ## How to measure my HRV?\n
        Suunto measures HRV during your sleep. To obtain HRV data, you should wear your watch while sleeping and ensure that sleep tracking is enabled on the device.\n\n
        During the sleep period, heart rate variability is measured continuously to calculate the average RMSSD value for the night. RMSSD stands for the root mean square of successive differences between normal heartbeats, which is a commonly used metric for assessing HRV.\n\n

        ## Values explained\n
        The Today’s HRV value is derived from the measurements taken during the previous night, while Yesterday’s HRV value refers to the night before.\n\n
        The 7-day average is calculated based on your HRV measurements from the past 7 nights.\n\n
        To establish your 7-day average, you should have a minimum of 3 HRV measurements within a 7-day period.\n\n
        To determine your normal range, you should have a total of 14 measurements taken over a span of 60 days.\n\n

        ## Your normal range\n
        The recovery status is evaluated by comparing your 7-day measurement to your personal normal range. The bar will illustrate this with Suunto coach, providing you insights about your recovery state.\n\n
        To display the bar, you should have a minimum of 14 days of measurements taken within the previous 60 days. The value shown in the indicator represents the 7-day average.\n\n
    </string>

    <string name="training_hub_info_sheet_recovery_form_title">Training Stress Balance (TSB)</string>
    <string name="training_hub_info_sheet_recovery_form_md">
        Training Stress Balance is a measurement calculated by subtracting an athlete\'s Chronic Training Load (CTL) from their Acute Training Load (ATL) to help determine whether an athlete is overtraining or under training.\n\n

        A positive TSB value indicates that the athlete has a higher level of fitness than fatigue and may be well-rested to perform at a high level. The ideal range for peak performance is between +15 to +25.\n\n

        A negative TSB value indicates that the athlete is experiencing a higher level of fatigue than fitness. The ideal range for training is -10 and -30. Pushing beyond -30 will require extensive recovery to achieve.\n\n

        A zero TSB value indicates that the athlete\'s fitness and fatigue are in balance.\n\n

        Use TSB in conjunction with other measures, such as subjective feeling, to monitor training load and optimize performance.\n\n
    </string>

    <string name="training_hub_info_sheet_recovery_sleep_title">Sleep</string>
    <string name="training_hub_info_sheet_recovery_sleep_md">
        Adequate sleep helps to improve physical and mental performance. It can help with concentration, memory, and your mood as well as strengthen the immune system. Not getting enough sleep will in turn do the opposite and can impair judgment and coordination, increasing the risk of accidents and injuries. Overtraining can lead to changes in sleep patterns, including difficulty falling asleep or staying asleep.\n\n
    </string>

    <string name="training_hub_info_sheet_recovery_feeling_title">Feeling</string>
    <string name="training_hub_info_sheet_recovery_feeling_md">
        Reporting your feeling from each workout is way to track and avoid overtraining.\n\n

        Overtraining refers to a state of excessive physical and/or mental fatigue caused by excessive training without sufficient rest and recovery. One of the most obvious signs of overtraining is a decline in performance, including a decrease in strength, endurance, and speed.\n\n

        It is important for athletes to pay attention to these signs and prevent overtraining by incorporating adequate rest into their training regimen.\n\n
    </string>

    <string name="training_hub_info_sheet_progress_title">Progress</string>
    <string name="training_hub_info_sheet_progress_md" formatted="false">
        ## Chronic Training Load (CTL)\n
        Chronic Training Load is a 42 day average of your daily TSS. This number depends on the individual athlete and is not a one number fits all.\n\n

        An increase in CTL occurs when an individual consistently trains with a TSS score that is at least 25% above their current CTL. Conversely, when an athlete reduces their training load, their CTL will decrease, indicating a decline in fitness. A steady progression of CTL is key to preventing injuries and during your rest weeks your CTL should decline.\n\n

        The chronic training load ramp rate monitors the rate of change in your fitness over time. An ideal ramp rate is one that can be maintained for a few weeks.\n\n

        ## Fitness tests\n
        Fitness tests are used to assess an individual\'s overall physical fitness, as well as to identify specific areas of strength and weakness. Suunto provides various SuuntoPlus™ sport apps for testing such as FTP Test, Anaerobic Threshold, and Cooper test.\n\n
    </string>

    <string name="training_hub_info_sheet_recovery_state_md" formatted="false">
        ## What is Recovery state?\n
        Recovery state shows how well your body has recovered and its readiness for activity. It helps you decide when to train hard, go easy, or rest.\n\n

        ## Recovery state is influenced by three key factors:\n
        * Training: The intensity and volume of your workouts directly impact recovery. Pushing hard adds stress to your body, so balancing effort and rest is key.\n
        * Sleep: Quality sleep has a big effect on recovery. Heart rate variability (HRV), sleep depth, and duration all help your body recharge.\n
        * Daily activity: Stressful or busy days can slow down recovery. High daily activity can limit your recovery time.\n\n

        ## Maximize insights\n
        Use your Suunto device for workouts, sleep, and daily tracking to get the best recovery state insights.\n\n
    </string>
    <string name="training_hub_info_sheet_recovery_state_read_more">Recovery tips</string>
    <!--  Coach phrases  -->
    <string name="training_hub_coach_phrase_id_1">Complete your first week to gain insights on your training.</string>
    <string name="training_hub_coach_phrase_id_2">You started the week with a high training load. Take it easy later in the week to prevent fatigue.</string>
    <string name="training_hub_coach_phrase_id_3">Your training load is high this week. Gradual progression is recommended.</string>
    <string name="training_hub_coach_phrase_id_4">Your training load is high this week. Rapidly increasing it can cause injuries.</string>
    <string name="training_hub_coach_phrase_id_5">You have challenged yourself and completed a high training load this week. Decreasing the load next week is recommended.</string>
    <string name="training_hub_coach_phrase_id_6">You started the week with a nice progressive training load. Good start! </string>
    <string name="training_hub_coach_phrase_id_7">Your training load is higher than usual. Train safely by gradually increasing it.</string>
    <string name="training_hub_coach_phrase_id_8">Great job pushing through! Your training load was higher than normal this week. Gradual progression of load helps improve fitness.</string>
    <string name="training_hub_coach_phrase_id_9">Good start to the week! Your training load is within your normal range.</string>
    <string name="training_hub_coach_phrase_id_10">Your training load is near your normal range this week.</string>
    <string name="training_hub_coach_phrase_id_11">Good start to the week! Increase your effort to progress in your training.</string>
    <string name="training_hub_coach_phrase_id_12">Your training load is slightly lower than usual. Increase intensity or volume during the week to meet your average.</string>
    <string name="training_hub_coach_phrase_id_13">Well done for being consistent! Your training load was within your average range.</string>
    <string name="training_hub_coach_phrase_id_14">This week was easy, which is necessary every now and then. Let\'s see what you can do next week!</string>
    <string name="training_hub_coach_phrase_id_15">This week was easy. Next week is a good opportunity to push a bit harder.</string>
    <string name="training_hub_coach_phrase_id_16">This week was ideal for recovery. Next week has a lot of potential.</string>
    <string name="training_hub_coach_phrase_id_17">The week started with a lower training load with potential to progress later in the week.</string>
    <string name="training_hub_coach_phrase_id_18">Easy start to the week. Increase your effort to progress in your training.</string>
    <string name="training_hub_coach_phrase_id_19">Your training load has been lighter than usual. Let\'s see what you\'re capable of this weekend.</string>
    <string name="training_hub_coach_phrase_id_20">Your training load is lower than normal. You still have time to increase it this week.</string>
    <string name="training_hub_coach_phrase_id_21">Your training has been relaxed this week. Unlock your potential this weekend.</string>
    <string name="training_hub_coach_phrase_id_22">Your training load is lighter than normal. If you\'re feeling okay, let\'s see what you can accomplish.</string>
    <string name="training_hub_coach_phrase_id_23">You had an easy week of training. Unlock your potential this weekend.</string>
    <string name="training_hub_coach_phrase_id_25">New week, new opportunities!</string>
    <string name="training_hub_coach_phrase_id_26">Ease into the week with a light intensity workout.</string>
    <string name="training_hub_coach_phrase_id_27">Set your goals and train at your own pace.</string>
    <string name="training_hub_coach_phrase_id_28">Let\'s see what you\'re capable of.</string>
    <string name="training_hub_coach_phrase_id_29">Begin your fitness journey and maximize your potential this weekend.</string>
    <string name="training_hub_coach_phrase_id_30">Let\'s end the week with a light walk or jog.</string>
    <string name="training_hub_coach_phrase_id_31">Easy week, set your fitness journey on fire next week!</string>
    <string name="training_hub_coach_phrase_id_39">You started the week with more high intensity cardio than usual. Rapidly increasing it can increase the risk of injury.</string>
    <string name="training_hub_coach_phrase_id_40">You have completed more high intensity workouts than normal. Balance these with low intensity workouts to reduce the risk of injury.</string>
    <string name="training_hub_coach_phrase_id_41">This week included more high intensity workouts, it\'s important to monitor your efforts and balance them with low intensity workouts to avoid injury.</string>
    <string name="training_hub_coach_phrase_id_42">You completed fewer high intensity workouts than normal this week. Consider pushing yourself to reach zone 4 and 5 this weekend.</string>
    <string name="training_hub_coach_phrase_id_43">Your training volume within zone 1 and 2 have significantly increased this week compared to normal.</string>
    <string name="training_hub_coach_phrase_id_44">The volume of your aerobic workouts is lower this week than normal, consider aiming for zone 1 and 2 during your workouts this weekend.</string>
    <string name="training_hub_coach_phrase_id_45">Monitor your training volume. It is currently very high.</string>
    <string name="training_hub_coach_phrase_id_46">Your workout volume is higher than your usual effort.</string>
    <string name="training_hub_coach_phrase_id_47">Great job! You have gained a high volume of training hours this week. Enjoy a lighter volume in the following week.</string>
    <string name="training_hub_coach_phrase_id_48">There were fewer workouts this week. Are you ready to increase it next week?</string>
    <string name="training_hub_coach_phrase_id_49" formatted="false">You have run more than your weekly target distance. A 10% weekly increase is a good rule of thumb.</string>
    <string name="training_hub_coach_phrase_id_50">This week you completed a high running volume. Follow up with a lower running volume next week to avoid risk of injury.</string>
    <string name="training_hub_coach_phrase_id_51">You have reached your weekly running distance. Gradual progress is key.</string>
    <string name="training_hub_coach_phrase_id_52">You have run less than your weekly target distance. Let\'s see what you can do!</string>
    <string name="training_hub_coach_phrase_id_53" formatted="false">You have cycled more than your weekly target volume. A 10% weekly increase is a good rule of thumb.</string>
    <string name="training_hub_coach_phrase_id_54">This week you cycled a high volume. Follow up with a lower cycling volume next week to avoid risk of injury.</string>
    <string name="training_hub_coach_phrase_id_55">You have reached your weekly cycling distance. Gradual progress is key.</string>
    <string name="training_hub_coach_phrase_id_56">You have cycled less than your weekly target distance. Let\'s see what you can do!</string>
    <string name="training_hub_coach_phrase_id_57" formatted="false">You have swam more than your weekly target volume. A 10% weekly increase is a good rule of thumb.</string>
    <string name="training_hub_coach_phrase_id_58">This week you swam a high volume. Follow up with a lower swimming volume next week to avoid risk of injury.</string>
    <string name="training_hub_coach_phrase_id_59">You have reached your weekly swimming distance. Gradual progress is key.</string>
    <string name="training_hub_coach_phrase_id_60">You have swam less than your weekly target distance. Let\'s see what you can do!</string>
    <string name="training_hub_coach_phrase_id_61">You have done more strength training this week than normal. Gradual increase of effort is recommended.</string>
    <string name="training_hub_coach_phrase_id_62">You have done less strength training this week than usual.</string>
    <string name="training_hub_coach_phrase_id_63">You have done more strength training this week. Continue to gradually increase your efforts in the following weeks.</string>
    <string name="training_hub_coach_phrase_id_64">You have done less strength training this week. Boost your fitness with strength training sessions next week.</string>
    <string name="training_hub_coach_phrase_id_65">You have done more high-intensity workouts than usual. Gradual increase with higher intensity workouts is recommended.</string>
    <string name="training_hub_coach_phrase_id_66">Increase the number of high-intensity workouts to meet your average volume.</string>
    <string name="training_hub_coach_phrase_id_67">You have put in a lot of effort this week with high-intensity workouts. To ensure a productive training, gradually increase the intensity.</string>
    <string name="training_hub_coach_phrase_id_68">Balancing the intensity of your workouts is crucial. Can you add one more high-intensity workout?</string>
    <string name="training_hub_coach_phrase_id_69">You\'ve made more aerobic effort than usual, which is good for your cardiovascular base.</string>
    <string name="training_hub_coach_phrase_id_70">This week, you increased your cardio effort.</string>
    <string name="training_hub_coach_phrase_id_71">Compared to your normal week, you did fewer aerobic activities this week.</string>
    <string name="training_hub_coach_phrase_id_72">Your focus on cardio aligns with the base training model, which has remained consistent with previous weeks.</string>
    <string name="training_hub_coach_phrase_id_73">You did more high-intensity cardio activities this week compared to your previous focus on base endurance.</string>
    <string name="training_hub_coach_phrase_id_74">This week, you did more cardio workouts in zones 3 and 4, compared to your normal base endurance focus.</string>
    <string name="training_hub_coach_phrase_id_75">Normally, your cardio focus aligns with the base endurance model, but this week you are shifting towards polarized training with an increase in high-intensity workouts.</string>
    <string name="training_hub_coach_phrase_id_76">Your normal cardio focus is in line with the base endurance model, but this week you added some mid to high-intensity efforts.</string>
    <string name="training_hub_coach_phrase_id_77">This week, your cardio focus aligned with the base endurance training model, which is a significant change from your typical high-intensity training.</string>
    <string name="training_hub_coach_phrase_id_78">You are consistent with your high intensity cardio effort.</string>
    <string name="training_hub_coach_phrase_id_79">With your recent workouts in zones 3 and 4, your high-intensity training is moving towards the sweet spot training model.</string>
    <string name="training_hub_coach_phrase_id_80">With your recent low-intensity workouts, your high-intensity training is shifting towards polarized training.</string>
    <string name="training_hub_coach_phrase_id_81">With your recent low-intensity workouts, your high-intensity training is moving towards the pyramid training model.</string>
    <string name="training_hub_coach_phrase_id_82">Normally, your cardio focus is in zones 3 and 4. This week you are following the base endurance training model. To return to your normal pattern, increase the intensity.</string>
    <string name="training_hub_coach_phrase_id_83">This week, you made high-intensity cardio efforts, which is different from your typical focus on zones 3 and 4.</string>
    <string name="training_hub_coach_phrase_id_84">This week, your cardio focus has been on workouts in zones 3 and 4, as in previous weeks.</string>
    <string name="training_hub_coach_phrase_id_85">This week, your cardio effort aligned with polarized training, which is a change from your typical sweet spot training.</string>
    <string name="training_hub_coach_phrase_id_86">You increased the volume of low-intensity workouts compared to your normal focus on zones 3 and 4.</string>
    <string name="training_hub_coach_phrase_id_87">This week, your cardio focus aligned with the base endurance training model, but normally you also have high-intensity workouts for polarized training.</string>
    <string name="training_hub_coach_phrase_id_88">This week, you had a higher focus on high-intensity cardio. Normally, you have more base endurance focus for polarized training.</string>
    <string name="training_hub_coach_phrase_id_89">This week, your cardio focus was on workouts in zones 3 and 4, which is a significant change from your normal polarized training model.</string>
    <string name="training_hub_coach_phrase_id_90">Normally, your cardio training follows the polarized intensity model, and this week you are following the same pattern.</string>
    <string name="training_hub_coach_phrase_id_91">Normally, your cardio training follows the polarized intensity model, but this week you shifted towards the pyramid model with more workouts between aerobic and anaerobic thresholds.</string>
    <string name="training_hub_coach_phrase_id_92">This week, your cardio focus was on base endurance. To follow your normal pyramid intensity model, add some mid to high-intensity workouts.</string>
    <string name="training_hub_coach_phrase_id_93">This week, your cardio focus was on high-intensity. To follow your normal pyramid intensity model, add some low-intensity workouts.</string>
    <string name="training_hub_coach_phrase_id_94">This week, your cardio focus was between aerobic and anaerobic thresholds. To follow your normal pyramid intensity model, add more low-intensity workouts.</string>
    <string name="training_hub_coach_phrase_id_95">Your normal cardio routine follows the pyramid intensity model, with fewer mid-intensity workouts, bringing you closer to polarized training.</string>
    <string name="training_hub_coach_phrase_id_96">You have been consistent with your pyramid intensity model.</string>
    <string name="training_hub_coach_phrase_id_97">You have been sleeping more lately, which is great for recovery.</string>
    <string name="training_hub_coach_phrase_id_98">Your sleep has been less than normal. Remember that sleep is important for recovery.</string>
    <string name="training_hub_coach_phrase_id_99">Your sleep volume is below the recommended amount. A good night\'s rest supports overall health.</string>
    <string name="training_hub_coach_phrase_id_100">You have reported more positive feelings after your workouts than usual. This is a great sign of training adaptation.</string>
    <string name="training_hub_coach_phrase_id_101">Your reported feelings after your workouts have been less positive than usual. This could be a sign of challenging sessions or stress.</string>
    <string name="training_hub_coach_phrase_id_102">With adequate rest, you are now ready to increase your training load.</string>
    <string name="training_hub_coach_phrase_id_103">The time you took for recovery has prepared you to increase your training intensity.</string>
    <string name="training_hub_coach_phrase_id_104">Your recovery is in sync with your long-term training load.</string>
    <string name="training_hub_coach_phrase_id_105">As you increase your training load, keep in mind that recovery is essential for balanced progress.</string>
    <string name="training_hub_coach_phrase_id_106">Rest and recovery are equally important as training for optimal performance.</string>
    <string name="training_hub_coach_phrase_id_107">You have significantly increased your training load. Take some time for rest and recovery to give your muscles time to restore and come back stronger.</string>
    <string name="training_hub_coach_phrase_id_108">Great job! Your fitness has significantly improved in recent weeks. Don\'t forget to balance training with proper rest.</string>
    <string name="training_hub_coach_phrase_id_109">Look at you go! Your fitness has improved in recent weeks.</string>
    <string name="training_hub_coach_phrase_id_110">Your fitness is on an upward trend. Keep up the good work!</string>
    <string name="training_hub_coach_phrase_id_111">Your fitness level is declining, but with a little effort, you\'ll be back in no time.</string>
    <string name="training_hub_coach_phrase_id_112">Your fitness level is declining. Make the most of the next few weeks.</string>
    <string name="training_hub_coach_phrase_id_113">Well-deserved rest after the hard work you\'ve done to significantly improve your fitness in recent weeks.</string>
    <string name="training_hub_coach_phrase_id_114">Way to take the time for recovery after the effort you put in to increase your fitness in recent weeks.</string>
    <string name="training_hub_coach_phrase_id_115">After a brief recovery period, your fitness has been improving.</string>
    <string name="training_hub_coach_phrase_id_116">Your fitness level is declining, but with a bit of effort, you\'ll be back in no time.</string>
    <string name="training_hub_coach_phrase_id_117">You got this! Your fitness is on an upward trend after a brief dip.</string>
    <string name="training_hub_coach_phrase_id_118">You have been maintaining a normal sleep volume.</string>
    <string name="training_hub_coach_phrase_id_119">Your reported feelings from workouts are at a similar level to normal.</string>
    <string name="training_hub_coach_phrase_id_120">You\'ve made less aerobic effort this week than you normally do.</string>
    <string name="training_hub_coach_phrase_id_121">Your fitness is at a normal level for you with a bit of progress lately.</string>
    <string name="training_hub_coach_phrase_id_122">Your fitness is at a normal level for you with a bit of decline lately.</string>
    <string name="training_hub_coach_phrase_id_123">You started the week with a high training load, is today time for rest?</string>
    <string name="training_hub_coach_phrase_id_124">You started the week with a nice progressive training load, is today reserved for recovery workout?</string>
    <string name="training_hub_coach_phrase_id_125">Great start with your training load this week. What is the plan for today?</string>
    <string name="training_hub_coach_phrase_id_126">Good start with your training load this week. Do you plan to continue today?</string>
    <string name="training_hub_coach_phrase_id_127">Light training load to start off the week. Progress in your training by balancing a high effort workout today.</string>
    <string name="training_hub_coach_phrase_id_128">Light start to the week and in line with active recovery.</string>
    <string name="training_hub_coach_phrase_id_129">You\'ve had a high training load this week, with big efforts today. Consider a recovery day tomorrow.</string>
    <string name="training_hub_coach_phrase_id_130">You started the week with a progressive training load with big efforts today. Consider some rest tomorrow.</string>
    <string name="training_hub_coach_phrase_id_131">You started the week with a nice training load with big efforts today. Consider some recovery tomorrow.</string>
    <string name="training_hub_coach_phrase_id_132">You made some big efforts today. Consider some recovery tomorrow.</string>
    <string name="training_hub_coach_phrase_id_133">You put in a lot of effort today. Consider some easier workouts tomorrow.</string>
    <string name="training_hub_coach_phrase_id_134">You put in a lot of effort today. Consider some easier workouts tomorrow.</string>
    <string name="training_hub_coach_phrase_id_135">Your training load has been high this week with big efforts today. Consider to rest tomorrow.</string>
    <string name="training_hub_coach_phrase_id_136">You have made strong efforts this week with a progressive training load. Consider some recovery tomorrow.</string>
    <string name="training_hub_coach_phrase_id_137">You\'re within your normal training load range this week with big efforts today to show you\'re on track. Good job</string>
    <string name="training_hub_coach_phrase_id_138">Your training load is slightly lower than usual, but today\'s big efforts show you\'re still pushing yourself. Great work!</string>
    <string name="training_hub_coach_phrase_id_139">Your training load earlier this week was lower than normal, but today you put in the work. Keep it up!</string>
    <string name="training_hub_coach_phrase_id_140">Despite a low training load this week, your effort today shows you\'re dedicated to your goals. Keep up the good work!</string>
    <string name="training_hub_coach_phrase_id_141">You\'ve had a high training load this week with big efforts today. Consider taking some rest this weekend to recharge.</string>
    <string name="training_hub_coach_phrase_id_142">You started the week strong with a progressive training load and big efforts today. Consider taking some time to recover this weekend for optimal results.</string>
    <string name="training_hub_coach_phrase_id_143">Great job on maintaining your normal training load this week and pushing through with big efforts today!</string>
    <string name="training_hub_coach_phrase_id_144">Although your training load was slightly lower than usual, you made up for it with big efforts today. Well done, and enjoy your weekend!</string>
    <string name="training_hub_coach_phrase_id_145">Your training load earlier this week was lower than normal, but you showed up today with big effort! Keep up the good work during weekend.</string>
    <string name="training_hub_coach_phrase_id_146">Your training load has been quite low this week, but today\'s activity had big effort! Keep up the good work during your weekend!</string>
    <string name="training_hub_coach_phrase_id_147">You really pushed yourself this week with a high training load and finished with big effort today. Consider taking some recovery days next week to allow your body to rest.</string>
    <string name="training_hub_coach_phrase_id_148">You\'ve had a high training load this week, finishing with a big effort today. Recovery days next week will help you come back stronger.</string>
    <string name="training_hub_coach_phrase_id_149">Your training load was in the normal range this week, and you pushed yourself with big efforts today. Keep up the great work!</string>
    <string name="training_hub_coach_phrase_id_150">Your training load was slightly lower this week, you finished strong with big efforts. Enjoy your weekend and keep up the good work!</string>
    <string name="training_hub_coach_phrase_id_151">This week you had a lower training load, but you finished strong with big efforts.</string>
    <string name="training_hub_coach_phrase_id_152">Your training load has been quite high this week. Is recovery a priority today?</string>
    <string name="training_hub_coach_phrase_id_153">You\'ve had a high training load this week. It is important to prioritize recovery by resting or with a light session.</string>
    <string name="training_hub_coach_phrase_id_154">You had a great start with your training load this week. Do you have plans to continue today?</string>
    <string name="training_hub_coach_phrase_id_155">Good start with your training load this week. Are you planning to continue today?</string>
    <string name="training_hub_coach_phrase_id_156">Your training load has been light this week. Are there plans to increase the tempo today?</string>
    <string name="training_hub_coach_phrase_id_157">This week continues to be light. Rest is great for optimal performance today or later this week.</string>
    <string name="training_hub_coach_phrase_id_158">Your training load for the week has been quite high. Will you be resting today and taking it easier next week?</string>
    <string name="training_hub_coach_phrase_id_159">Your training load for the week was quite high. Resting today will prepare you for the following week.</string>
    <string name="training_hub_coach_phrase_id_160">You have had great week managing your training load. Is there more to come at the end of the week?</string>
    <string name="training_hub_coach_phrase_id_161">Good week! Do you have time for a final push?</string>
    <string name="training_hub_coach_phrase_id_162">You have had an easy training load this week. Anything left to finalize the week?</string>
    <string name="training_hub_coach_phrase_id_163">You\'ve had an easy training week. Is there something planned for a strong finish?</string>
    <string name="training_hub_coach_phrase_id_164">Your HRV is normal, indicating optimal recovery.</string>
    <string name="training_hub_coach_phrase_id_165">Your HRV seems to be normal. Balance training and recovery.</string>
    <string name="training_hub_coach_phrase_id_166">Your HRV seems to be normal. Balance training and recovery.</string>
    <string name="training_hub_coach_phrase_id_167">Your HRV is below normal, indicating poor recovery.</string>
    <string name="training_hub_coach_phrase_id_168">Your HRV is above normal. Pay attention.</string>
    <string name="training_hub_coach_phrase_id_169">Your readiness to train looks great based on your recovery status.</string>
    <string name="training_hub_coach_phrase_id_170">You have good training readiness, but there is a decline in subjective feeling.</string>
    <string name="training_hub_coach_phrase_id_171">You have good training readiness, but there is a decline in sleep which is important for recovery.</string>
    <string name="training_hub_coach_phrase_id_172">Your training readiness is impacted with recent high training load.</string>
    <string name="training_hub_coach_phrase_id_173">Your training readiness is impacted based on HRV data.</string>
    <string name="training_hub_coach_phrase_id_174">Your training readiness seems okay, but sleep and subjective feeling decline might indicate recovery issues.</string>
    <string name="training_hub_coach_phrase_id_175">Your training readiness impacted with recent high training load and decline in subjective feeling.</string>
    <string name="training_hub_coach_phrase_id_176">Your training readiness is impaired based on HRV data and there is also decline in subjective feeling.</string>
    <string name="training_hub_coach_phrase_id_177">Your training readiness and recovery are impacted due to recent high training load and less sleep.</string>
    <string name="training_hub_coach_phrase_id_178">Your training readiness is impaired based on HRV data and insufficient sleep affecting recovery.</string>
    <string name="training_hub_coach_phrase_id_179">Your training readiness is affected due to HRV data and recent high training fatigue.</string>
    <string name="training_hub_coach_phrase_id_180">You have poor training readiness with low HRV, high training fatigue, poor sleep, and declined feeling.</string>
    <string name="training_hub_coach_phrase_id_181">You have poor training readiness with low HRV, high training fatigue, and poor sleep.</string>
    <string name="training_hub_coach_phrase_id_182">You have poor training readiness with low HRV, high training fatigue, and declined subjective feeling.</string>
    <string name="training_hub_coach_phrase_id_183">Your training readiness is not the best with high training fatigue from latest training load as well as poor sleep and declined feeling lately.</string>
    <string name="training_hub_coach_phrase_id_184">You have poor training readiness indicated by low HRV, poor sleep, and declined feeling.</string>
    <!--  END of Coach phrases  -->
    <!-- END Of Training HUB -->
    <string name="suunto_coach_message_title" translatable="false">Suunto coach</string>
    <string name="suunto_coach_message_default" translatable="false">Your recovery is moderate, which may indicate ongoing adaptation.</string>
    <string name="recovery_state_title" translatable="false">Recovery state</string>
    <string name="recovery_state_date_time" translatable="false">Today at %s</string>
    <string name="recovery_daily_average" translatable="false">Daily avg.</string>
    <string name="recovery_average" translatable="false">Average</string>"

    <!-- HRV Display -->
    <string name="hrv_title" translatable="false">HRV</string>
    <string name="hrv_last_night" translatable="false">Last night</string>
    <string name="hrv_seven_day_avg" translatable="false">7-d avg.</string>
    <string name="hrv_status_low" translatable="false">Low</string>
    <string name="hrv_status_normal" translatable="false">Normal</string>
    <string name="hrv_status_high" translatable="false">High</string>
    <string name="hrv_status_no_data" translatable="false">No data</string>
    <string name="info" translatable="false">Information</string>

    <!-- Training Fatigue Resources -->
    <string name="tss_today" translatable="false">TSS today</string>
    <string name="tss_yesterday" translatable="false">TSS yesterday</string>
    <string name="tsb" translatable="false">TSB</string>
    <string name="seven_day_avg_feelings" translatable="false">7-day avg. feelings</string>
    <string name="feeling_excellent" translatable="false">Excellent</string>
    <string name="feeling_good" translatable="false">Good</string>
    <string name="feeling_normal" translatable="false">Normal</string>
    <string name="feeling_poor" translatable="false">Poor</string>
    <string name="feeling_very_poor" translatable="false">Very poor</string>

    <string name="daily_resources" translatable="false">Daily resources</string>

    <!-- Resources Level -->
    <string name="resources_level_low" translatable="false">Low</string>
    <string name="resources_level_moderate" translatable="false">Moderate</string>
    <string name="resources_level_high" translatable="false">High</string>
    <string name="resources_level_very_high" translatable="false">Very high</string>

    <!-- TSB -->
    <string name="tsb_rating_exhausted" translatable="false">Exhausted</string>
    <string name="tsb_rating_fatigued_gaining_fitness" translatable="false">Fatigued / Gaining Fitness</string>
    <string name="tsb_rating_balanced" translatable="false">Balanced</string>
    <string name="tsb_rating_ready_for_more" translatable="false">Ready for more</string>

    <!-- Recovery Tips -->
    <string name="recovery_tips_optimal" translatable="false">Your recovery is moderate, which may indicate ongoing adaptation.</string>
    <string name="recovery_tips_good" translatable="false">Your recovery is at a good level, indicating a well-recovered state for training.</string>
    <string name="recovery_tips_fair" translatable="false">Your recovery is below optimal, suggesting your body is still in the process of adjusting.</string>
    <string name="recovery_tips_poor" translatable="false">Your recovery is very low, which may indicate high fatigue. Awareness of this can help in managing your training.</string>
    <string name="recovery_tips_limited" translatable="false">Your recovery is limited. Consider taking more rest before intense training.</string>
    <string name="recovery_tips_no_data" translatable="false">No recovery data available. Wear your watch during sleep to track recovery.</string>

    <string name="contributor_sleep_empty" translatable="false">No insights. Enable sleep tracking in your watch</string>
    <string name="contributor_hrv_empty" translatable="false">No insights. Enable HRV and sleep tracking in your watch</string>
    <string name="contributor_resting_heart_rate_empty" translatable="false">No insights. Enable HR tracking in your watch</string>
    <string name="contributor_training_fatigue_empty" translatable="false">Track workouts to get insights</string>
    <string name="contributor_resource_empty" translatable="false">No insights. Enable sleep tracking in your watch</string>
    <!-- Date Picker -->
    <string name="date_picker_today" translatable="false">Today</string>
    <string name="date_picker_yesterday" translatable="false">Yesterday</string>
    <string name="date_picker_this_week" translatable="false">This week</string>
    <string name="date_picker_last_week" translatable="false">Last week</string>
    <string name="date_picker_this_month" translatable="false">This month</string>
    <string name="date_picker_last_month" translatable="false">Last month</string>
    <string name="date_picker_this_year" translatable="false"> This year</string>
    <string name="date_picker_last_year" translatable="false">Last year</string>
    <string name="date_picker_days_30" translatable="false">Last 30 days</string>
    <string name="date_picker_days_30_previous" translatable="false">Previous 30 days</string>

    <string name="contributors_content_sleep" translatable="false">Sleep</string>
    <string name="contributors_content_resting_heart_rate" translatable="false">Resting heart rate</string>
    <string name="contributors_content_resources" translatable="false">Resources</string>
    <string name="contributors_content_hrv" translatable="false">Hrv</string>

    <!-- Sleep data selection options -->
    <string name="sleep_data_left_selection" translatable="false">Sleep data - left selection</string>
    <string name="sleep_data_right_selection" translatable="false">Sleep data - right selection</string>
    <string name="total_time" translatable="false" >Total time</string>
    <string name="sleep_regularity" translatable="false">Sleep regularity</string>
    <string name="sleep_duration" translatable="false"> Sleep duration</string>
    <string name="nap_duration" translatable="false">Nap duration</string>
    <string name="min_sleep_hr" translatable="false">Min. sleep HR</string>
    <string name="avg_sleep_hr" translatable="false">Avg. sleep HR</string>
    <string name="max_sleep_spo2" translatable="false">Max. sleep SPO₂</string>
    <string name="training_duration" translatable="false">Training duration</string>
    <string name="wake_up_resources" translatable="false">Wake-up resources</string>
    <string name="none" translatable="false">None</string>
    <string name="recovery" translatable="false">Recovery</string>
    <string name="gain" translatable="false">Gain</string>
    <string name="recovery_state_contributors" translatable="false">Recovery state contributors</string>
    <string name="sleep" translatable="false">Sleep</string>
    <string name="hrv" translatable="false">HRV</string>
    <string name="stress" translatable="false">Stress</string>
    <string name="activity" translatable="false">Activity</string>
    <string name="spo2" translatable="false">SPO₂</string>
    <string name="unknown" translatable="false">Unknown</string>

    <string name="recovery_hrv_info_sheet_intensity_md" formatted="false" translatable="false">
        ## What is HRV?\n
        Heart rate variability (HRV) measures the variation in time intervals between heartbeats. It reflects autonomic nervous system (ANS) balance and provides insights into overall health and stress levels. HRV is a valuable tool for understanding autonomic function and promoting well-being.\n\n

        ## How should I read the data?\n
        For optimal HRV, values should remain within your normal range, ideally closer to the "Too high" limit. While higher HRV is generally associated with better health, it should always be interpreted relative to your baseline. Factors such as relaxation, physical and mental exertion, or illness (e.g., flu) can cause fluctuations in HRV.\n\n

        ## How to measure my HRV?\n
        1. Suunto measures your HRV during your sleep. To obtain HRV data, wear your watch while sleeping and ensure sleep tracking is enabled. Heart rate variability is continuously measured throughout the sleep period to calculate the average RMSSD value for the night. RMSSD (root mean square of successive differences) is a widely used metric for assessing HRV.\n\n

        2. Values explained\n
        a. Today\'s HRV value is derived from measurements taken during the previous night, while yesterday\'s HRV value refers to the night before.\n\n
        b. The 7-day average is calculated based on HRV measurements from the past 7 nights. To establish this average, at least 3 HRV measurements within a 7-day period are required.\n\n
        c. To determine your normal range, you need 14 HRV measurements taken over a span of 60 days.\n\n
    </string>
    <string name="recovery_rhr_info_sheet_title" translatable="false">About resting heart rate (RHR)</string>
    <string name="recovery_rhr_info_sheet_md" translatable="false" formatted="false">
        Resting heart rate (RHR) is the heart rate measured while at rest, reflecting cardiovascular health and fitness. A lower RHR typically indicates better heart efficiency and conditioning. Tracking RHR can also help assess recovery and adjust training and rest periods accordingly.
    </string>
    <string name="recovery_training_fatigue_info_sheet_title" translatable="false">Training fatigue</string>
    <string name="recovery_training_fatigue_info_sheet_md" translatable="false" formatted="false">
        Training fatigue plays a key role in recovery, as it helps stimulate adaptation.\n\n

        ## Fitness Status (TSB)\n
        Reflects your current fitness level based on training data and its impact on recovery.\n\n

        ## Training Stress Score (TSS)\n
        Measures training effort from today and yesterday, influencing recovery.\n\n

        ## Feelings\n
        Post-workout feedback that subjectively records body load. Providing accurate feedback helps improve the recovery index. Select how you feel after training:\n\n

        **Excellent:** Very easy, minimal effort\n\n
        **Good:** Easy and enjoyable\n\n
        **Average:** Challenging but manageable\n\n
        **Poor:** Difficult, but achievable\n\n
    </string>
    <string name="recovery_resources_info_sheet_title" translatable="false">About resources</string>
    <string name="recovery_resources_info_sheet_md" translatable="false" formatted="false">
        Resources reflect your daily recovery and energy expenditure, helping you monitor your physical state and adjust activity levels. Energy is generally restored during sleep.\n\n

        ## How to read the data?\n
        The chart displays four states based on Resource changes:\n\n

        **1. Recovering:** Resources rise quickly.\n
        a. **Scenario:** Deep relaxation, especially quality sleep, indicating optimal recovery.\n\n

        **2. Inactive:** Resources change slowly and unpredictably.\n
        a. **During Sleep:** Remains stable or gradually increases (excluding awake periods), with slow recovery.\n
        b. **During Wakefulness:** Can fluctuate slightly, depending on minor activities.\n\n

        **3. Active:** Resources decline, with the rate linked to exercise intensity and recovery.\n
        a. **Scenario:** Daily activities or exercise, where Resources change based on activity intensity and recovery time.\n\n

        **4. Stressed:** Resources drop rapidly.\n
        a. **Scenario:** Stress during wakefulness causes a quick decline in Resources, signaling a high-pressure state.\n\n

        ## How resources are measured?\n
        Resources are measured based on physiological state.\n\n

        **Active State:** Activity Recovery Time reflects recovery needs; longer recovery times suggest faster Resource depletion.\n\n

        **Inactive State:** Heart Rate Variability (HRV) gauges autonomic balance. High HRV indicates high Resources; low HRV signals low Resources. Key HRV metrics like RMSSD, Stress Index, and SDNN, alongside Heart Rate (HR), help assess stress levels.\n\n
    </string>

    <string name="about_heart_rate_title" translatable="false">About Heart rate</string>
    <string name="about_heart_rate_md" translatable="false" formatted="false">
        Heart rate (HR) refers to the number of heartbeats per minute and is a key indicator of heart health and fitness. Understanding heart rate variations can help optimize training and improve overall well-being. Heart rate fluctuates during sleep, exercise, and daily activities. Resting heart rate and minimum sleeping hear rate tend to be lower, aiding in recovery assessment and guiding training intensity. Monitoring daily heart rate helps track trends, detect abnormalities, and maintain awareness of overall health.
    </string>

    <string name="about_calories_title" translatable="false">About calories</string>
    <string name="about_calories_md" translatable="false" formatted="false">
        Total calories represent the energy consumed in a day, including basal metabolic rate (BMR) and activity calories. BMR is the minimum energy required to maintain essential physiological functions, such as breathing, heartbeat, and temperature regulation, while at rest. Active calories account for energy expended during exercise and daily activities. Increasing BMR can be achieved by building muscle mass, engaging in regular exercise, staying hydrated, and maintaining good sleep habits.
    </string>
</resources>
