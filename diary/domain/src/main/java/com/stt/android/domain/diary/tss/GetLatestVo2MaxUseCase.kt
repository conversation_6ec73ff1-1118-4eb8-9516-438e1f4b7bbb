package com.stt.android.domain.diary.tss

import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.workouts.extensions.intensity.FitnessExtensionDataSource
import com.stt.android.utils.toEpochMilli
import java.time.LocalDateTime
import javax.inject.Inject

class GetLatestVo2MaxUseCase @Inject constructor(
    private val fitnessExtensionDataSource: FitnessExtensionDataSource,
) {
    suspend fun getFitnessExtensionWithLatestVo2Max(username: String): FitnessExtension? =
        fitnessExtensionDataSource.findLatestVo2MaxInRangeByTime(
            username = username,
            vo2MaxRange = 1f..99f,
            untilMillis = LocalDateTime.now().toEpochMilli()
        )

    suspend fun getLatestFitnessAge(username: String): Int? =
        fitnessExtensionDataSource.findLatestFitnessAge(
            username = username
        )
}
