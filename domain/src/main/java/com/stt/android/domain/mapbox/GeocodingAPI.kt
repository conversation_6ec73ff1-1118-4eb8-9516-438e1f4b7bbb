package com.stt.android.domain.mapbox

import java.util.Locale

interface GeocodingAPI {
    /**
     * Fetches the location's place
     */
    suspend fun fetchPlaceForLocation(latitude: Double, longitude: Double): Place?

    fun initGeocoder(locale: Locale)
}

data class Place(
    val fullAddress: String? = null,
    val city: String? = null,
    val province: String? = null,
    val country: String? = null,
)
