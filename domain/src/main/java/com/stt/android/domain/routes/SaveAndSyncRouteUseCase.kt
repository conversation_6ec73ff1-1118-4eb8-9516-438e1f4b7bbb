package com.stt.android.domain.routes

import javax.inject.Inject

/**
 * Use case for syncing and persisting the state of [Route] instance to watch and local store.
 */
class SaveAndSyncRouteUseCase @Inject constructor(
    private val saveRouteUseCase: SaveRouteUseCase,
    private val routeSyncWithWatchJobScheduler: RouteSyncWithWatchJobScheduler,
) {
    suspend fun saveAndSyncRoute(route: Route, navigateOngoing: Boolean = false) {
        saveRouteUseCase.saveRoute(route)
        routeSyncWithWatchJobScheduler.schedule(navigateOngoing)
    }
}
