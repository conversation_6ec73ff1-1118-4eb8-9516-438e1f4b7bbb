package com.stt.android.domain.firmware

import com.suunto.connectivity.firmware.WatchFirmwareInfo

data class FirmwareInfo(
    val deviceName: String,
    val firmwareUploadDate: String,
    val latestFirmwareURI: String,
    val latestFirmwareVersion: String,
    val version: String,
    val releaseType: String?,
    val forceUpdate: Boolean = false,
    val versionLog: String = "",
    )

fun FirmwareInfo.toBaseWatchFirmware(): BaseWatchFirmwareInfo {
    return BaseWatchFirmwareInfo(
        deviceName = deviceName,
        firmwareUploadDate = firmwareUploadDate,
        latestFirmwareURI = latestFirmwareURI,
        latestFirmwareVersion = latestFirmwareVersion,
        version = version,
        releaseType = releaseType,
        forceUpdate = forceUpdate
    )
}

fun FirmwareInfo.toWatchFirmware(): WatchFirmwareInfo {
    return WatchFirmwareInfo(
        deviceName = deviceName,
        firmwareUploadDate = firmwareUploadDate,
        latestFirmwareURI = latestFirmwareURI,
        latestFirmwareVersion = latestFirmwareVersion,
        version = version,
        versionLog = versionLog,
        releaseType = releaseType,
        forceUpdate = forceUpdate
    )
}
