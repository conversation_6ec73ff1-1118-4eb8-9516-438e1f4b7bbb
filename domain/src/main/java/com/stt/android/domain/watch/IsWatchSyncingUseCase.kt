package com.stt.android.domain.watch

import com.stt.android.TestOpen
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

@TestOpen
class IsWatchSyncingUseCase
@Inject constructor(
    private val repository: WatchInfoRepository
) {
    /**
     * Fetches the information whether the watch is syncing and keeps listening
     * for state changes.
     */
    fun isWatchSyncing(): Flow<Boolean> = repository.isSyncing()
}
