package com.stt.android.domain.sportmodes

import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import io.reactivex.Completable
import io.reactivex.Scheduler
import kotlinx.coroutines.rx2.rxCompletable
import javax.inject.Inject

/**
 * Use case responsible for initializing sport mode component
 */
class InitSportModeComponentUseCase
@Inject constructor(
    private val repository: SportModesRepository,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(ioThread, mainThread) {

    /**
     * inits sport mode component
     */
    fun init(client: String): Completable {
        return rxCompletable { repository.initSportModeComponent(client) }
    }

    fun close(client: String) {
        repository.closeSportModeComponent(client)
    }
}
