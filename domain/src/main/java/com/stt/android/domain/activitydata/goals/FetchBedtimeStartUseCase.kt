package com.stt.android.domain.activitydata.goals

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case responsible for getting bedtime start from data source
 */
class FetchBedtimeStartUseCase @Inject constructor(
    private val repository: ActivityDataGoalRepository,
) {
    /**
     * fetches bedtime start from the watches in seconds since midnight
     */
    operator fun invoke(): Flow<Int> = repository.fetchBedtimeStart()
}
