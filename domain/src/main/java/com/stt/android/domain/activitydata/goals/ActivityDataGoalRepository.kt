package com.stt.android.domain.activitydata.goals

import com.suunto.algorithms.data.Energy
import kotlinx.coroutines.flow.Flow
import kotlin.time.Duration

interface ActivityDataGoalRepository {
    fun fetchStepsGoal(): Flow<Int>
    fun fetchEnergyGoal(): Flow<Energy>
    fun fetchSleepGoal(): Flow<Duration>
    fun fetchBedtimeStart(): Flow<Int>
    fun fetchBedtimeEnd(): Flow<Int>
    suspend fun setStepsGoal(goal: Int)
    suspend fun setEnergyGoal(goal: Energy)
    suspend fun setSleepGoal(goal: Duration)
    suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int)
    suspend fun fetchLocalStepsGoal(): Int
    suspend fun fetchLocalEnergyGoal(): Energy
    suspend fun fetchLocalSleepGoal(): Duration
}
