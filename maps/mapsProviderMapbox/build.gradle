plugins {
    id 'stt.android.plugin.library'
}

android {
    namespace 'com.stt.android.maps.mapbox'
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.maps)

    implementation libs.androidx.corektx

    implementation(libs.mapbox) {
        // Exclude the lifecycle plugin which comes bundled with RC5. Mapbox implies, it will be
        // disabled by default in later releases. It handles lifecycle calls to mapbox MapView
        // automatically. The plugin was introduced in RC5. Best to let it mature before removing
        // manual lifecycle calls and taking it into use.
        exclude group: 'com.mapbox.plugin', module: 'maps-lifecycle'
    }

    implementation libs.mapbox.search

    implementation libs.rxjava2
    implementation libs.rxjava2.android
    implementation libs.moshi
}
