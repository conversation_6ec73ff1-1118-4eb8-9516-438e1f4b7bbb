package com.stt.android.maps

import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Typeface
import android.text.TextPaint
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.ColorUtils
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.graphics.scale
import androidx.core.util.TypedValueCompat
import kotlin.math.roundToInt

sealed class SuuntoBitmapDescriptor(
    val name: String
) {
    abstract fun asBitmap(scaleFactor: Float = 1.0f): Bitmap?
}

data class SuuntoBitmapResourceDescriptor(
    private val context: Context,
    @DrawableRes val resourceId: Int,
    val isVectorDrawable: Boolean = false
) : SuuntoBitmapDescriptor("map_icon_resource_id_$resourceId") {

    override fun asBitmap(scaleFactor: Float): Bitmap? {
        if (resourceId == Resources.ID_NULL) {
            return null
        }

        val drawable = AppCompatResources.getDrawable(context, resourceId)
        return if (drawable != null) {
            val originalWidth = drawable.intrinsicWidth
            val originalHeight = drawable.intrinsicHeight
            val scaledWidth = (originalWidth * scaleFactor).toInt()
            val scaledHeight = (originalHeight * scaleFactor).toInt()
            val bitmap = createBitmap(scaledWidth, scaledHeight)
            drawable.bounds = Rect(0, 0, scaledWidth, scaledHeight)
            drawable.draw(Canvas(bitmap))
            bitmap
        } else {
            null
        }
    }

    fun getNoVectorBitmap(
        resourceId: Int,
        scaleFactor: Float
    ): Bitmap {
        val originalBitmap = BitmapFactory.decodeResource(context.resources, resourceId)
        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height
        val scaledWidth = (originalWidth * scaleFactor).toInt()
        val scaledHeight = (originalHeight * scaleFactor).toInt()
        return originalBitmap.scale(scaledWidth, scaledHeight, false)
    }
}

data class SuuntoLocationForegroundDescriptor(
    private val context: Context,
    @ColorRes private val tintColor: Int
) : SuuntoBitmapDescriptor("map_icon_location_foreground_$tintColor") {

    private val bitmap: Bitmap by lazy {
        val color = ContextCompat.getColor(context, tintColor)
        drawLocationForeground(color)
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawLocationForeground(@ColorInt color: Int): Bitmap {
        val centerRadius =
            context.resources.getDimensionPixelSize(R.dimen.map_dot_marker_inner_radius)

        val canvas = Canvas()
        val bitmap = createBitmap(centerRadius * 2, centerRadius * 2)

        val center = centerRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = color
        }
        canvas.drawCircle(center, center, centerRadius.toFloat(), paint)

        return bitmap
    }
}

data class SuuntoLocationBackgroundDescriptor(
    private val context: Context
) : SuuntoBitmapDescriptor("map_icon_location_background") {
    private val bitmap: Bitmap by lazy {
        drawLocationBackground()
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawLocationBackground(): Bitmap {
        val outerRadius =
            context.resources.getDimensionPixelSize(R.dimen.map_dot_marker_outer_radius)

        val canvas = Canvas()
        val bitmap = createBitmap(outerRadius * 2, outerRadius * 2)

        val center = outerRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = ContextCompat.getColor(context, R.color.map_dot_border_color)
        }

        canvas.drawCircle(center, center, outerRadius.toFloat(), paint)
        return bitmap
    }
}

data class SuuntoActivityDotDescriptor(
    private val context: Context,
    @ColorRes private val tintColor: Int,
    private val isMarkerSelected: Boolean,
    @ColorRes private val borderColorRes: Int = R.color.map_dot_border_color,
    @DimenRes private val centerRadiusRes: Int = R.dimen.map_dot_marker_inner_radius
) : SuuntoBitmapDescriptor("map_icon_activity_dot_${tintColor}_$isMarkerSelected") {

    private val bitmap: Bitmap by lazy {
        val color = ContextCompat.getColor(context, tintColor)
        val borderColor = ContextCompat.getColor(context, borderColorRes)
        val centerRadius =
            context.resources.getDimensionPixelSize(centerRadiusRes)
        if (isMarkerSelected) {
            drawSelectedMapDot(color)
        } else {
            drawMapDot(color, borderColor, centerRadius)
        }
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawMapDot(
        @ColorInt color: Int,
        @ColorInt borderColor: Int,
        centerRadius: Int
    ): Bitmap {
        val outerRadius =
            context.resources.getDimensionPixelSize(R.dimen.map_dot_marker_outer_radius)

        val canvas = Canvas()
        val bitmap = createBitmap(outerRadius * 2, outerRadius * 2)

        val center = outerRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = borderColor
        }

        canvas.drawCircle(center, center, outerRadius.toFloat(), paint)

        paint.color = color
        canvas.drawCircle(center, center, centerRadius.toFloat(), paint)

        return bitmap
    }

    private fun drawSelectedMapDot(@ColorInt color: Int): Bitmap {
        val outerRingRadius =
            context.resources.getDimensionPixelSize(R.dimen.selected_map_dot_marker_outer_radius)
        val innerRingRadius =
            context.resources.getDimensionPixelSize(R.dimen.selected_map_dot_marker_inner_radius)
        val centerRadius =
            context.resources.getDimensionPixelSize(R.dimen.selected_map_dot_marker_center_radius)

        val canvas = Canvas()
        val bitmap = createBitmap(outerRingRadius * 2, outerRingRadius * 2)

        val center = outerRingRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = color
        }

        canvas.drawCircle(center, center, outerRingRadius.toFloat(), paint)

        paint.color = ContextCompat.getColor(context, R.color.selected_map_dot_ring_color)
        canvas.drawCircle(center, center, innerRingRadius.toFloat(), paint)

        paint.color = ContextCompat.getColor(context, R.color.selected_map_dot_center_color)
        canvas.drawCircle(center, center, centerRadius.toFloat(), paint)

        return bitmap
    }
}

data class SuuntoActivityNumberDotDescriptor(
    private val context: Context,
    @ColorRes private val textColorRes: Int,
    @ColorRes private val bgColorRes: Int,
    private val number: Int,
) : SuuntoBitmapDescriptor("map_icon_activity_num_dot_${textColorRes}_${bgColorRes}_$number") {

    private val paint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ContextCompat.getColor(context, bgColorRes)
            style = Paint.Style.FILL
        }
    }

    private val textPaint: TextPaint by lazy {
        TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ContextCompat.getColor(context, textColorRes)
            textSize = context.resources.getDimensionPixelSize(R.dimen.map_lap_marker_text_size).toFloat()
            setTypeface(Typeface.DEFAULT_BOLD)
        }
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = drawMapDot(scaleFactor)

    private fun drawMapDot(scaleFactor: Float): Bitmap {
        val radius =
            (context.resources.getDimensionPixelSize(R.dimen.map_lap_marker_radius) * scaleFactor).toInt()
        return createBitmap(radius * 2, radius * 2).applyCanvas {
            val center = radius.toFloat()
            drawCircle(center, center, radius.toFloat(), paint)

            // https://chrisbanes.me/posts/measuring-text/
            val text = number.toString()
            val textBounds = Rect()
            val labelPaint = textPaint.apply { this.textSize *= scaleFactor }
            labelPaint.getTextBounds(text, 0, text.length, textBounds)
            val textWidth = labelPaint.measureText(text)
            val textHeight = textBounds.height()
            drawText(text, center - (textWidth / 2f), center + (textHeight / 2f), labelPaint)
        }
    }
}

data class SuuntoFlagDescriptor(
    private val context: Context,
    @ColorRes private val textColorRes: Int,
    @ColorRes private val bgColorRes: Int,
    private val line1: String,
    private val line2: String
) : SuuntoBitmapDescriptor("map_icon_flag_${textColorRes}_${bgColorRes}_${line1}_$line2") {

    private val paint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ContextCompat.getColor(context, bgColorRes)
            style = Paint.Style.FILL
        }
    }

    private val line1Paint: TextPaint by lazy {
        TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ContextCompat.getColor(context, textColorRes)
            textSize = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_line1_text_size).toFloat()
        }
    }

    private val line2Paint: TextPaint by lazy {
        TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ContextCompat.getColor(context, textColorRes)
            textSize = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_line2_text_size).toFloat()
            setTypeface(Typeface.DEFAULT_BOLD)
        }
    }

    private val bitmap: Bitmap by lazy {
        drawMapDot()
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawMapDot(): Bitmap {
        val radius = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_radius)
        val padding = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_padding)
        val poleWidth = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_pole_width)
        val gap = context.resources.getDimensionPixelSize(R.dimen.map_flag_marker_text_gap)
        // https://chrisbanes.me/posts/measuring-text/
        val textBounds = Rect()
        line1Paint.getTextBounds(line1, 0, line1.length, textBounds)
        val line1Width = line1Paint.measureText(line1)
        val line1Height = textBounds.height().toFloat()
        line2Paint.getTextBounds(line2, 0, line2.length, textBounds)
        val line2Width = line2Paint.measureText(line2)
        val line2Height = textBounds.height().toFloat()
        val textWidth = line1Width.coerceAtLeast(line2Width)
        val textHeight = line1Height + line2Height + gap
        val flagWidth = textWidth + padding * 2
        val flagHeight = textHeight + padding * 2
        val bitmapWidth = flagWidth.roundToInt()
        val bitmapHeight = (flagHeight * 2.5).roundToInt()
        return createBitmap(bitmapWidth, bitmapHeight).applyCanvas {
            // draw pole
            drawRect(0f, radius.toFloat(), poleWidth.toFloat(), bitmapHeight.toFloat(), paint)
            // draw flag
            drawRoundRect(0f, 0f, flagWidth, flagHeight, radius.toFloat(), radius.toFloat(), paint)
            // draw text
            drawText(line2, padding.toFloat(), flagHeight - padding, line2Paint)
            drawText(line1, padding.toFloat(), flagHeight - padding - line2Height - gap, line1Paint)
        }
    }
}

data class SuuntoMyLocationDescriptor(
    private val context: Context,
) : SuuntoBitmapDescriptor("map_icon_my_location") {

    private val bitmap: Bitmap by lazy {
        drawMapDot()
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawMapDot(): Bitmap {
        val resources = context.resources
        val displayMetrics = resources.displayMetrics
        val size = TypedValueCompat.dpToPx(70f, displayMetrics).toInt()
        val dotSize = TypedValueCompat.dpToPx(16f, displayMetrics).toInt()
        return createBitmap(size, size + dotSize).applyCanvas {
            ResourcesCompat.getDrawable(resources, R.drawable.location_bearing, null)?.apply {
                setBounds(0, 0, intrinsicWidth, intrinsicHeight)
                draw(this@applyCanvas)
            }
            ResourcesCompat.getDrawable(resources, R.drawable.location_puck, null)?.apply {
                val offset = (size - dotSize) / 2
                setBounds(offset, offset, offset + intrinsicWidth, offset + intrinsicHeight)
                draw(this@applyCanvas)
            }
        }
    }
}

data class SuuntoPersonalHeatMapDotDescriptor(
    private val context: Context,
    @ColorRes private val tintColor: Int,
    @DimenRes private val radiusRes: Int,
    private val isMarkerSelected: Boolean
) : SuuntoBitmapDescriptor("map_icon_heatmap_dot_${tintColor}_${radiusRes}_$isMarkerSelected") {

    private fun getBitmap(scaleFactor: Float): Bitmap {
        val color = ContextCompat.getColor(context, tintColor)
        return if (isMarkerSelected) {
            drawSelectedMapDot(color, scaleFactor)
        } else {
            drawMapDot(color, scaleFactor)
        }
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = getBitmap(scaleFactor)

    private fun drawMapDot(@ColorInt color: Int, scaleFactor: Float): Bitmap {
        val bitmapRadius =
            (context.resources.getDimensionPixelSize(R.dimen.map_dot_marker_heat_map_radius) * scaleFactor).toInt()
        val radius = context.resources.getDimensionPixelSize(radiusRes) * scaleFactor
        val canvas = Canvas()
        val bitmap = createBitmap(bitmapRadius * 2, bitmapRadius * 2)

        val center = bitmapRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.STROKE
            isAntiAlias = true
            this.color = color
            strokeWidth = 2.0f
        }
        canvas.drawCircle(center, center, radius, paint)

        paint.color = ColorUtils.setAlphaComponent(color, context.resources.getInteger(R.integer.personal_heat_map_alpha))
        paint.style = Paint.Style.FILL
        canvas.drawCircle(center, center, radius, paint)

        return bitmap
    }

    private fun drawSelectedMapDot(@ColorInt color: Int, scaleFactor: Float): Bitmap {
        val bitmapRadius =
            (context.resources.getDimensionPixelSize(R.dimen.map_dot_marker_heat_map_radius) * scaleFactor).toInt()
        val radius = context.resources.getDimensionPixelSize(radiusRes) * scaleFactor
        val canvas = Canvas()
        val bitmap = createBitmap(bitmapRadius * 2, bitmapRadius * 2)

        val center = bitmapRadius.toFloat()

        canvas.setBitmap(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = color
        }
        canvas.drawCircle(center, center, radius, paint)
        return bitmap
    }
}

data class SuuntoLabelDescriptor(
    private val context: Context,
    @ColorRes private val backgroundColor: Int,
    @ColorRes private val textColor: Int,
    private val text: String,
    private val showEnd: Boolean = true
) : SuuntoBitmapDescriptor("map_label_distance_${backgroundColor}_${textColor}_$text") {

    private val rectBounds = RectF()

    private val bitmap: Bitmap by lazy {
        val backgroundColor = ContextCompat.getColor(context, backgroundColor)
        val textColor = ContextCompat.getColor(context, textColor)
        drawLabel(backgroundColor, textColor, text, showEnd)
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = bitmap

    private fun drawLabel(@ColorInt backgroundColor: Int, @ColorInt textColor: Int, text: String, showEnd: Boolean = true): Bitmap {
        val labelRadius =
            context.resources.getDimensionPixelSize(R.dimen.map_label_marker_radius)
        val labelHeight =
            context.resources.getDimensionPixelSize(R.dimen.map_label_marker_height)
        val paddingEnd = context.resources.getDimensionPixelSize(R.dimen.map_label_marker_padding_end)
        val paddingMarker = context.resources.getDimensionPixelSize(R.dimen.map_label_marker_padding_marker)
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            this.color = backgroundColor
        }
        val canvas = Canvas()

        paint.textSize = context.resources.getDimensionPixelSize(R.dimen.map_label_marker_text_size).toFloat()
        val textWidth = paint.measureText(text)

        val labelWidth = textWidth + paddingEnd + paddingMarker
        val bitmap = createBitmap(labelWidth.toInt(), labelHeight)
        canvas.setBitmap(bitmap)

        rectBounds.set(0f, 0f, labelWidth, labelHeight.toFloat())
        canvas.drawRoundRect(rectBounds, labelRadius.toFloat(), labelRadius.toFloat(), paint)

        paint.color = textColor
        val x = if (showEnd) paddingMarker else paddingEnd
        val y = labelHeight / 2 - (paint.descent() + paint.ascent()) / 2
        canvas.drawText(text, x.toFloat(), y, paint)

        return bitmap
    }
}

data class SuuntoTopRoutesMarkerDotDescriptor(
    private val context: Context,
    @ColorRes private val bgColorRes: Int,
    private val bgColorAlpha: Int,
    private val isLargeMarker: Boolean = false,
    private val isClusterMakerWithNumber: Boolean = false
) : SuuntoBitmapDescriptor("map_icon_top_route_num_dot_${bgColorRes}") {

    private val fillPaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.FILL
            color = ContextCompat.getColor(context, bgColorRes)
            alpha = bgColorAlpha
        }
    }

    private val strokePaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            color = Color.BLACK
            strokeWidth = context.resources.getDimension(R.dimen.map_top_route_marker_border_width)
        }
    }

    override fun asBitmap(scaleFactor: Float): Bitmap = if (isLargeMarker) {
        drawLagerMapDot(scaleFactor)
    } else if (isClusterMakerWithNumber) {
        drawNumberMapDot(scaleFactor)
    } else {
        drawMapDot(scaleFactor)
    }

    private fun drawMapDot(scaleFactor: Float): Bitmap {
        val radius =
            (context.resources.getDimensionPixelSize(R.dimen.map_top_route_marker_radius) * scaleFactor).toInt()
        val strokeWidth =
            (context.resources.getDimension(R.dimen.map_top_route_marker_border_width) * scaleFactor).toInt()
        return createBitmap(radius * 2, radius * 2).applyCanvas {
            val center = radius.toFloat()
            drawCircle(center, center, radius.toFloat() - strokeWidth, fillPaint)
            drawCircle(center, center, radius.toFloat() - strokeWidth, strokePaint)
        }
    }

    private fun drawLagerMapDot(scaleFactor: Float): Bitmap {
        val width =
            context.resources.getDimensionPixelSize(R.dimen.map_top_route_marker_large_with) * scaleFactor
        val height =
            context.resources.getDimensionPixelSize(R.dimen.map_top_route_marker_large_height) * scaleFactor
        val cornerRadius =
            context.resources.getDimensionPixelSize(R.dimen.map_top_route_marker_large_radius) * scaleFactor
        return createBitmap(width.toInt(), height.toInt()).applyCanvas {
            val rect = RectF(0f, 0f, width, height)
            drawRoundRect(rect, cornerRadius, cornerRadius, fillPaint)
        }
    }

    private fun drawNumberMapDot(scaleFactor: Float): Bitmap {
        val radius =
            (context.resources.getDimensionPixelSize(R.dimen.map_top_route_marker_large_radius) * scaleFactor).toInt()
        return createBitmap(radius * 2, radius * 2).applyCanvas {
            val center = radius.toFloat()
            drawCircle(center, center, radius.toFloat(), fillPaint)
        }
    }
}
