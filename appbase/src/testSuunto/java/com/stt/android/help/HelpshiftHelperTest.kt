package com.stt.android.help

import android.content.SharedPreferences
import com.helpshift.support.FaqTagFilter
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.spy

@RunWith(MockitoJUnitRunner::class)
class HelpshiftHelperTest {

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    private lateinit var helpshiftHelper: HelpshiftHelper

    @Before
    fun setup() {
        helpshiftHelper = spy(HelpshiftHelper())
    }

    @Test
    fun `correct tag filter creation if suunto 9 is paired`() {
        doReturn(SuuntoConnectivityConstants.SUUNTO_9_VARIANT_NAME_GLOBAL)
            .`when`(helpshiftHelper).getVariantName(any())
        val expected = FaqTagFilter(
            FaqTagFilter.Operator.OR,
            arrayOf(HelpshiftTag.SUU_SUUNTO_9_BARO.tagName, HelpshiftTag.SUU_COMMON.tagName)
        )
        assertThat(helpshiftHelper.getTagFilter(sharedPreferences)!!.tags).isEqualTo(expected.tags)
        assertThat(helpshiftHelper.getTagFilter(sharedPreferences)!!.operator).isEqualTo(expected.operator)
    }

    @Test
    fun `correct tag filter creation if no watch is paired`() {
        doReturn("").`when`(helpshiftHelper).getVariantName(any())
        val expected = FaqTagFilter(
            FaqTagFilter.Operator.OR,
            arrayOf(HelpshiftTag.SUU_NO_DEVICE.tagName, HelpshiftTag.SUU_COMMON.tagName)
        )
        assertThat(helpshiftHelper.getTagFilter(sharedPreferences)!!.tags).isEqualTo(expected.tags)
        assertThat(helpshiftHelper.getTagFilter(sharedPreferences)!!.operator).isEqualTo(expected.operator)
    }
}
