package com.stt.android.domain;

import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.ActivityType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkoutHeaderTest {
    @Test
    public void testWorkoutId() {
        // "local" workout header
        WorkoutHeader localHeader =
            WorkoutHeader.local(0.0, 0.0, ActivityType.DEFAULT, 0.0, null, null, null, null,
                1234567890L, 1234567891L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 0,
                0, 0, 0, false, null, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null);
        Assert.assertEquals(-579392529, localHeader.getId());

        WorkoutHeader anotherLocalHeader =
            WorkoutHeader.local(0.0, 0.0, ActivityType.DEFAULT, 0.0, null, null, null, null,
                9876543210L, 9876543211L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 0,
                0, 0, 0, false, null, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null);
        Assert.assertEquals(43016431, anotherLocalHeader.getId());

        // "manual" workout header
        WorkoutHeader manualHeader =
            WorkoutHeader.manual(0.0, 0.0, ActivityType.DEFAULT, 0.0, null, null, null, null,
                1234567890L, 1234567891L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 0,
                0, 0, 0, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null);
        Assert.assertEquals(-579392529, manualHeader.getId());

        WorkoutHeader anotherManualHeader =
            WorkoutHeader.manual(0.0, 0.0, ActivityType.DEFAULT, 0.0, null, null, null, null,
                9876543210L, 9876543211L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 0,
                0, 0, 0, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null);
        Assert.assertEquals(43016431, anotherManualHeader.getId());

        // "remote" workout header
        WorkoutHeader remoteHeader =
            WorkoutHeader.remote("workout_key", 0.0, 0.0, ActivityType.DEFAULT, 0.0, null, null,
                null, null, 1234567890L, 1234567891L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0, 0.0,
                0, 0, 0, 0, 0, 0, 0, false, null, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null, null);
        Assert.assertEquals(-65038524, remoteHeader.getId());

        WorkoutHeader anotherRemoteHeader =
            WorkoutHeader.remote("another_workout_key", 0.0, 0.0, ActivityType.DEFAULT, 0.0, null,
                null, null, null, 9876543210L, 9876543211L, 0.5, 0.0, "random", 0.0, 0.0, 0.0, 0.0,
                0.0, 0, 0, 0, 0, 0, 0, 0, false, null, 0, 0.0, 0.0, 0, 0.0, 0.0, null, null, null);
        Assert.assertEquals(-391112440, anotherRemoteHeader.getId());
    }
}
