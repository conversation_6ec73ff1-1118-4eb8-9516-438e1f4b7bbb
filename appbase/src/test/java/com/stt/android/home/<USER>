package com.stt.android.home

import android.content.SharedPreferences
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.google.common.truth.Truth.assertThat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboard.BaseDashboardGridViewModel
import com.stt.android.home.dashboard.DashboardAnalytics
import com.stt.android.home.dashboard.widget.LoadedWidgetData
import com.stt.android.home.dashboard.widget.WidgetDataFetcher
import com.stt.android.home.dashboard.widget.WidgetType
import com.stt.android.home.dashboard.widget.customization.CheckPremiumSubscriptionForWidgetTypeUseCase
import com.stt.android.home.dashboard.widget.customization.CheckWidgetPlacementAllowedUseCase
import com.stt.android.home.dashboard.widget.customization.InMemorySelectedDashboardWidgetsDataSource
import com.stt.android.home.dashboard.widget.customization.SelectedDashboardWidgetsRepository
import com.stt.android.home.dashboardnew.customization.guidance.DashboardCustomizationGuidanceTrigger
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleType
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleUpdateUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.utils.FixedFirstDayOfTheWeekCalendarProvider
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.Dashboard.WIDGETS_PER_PAGE
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.runCurrent
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever
import java.time.DayOfWeek
import java.util.Locale

@RunWith(MockitoJUnitRunner::class)
class BaseDashboardGridViewModelTest {
    @JvmField
    @Rule
    val archTaskExecutorRule = InstantTaskExecutorRule()

    @Mock
    lateinit var currentUserController: CurrentUserController

    @Mock
    lateinit var getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase

    @Mock
    lateinit var diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder

    @Mock
    lateinit var mapSelectionModel: MapSelectionModel

    @Mock
    lateinit var dashboardAnalytics: DashboardAnalytics

    @Mock
    lateinit var locationSource: SuuntoLocationSource

    @Mock
    lateinit var dashboardPreferences: SharedPreferences

    @Mock
    lateinit var exploreMapPreferences: SharedPreferences

    @Mock
    lateinit var widgetDataFetcher: WidgetDataFetcher

    @Mock
    lateinit var dashboardCustomizationGuidanceTrigger: DashboardCustomizationGuidanceTrigger

    @Mock
    lateinit var observableMenstrualCycleUpdateUseCase: ObservableMenstrualCycleUpdateUseCase

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    private val calendarProvider = FixedFirstDayOfTheWeekCalendarProvider(
        Locale.US,
        DayOfWeek.SUNDAY
    )

    private lateinit var selectedDashboardWidgetsRepository: SelectedDashboardWidgetsRepository

    private val checkWidgetPlacementAllowedUseCase = CheckWidgetPlacementAllowedUseCase(
        AVAILABLE_WIDGET_TYPES
    )

    @Mock
    lateinit var isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase

    @Mock
    lateinit var checkPremiumSubscriptionForWidgetTypeUseCase: CheckPremiumSubscriptionForWidgetTypeUseCase

    private val scheduler = TestCoroutineScheduler()
    private val testDispatcher = StandardTestDispatcher(scheduler)
    private val coroutinesDispatchers = object : CoroutinesDispatchers {
        override val main = testDispatcher
        override val computation = testDispatcher
        override val io = testDispatcher
    }

    private lateinit var viewModel: BaseDashboardGridViewModel

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        whenever(
            dashboardPreferences.getString(
                eq(STTConstants.DashboardPreferences.KEY_GRANULARITY),
                any()
            )
        ).thenReturn(MyTracksGranularity.Type.THIS_MONTH.value)

        selectedDashboardWidgetsRepository = SelectedDashboardWidgetsRepository(
            AVAILABLE_WIDGET_TYPES,
            InMemorySelectedDashboardWidgetsDataSource(),
            widgetDataFetcher,
            dashboardPreferences,
            coroutinesDispatchers
        )

        whenever(currentUserController.username).thenReturn("test-user")
        runBlocking {
            whenever(getWorkoutStatisticsWithSummaryUseCase.runWithMultipleTimePeriods(any())).thenReturn(
                List(3) {
                    DailyWorkoutStatisticsWithSummary(
                        emptyMap(),
                        emptyMap(),
                        DiaryCalendarTotalValues.EMPTY,
                        emptyList()
                    )
                }
            )
        }

        whenever(diaryCalendarListContainerBuilder.getBubbleType(any(), any()))
            .thenReturn(DiaryBubbleType.NoBubbleType)

        whenever(widgetDataFetcher.widgetDataFlow()).thenReturn(
            flowOf(LoadedWidgetData())
        )

        runBlocking {
            whenever(dashboardCustomizationGuidanceTrigger.shouldShowCustomizationGuidance())
                .thenReturn(false)

            whenever(isSubscribedToPremiumUseCase.invoke())
                .thenReturn(flowOf(true))
            whenever(checkPremiumSubscriptionForWidgetTypeUseCase.hasValidSubscription(any()))
                .thenReturn(true)
            whenever(checkPremiumSubscriptionForWidgetTypeUseCase.getWidgetTypesBlockedByPremiumRequirement())
                .thenReturn(emptySet())
        }

        viewModel = object : BaseDashboardGridViewModel(
            currentUserController,
            getWorkoutStatisticsWithSummaryUseCase,
            diaryCalendarListContainerBuilder,
            mapSelectionModel,
            dashboardAnalytics,
            locationSource,
            exploreMapPreferences,
            widgetDataFetcher,
            calendarProvider,
            selectedDashboardWidgetsRepository,
            checkWidgetPlacementAllowedUseCase,
            isSubscribedToPremiumUseCase,
            checkPremiumSubscriptionForWidgetTypeUseCase,
            dashboardCustomizationGuidanceTrigger,
            observableMenstrualCycleUpdateUseCase,
            sharedPreferences,
            Schedulers.trampoline(),
            Schedulers.trampoline(),
            coroutinesDispatchers
        ) {
            override suspend fun isHrvSupported(): Boolean = true
        }

        // Bring the ViewModel to a state where it has the initial ViewState ready.
        // The widget data isn't loaded before something observes the viewState, and needs to
        // schedule & run several coroutines so a call to scheduler.runCurrent() is also needed
        viewModel.loadData()
        viewModel.viewState.observeForever { }
        scheduler.runCurrent()
    }

    @Test
    fun `opens customization mode with widget long click`() = runTest {
        assertThat(viewModel.viewState.value?.data?.widgetCustomizationModeEnabled).isFalse()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage?.flatten()).doesNotContain(WidgetType.ADD_NEW_WIDGET)

        viewModel.viewState.value?.data?.onWidgetLongClicked?.invoke(0, 0)
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetCustomizationModeEnabled).isTrue()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetCustomizationModeEnabled).isFalse()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage?.flatten()).doesNotContain(WidgetType.ADD_NEW_WIDGET)
    }

    @Test
    fun `inserts add new widget during customization mode when no selected widgets`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(listOf())

        viewModel.openDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(listOf(listOf(WidgetType.ADD_NEW_WIDGET)))

        viewModel.closeDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEmpty()
    }

    @Test
    fun `inserts add new widgets during customization mode when existing page has room`() = runTest {
        val storedWidgets = listOf(
            listOf(
                WidgetType.PROGRESS,
                WidgetType.TRAINING,
                WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH
            )
        )

        val expectedWithAddNewWidgets = listOf(
            listOf(
                WidgetType.PROGRESS,
                WidgetType.TRAINING,
                WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                WidgetType.ADD_NEW_WIDGET
            ),
            listOf(
                WidgetType.ADD_NEW_WIDGET
            )
        )

        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(storedWidgets)

        viewModel.openDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(expectedWithAddNewWidgets)

        viewModel.closeDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(storedWidgets)
    }

    @Test
    fun `inserts add new widget during customization mode when existing page does not have room`() = runTest {
        val storedWidgets = listOf(
            listOf(
                WidgetType.PROGRESS,
                WidgetType.TRAINING,
                WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                WidgetType.TOTAL_DURATION_THIS_MONTH
            )
        )

        val expectedWithAddNewWidget = listOf(
            listOf(
                WidgetType.PROGRESS,
                WidgetType.TRAINING,
                WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                WidgetType.TOTAL_DURATION_THIS_MONTH
            ),
            listOf(
                WidgetType.ADD_NEW_WIDGET
            )
        )

        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(storedWidgets)

        viewModel.openDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(expectedWithAddNewWidget)

        viewModel.closeDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(storedWidgets)
    }

    @Test
    fun `does not insert add new widget during customization mode when all available widgets are already placed`() = runTest {
        val storedWidgets = AVAILABLE_WIDGET_TYPES.toList().chunked(WIDGETS_PER_PAGE)
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(storedWidgets)

        viewModel.openDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(storedWidgets)

        viewModel.closeDashboardCustomizationMode()
        runCurrent()
        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage)
            .isEqualTo(storedWidgets)
    }

    @Test
    fun `enables deletion when there are more than 1 widget placed`() = runTest {
        val storedWidgets = listOf(
            WidgetType.PROGRESS,
            WidgetType.TRAINING,
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
            WidgetType.TOTAL_DURATION_THIS_MONTH,
            WidgetType.CALENDAR_THIS_MONTH
        ).chunked(WIDGETS_PER_PAGE)
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(storedWidgets)

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetRemovalEnabled).isTrue()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetRemovalEnabled).isFalse()
    }

    @Test
    fun `does not enable deletion when is only 1 widget placed`() = runTest {
        val storedWidgets = listOf(listOf(WidgetType.PROGRESS))
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(storedWidgets)

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetRemovalEnabled).isFalse()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetRemovalEnabled).isFalse()
    }

    @Test
    fun `removes page if emptied`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(
            listOf(
                listOf(WidgetType.PROGRESS),
                listOf(WidgetType.TRAINING),
                listOf(WidgetType.CALENDAR_THIS_MONTH)
            )
        )

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        viewModel.viewState.value?.data?.removeWidgetClicked?.invoke(1, 0)

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEqualTo(
            listOf(
                listOf(WidgetType.PROGRESS),
                listOf(WidgetType.CALENDAR_THIS_MONTH)
            )
        )
    }

    @Test
    fun `does not remove page if not emptied by widget removal`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(
            listOf(
                listOf(WidgetType.PROGRESS),
                listOf(WidgetType.TRAINING, WidgetType.MAP_THIS_MONTH),
                listOf(WidgetType.CALENDAR_THIS_MONTH)
            )
        )

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        viewModel.viewState.value?.data?.removeWidgetClicked?.invoke(1, 0)

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEqualTo(
            listOf(
                listOf(WidgetType.PROGRESS),
                listOf(WidgetType.MAP_THIS_MONTH),
                listOf(WidgetType.CALENDAR_THIS_MONTH)
            )
        )
    }

    @Test
    fun `correctly adds widgets`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(
            listOf(
                listOf(WidgetType.PROGRESS),
                listOf(WidgetType.TRAINING),
                listOf(
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.TOTAL_DURATION_THIS_MONTH
                )
            )
        )

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        viewModel.setWidgetType(0, 1, WidgetType.CALENDAR_THIS_MONTH)
        runCurrent()
        viewModel.setWidgetType(0, 2, WidgetType.MAP_THIS_MONTH)
        runCurrent()
        viewModel.setWidgetType(0, 3, WidgetType.CALORIES)
        runCurrent()

        viewModel.setWidgetType(1, 1, WidgetType.STEPS)
        runCurrent()

        viewModel.setWidgetType(2, 2, WidgetType.SLEEP)
        runCurrent()
        viewModel.setWidgetType(2, 3, WidgetType.RESOURCES)
        runCurrent()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEqualTo(
            listOf(
                listOf(
                    WidgetType.PROGRESS,
                    WidgetType.CALENDAR_THIS_MONTH,
                    WidgetType.MAP_THIS_MONTH,
                    WidgetType.CALORIES
                ),
                listOf(
                    WidgetType.TRAINING,
                    WidgetType.STEPS,
                ),
                listOf(
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.TOTAL_DURATION_THIS_MONTH,
                    WidgetType.SLEEP,
                    WidgetType.RESOURCES
                )
            )
        )
    }

    @Test
    fun `correctly replaces widgets`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(
            listOf(
                listOf(
                    WidgetType.PROGRESS,
                    WidgetType.CALENDAR_THIS_MONTH,
                    WidgetType.MAP_THIS_MONTH,
                    WidgetType.CALORIES
                ),
                listOf(
                    WidgetType.TRAINING,
                    WidgetType.STEPS
                ),
                listOf(
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.TOTAL_DURATION_THIS_MONTH,
                    WidgetType.SLEEP
                )
            )
        )

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        viewModel.setWidgetType(0, 0, WidgetType.RESOURCES)
        runCurrent()
        viewModel.setWidgetType(0, 1, WidgetType.PROGRESS)
        runCurrent()
        viewModel.setWidgetType(0, 2, WidgetType.CALENDAR_THIS_MONTH)
        runCurrent()
        viewModel.setWidgetType(0, 3, WidgetType.MAP_THIS_MONTH)
        runCurrent()

        viewModel.setWidgetType(1, 0, WidgetType.CALORIES)
        runCurrent()
        viewModel.setWidgetType(1, 1, WidgetType.TRAINING)
        runCurrent()

        viewModel.setWidgetType(2, 0, WidgetType.STEPS)
        runCurrent()
        viewModel.setWidgetType(2, 1, WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH)
        runCurrent()
        viewModel.setWidgetType(2, 2, WidgetType.TOTAL_DURATION_THIS_MONTH)
        runCurrent()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEqualTo(
            listOf(
                listOf(
                    WidgetType.RESOURCES,
                    WidgetType.PROGRESS,
                    WidgetType.CALENDAR_THIS_MONTH,
                    WidgetType.MAP_THIS_MONTH
                ),
                listOf(
                    WidgetType.CALORIES,
                    WidgetType.TRAINING
                ),
                listOf(
                    WidgetType.STEPS,
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.TOTAL_DURATION_THIS_MONTH
                )
            )
        )
    }

    @Test
    fun `correctly removes widgets`() = runTest {
        selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(
            listOf(
                listOf(
                    WidgetType.PROGRESS,
                    WidgetType.CALENDAR_THIS_MONTH,
                    WidgetType.MAP_THIS_MONTH,
                    WidgetType.CALORIES
                ),
                listOf(
                    WidgetType.TRAINING,
                    WidgetType.STEPS
                ),
                listOf(
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.TOTAL_DURATION_THIS_MONTH,
                    WidgetType.SLEEP
                )
            )
        )

        viewModel.openDashboardCustomizationMode()
        runCurrent()

        viewModel.removeWidget(0, 2) // map
        runCurrent()
        viewModel.removeWidget(0, 0) // progress
        runCurrent()
        viewModel.removeWidget(0, 1) // calories
        runCurrent()

        viewModel.removeWidget(1, 0) // training
        runCurrent()

        viewModel.removeWidget(2, 1) // total duration
        runCurrent()

        viewModel.closeDashboardCustomizationMode()
        runCurrent()

        assertThat(viewModel.viewState.value?.data?.widgetData?.widgetsPerPage).isEqualTo(
            listOf(
                listOf(
                    WidgetType.CALENDAR_THIS_MONTH
                ),
                listOf(
                    WidgetType.STEPS
                ),
                listOf(
                    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
                    WidgetType.SLEEP
                )
            )
        )
    }

    companion object {
        private val AVAILABLE_WIDGET_TYPES = setOf(
            WidgetType.PROGRESS,
            WidgetType.TRAINING,
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH,
            WidgetType.TOTAL_DURATION_THIS_MONTH,
            WidgetType.CALENDAR_THIS_MONTH,
            WidgetType.MAP_THIS_MONTH,
            WidgetType.CALORIES,
            WidgetType.STEPS,
            WidgetType.SLEEP,
            WidgetType.RESOURCES
        )
    }
}
