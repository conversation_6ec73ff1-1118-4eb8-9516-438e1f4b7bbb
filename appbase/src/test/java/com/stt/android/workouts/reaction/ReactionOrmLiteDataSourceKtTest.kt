package com.stt.android.workouts.reaction

import com.stt.android.domain.workouts.reactions.Reaction
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import com.stt.android.domain.user.Reaction as OldReaction

class ReactionOrmLiteDataSourceKtTest {

    @Test
    fun `map old domain entity to new one correctly`() {
        val expected = OldReaction.Builder()
            .reaction("reaction")
            .deleted(true)
            .key("key")
            .workoutKey("workoutkey")
            .locallyChanged(true)
            .timestamp(123L)
            .userName("user")
            .userRealName("real user")
            .userProfilePictureUrl("url")
            .build()

        val actual = expected.toDomainEntity()

        assertThat(actual.workoutKey).isEqualTo(expected.workoutKey)
        assertThat(actual.userRealOrUsername).isEqualTo(expected.userRealOrUserName)
        assertThat(actual.userName).isEqualTo(expected.userName)
        assertThat(actual.timestamp).isEqualTo(expected.timestamp)
        assertThat(actual.reaction).isEqualTo(expected.reaction)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.userProfilePictureUrl).isEqualTo(expected.userProfilePictureUrl)
    }

    @Test
    fun `map new domain entity to old successfully`() {
        val expected = Reaction(
            key = "key",
            workoutKey = "workoutkey",
            reaction = "reaction",
            userName = "username",
            userRealOrUsername = "real",
            userProfilePictureUrl = "url",
            timestamp = 123L
        )

        val actual = expected.toOldDomainEntity()

        assertThat(actual.workoutKey).isEqualTo(expected.workoutKey)
        assertThat(actual.userRealOrUserName).isEqualTo(expected.userRealOrUsername)
        assertThat(actual.userName).isEqualTo(expected.userName)
        assertThat(actual.timestamp).isEqualTo(expected.timestamp)
        assertThat(actual.reaction).isEqualTo(expected.reaction)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.userProfilePictureUrl).isEqualTo(expected.userProfilePictureUrl)
    }
}
