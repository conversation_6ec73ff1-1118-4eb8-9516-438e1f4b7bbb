<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="tts_language">zh-TW</string>
    <string name="tts_start">已開始</string>
    <string name="tts_stop">已停止</string>
    <string name="tts_autopause">已自動暫停</string>
    <string name="tts_autoresume">已自動繼續</string>
    <string name="tts_resume">已繼續</string>
    <string name="tts_lap_pace_metric">單圈配速每公里 %s。</string>
    <string name="tts_lap_pace_imperial">單圈配速每英里 %s。</string>
    <string name="tts_lap_swim_pace_metric">單圈配速每 100 公尺 %s。</string>
    <string name="tts_lap_swim_pace_imperial">單圈配速每 100 碼 %s。</string>
    <string name="tts_lap_time">單圈時間 %1$s</string>
    <string name="tts_total_time">%1$s。</string>

    <!-- We need two tts_kilometers -->
    <!-- we use string when it's not a full unit (1.23 kilometers, 2.1 kilometers) -->
    <string name="tts_kilometers">%1$s 公里</string>
    <!-- we can use the plurals when it's a full unit (1 kilometer, 2 kilometers) -->
    <plurals name="tts_kilometers">
        <item quantity="one">%1$s 公里</item>
        <item quantity="few">%1$s 公里</item>
        <item quantity="many">%1$s 公里</item>
        <item quantity="other">%1$s 公里</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_miles">%1$s 英里</string>

    <plurals name="tts_miles">
        <item quantity="one">%1$s 英里</item>
        <item quantity="few">%1$s 英里</item>
        <item quantity="many">%1$s 英里</item>
        <item quantity="other">%1$s 英里</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_nautical_miles">%1$s 海里</string>

    <plurals name="tts_nautical_miles">
        <item quantity="one">%1$s 海里</item>
        <item quantity="few">%1$s 海里</item>
        <item quantity="many">%1$s 海里</item>
        <item quantity="other">%1$s 海里</item>
    </plurals>

    <plurals name="tts_seconds">
        <item quantity="one">%1$d 秒</item>
        <item quantity="few">%1$d 秒</item>
        <item quantity="many">%1$d 秒</item>
        <item quantity="other">%1$d 秒</item>
    </plurals>

    <plurals name="tts_minutes">
        <item quantity="one">%1$d 分鐘</item>
        <item quantity="few">%1$d 分鐘</item>
        <item quantity="many">%1$d 分鐘</item>
        <item quantity="other">%1$d 分鐘</item>
    </plurals>

    <plurals name="tts_hours">
        <item quantity="one">%1$d 小時</item>
        <item quantity="few">%1$d 小時</item>
        <item quantity="many">%1$d 小時</item>
        <item quantity="other">%1$d 小時</item>
    </plurals>

    <string name="tts_ghost_ahead">您領先 %1$s</string>
    <string name="tts_ghost_behind">您落後 %1$s</string>
    <string name="tts_ghost_neutral">不相上下</string>

    <string name="tts_decimal_separator">點</string>

    <!-- Added in the Custom VoiceFeedback release -->
    <string name="tts_lap_speed">單圈速度：%s。</string>
    <string name="tts_current_speed">速度：%s。</string>
    <string name="tts_current_pace_metric">配速：每公里 %s。</string>
    <string name="tts_current_pace_imperial">配速：每英里 %s。</string>
    <string name="tts_average_speed">平均速度：%s。</string>
    <string name="tts_average_pace_metric">平均配速：每公里 %s。</string>
    <string name="tts_average_pace_imperial">平均配速：每英里 %s。</string>
    <string name="tts_total_distance">%1$s。</string>

    <string name="tts_current_heart_rate">心率：%d。</string>
    <string name="tts_average_heart_rate">平均心率：%d。</string>

    <string name="tts_energy">%1$d 卡路里。</string>
    <plurals name="tts_energy">
        <item quantity="one">%1$d 卡路里。</item>
        <item quantity="few">%1$d 卡路里。</item>
        <item quantity="many">%1$d 卡路里。</item>
        <item quantity="other">%1$d 卡路里。</item>
    </plurals>

    <string name="tts_current_cadence">踏頻：%d。</string>
    <string name="tts_average_cadence">平均步頻：%d。</string>

    <string name="tts_lap_number">圈數：%d。</string>
    <string name="tts_lap_distance">單圈距離：%s。</string>
    <string name="tts_max_heart_rate">最大心率：%d。</string>
    <string name="tts_total_time_with_title">總計時間：%1$s。</string>
    <string name="tts_heart_zone">心率區間：%d。</string>
    <string name="tts_lap_power">單圈功率：%s。</string>
    <string name="tts_lap_ascent">單圈上升：%s。</string>
    <string name="tts_lap_descent">單圈下降：%s。</string>
</resources>
