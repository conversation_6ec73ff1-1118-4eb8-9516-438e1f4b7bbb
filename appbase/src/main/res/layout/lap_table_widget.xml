<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/WhiteTheme"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <androidx.percentlayout.widget.PercentFrameLayout
        android:id="@+id/lapColumnTitles"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="@dimen/padding">

        <TextView
            android:id="@+id/lapDistanceOrSkiRunTitle"
            android:layout_height="wrap_content"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_widthPercent="10%"
            tools:text="Km"
            style="@style/LapTable.Labels"/>

        <TextView
            android:id="@+id/lapAvgSpeedTitle"
            android:layout_height="wrap_content"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_marginStartPercent="10%"
            app:layout_widthPercent="38%"
            tools:text="Speed km"
            style="@style/LapTable.Labels"/>

        <TextView
            android:id="@+id/lapDurationTitle"
            android:layout_height="wrap_content"
            android:text="@string/time_lap_label"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_marginStartPercent="48%"
            app:layout_widthPercent="18%"
            style="@style/LapTable.Labels"/>

        <TextView
            android:id="@+id/lapHrTitle"
            android:layout_height="wrap_content"
            android:text="@string/lap_hr"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_marginStartPercent="66%"
            app:layout_widthPercent="12%"
            tools:visibility="visible"
            style="@style/LapTable.Labels"/>

        <TextView
            android:id="@+id/lapAscentOrSkiDistanceTitle"
            android:layout_height="wrap_content"
            android:text="@string/ascend_lap_label"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_marginStartPercent="78%"
            app:layout_widthPercent="12%"
            style="@style/LapTable.Labels"/>

        <TextView
            android:id="@+id/lapDescentTitle"
            android:layout_height="wrap_content"
            android:text="@string/descend_lap_label"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizePresetSizes="@array/autosize_text_sizes_body"
            app:layout_marginStartPercent="90%"
            app:layout_widthPercent="10%"
            style="@style/LapTable.Labels"/>

    </androidx.percentlayout.widget.PercentFrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/laps"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding"
        android:scrollbars="vertical"/>

    <TextView
        android:id="@+id/no_laps_text"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="@string/ski_no_runs"
        android:visibility="gone"
        style="@style/Body.Large"/>
</LinearLayout>
