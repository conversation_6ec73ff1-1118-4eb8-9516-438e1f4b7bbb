<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    android:layout_marginTop="4dp"
    android:orientation="horizontal">
    <View
        android:id="@+id/heart_rate_zone_stripe"
        android:layout_width="5dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="13dp"/>
    <LinearLayout
        android:id="@+id/heart_rate_zone_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/heart_rate_zone_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                style="@style/hrZoneText.Bold" />

            <TextView
                android:id="@+id/heart_rate_zone_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/hrZoneText.Bold" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/heart_rate_zone_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:textColor="?android:textColorSecondary"
                style="@style/hrZoneText" />
            <TextView
                android:id="@+id/heart_rate_zone_bpm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/hrZoneText.Bold" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
