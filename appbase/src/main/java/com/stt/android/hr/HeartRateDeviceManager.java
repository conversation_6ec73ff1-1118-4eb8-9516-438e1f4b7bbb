package com.stt.android.hr;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import com.stt.android.bluetooth.BluetoothDeviceManager;
import com.stt.android.utils.STTConstants;
import java.util.Set;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

@Singleton
public class HeartRateDeviceManager {
    private final Context appContext;

    @Inject
    public HeartRateDeviceManager(Context context) {
        this.appContext = context.getApplicationContext();
    }

    public void savePairedDevice(String deviceAddress, HeartRateMonitorType hrType, String name) {
        savePairedDevice(appContext, deviceAddress, hrType, name);
    }

    public boolean hasPairedDevice() {
        return hasPairedDevice(appContext);
    }

    @Nullable
    public HeartRateMonitorType getPairedDeviceType() {
        return getPairedDeviceType(appContext);
    }

    @Nullable
    public String getPairedDeviceAddress() {
        return getPairedDeviceAddress(appContext);
    }

    @Nullable
    public String getPairedDeviceName() {
        return getPairedDeviceName(appContext);
    }

    @Nullable
    public Pair<HeartRateMonitorType, BluetoothDevice> getPairedDevice() {
        return getPairedDevice(appContext);
    }

    // Static methods that take in context as a parameter
    public static void savePairedDevice(Context context, String deviceAddress,
        HeartRateMonitorType hrType, String name) {
        Timber.d("Storing paired device %s", deviceAddress);
        getSharedPreferences(context).edit()
            .putString(STTConstants.BluetoothPreferences.KEY_LAST_HR_ADDR, deviceAddress)
            .putString(STTConstants.BluetoothPreferences.KEY_LAST_HR_NAME, name)
            .putString(STTConstants.BluetoothPreferences.KEY_LAST_HR_TYPE, hrType.toString())
            .apply();
    }

    @NonNull
    private static SharedPreferences getSharedPreferences(@NonNull Context context) {
        return context.getSharedPreferences(STTConstants.BluetoothPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    public static boolean hasPairedDevice(Context context) {
        return !TextUtils.isEmpty(getPairedDeviceAddress(context));
    }

    @Nullable
    public static HeartRateMonitorType getPairedDeviceType(Context context) {
        String type = getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_HR_TYPE, null);
        return TextUtils.isEmpty(type) ? null : HeartRateMonitorType.valueOf(type);
    }

    @Nullable
    public static String getPairedDeviceAddress(Context context) {
        return getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_HR_ADDR, null);
    }

    @Nullable
    public static String getPairedDeviceName(Context context) {
        return getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_HR_NAME, null);
    }

    @Nullable
    public static Pair<HeartRateMonitorType, BluetoothDevice> getPairedDevice(Context context) {
        String deviceAddress = getPairedDeviceAddress(context);
        if (TextUtils.isEmpty(deviceAddress)) {
            return null;
        }

        HeartRateMonitorType deviceType = getPairedDeviceType(context);
        if (deviceType == null) {
            return null;
        }

        BluetoothAdapter bluetoothAdapter = BluetoothDeviceManager.getBluetoothAdapter(context);
        if (bluetoothAdapter == null) {
            return null;
        }
        if (!deviceType.bonds()) {
            // if the device is of type that does not bond, let's return it immediately
            // without checking in the bonded devices list
            return new Pair<>(deviceType, bluetoothAdapter.getRemoteDevice(deviceAddress));
        }

        @SuppressLint("MissingPermission") Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
        if (bondedDevices != null && !bondedDevices.isEmpty()) {
            for (BluetoothDevice bondedDevice : bondedDevices) {
                if (deviceAddress.equalsIgnoreCase(bondedDevice.getAddress())) {
                    return new Pair<>(deviceType, bondedDevice);
                }
            }
        }

        // the device is no longer bonded, remove it
        getSharedPreferences(context).edit()
            .remove(STTConstants.BluetoothPreferences.KEY_LAST_HR_ADDR)
            .remove(STTConstants.BluetoothPreferences.KEY_LAST_HR_NAME)
            .remove(STTConstants.BluetoothPreferences.KEY_LAST_HR_TYPE)
            .apply();

        return null;
    }
}
