package com.stt.android.glance

import android.graphics.Paint.Style
import android.graphics.RectF
import androidx.annotation.FloatRange
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.util.TypedValueCompat
import androidx.glance.GlanceModifier
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.layout.ContentScale

private fun drawCircularProgressBar(
    progress: Float,
    activeTrackColor: Int,
    inactiveTrackColor: Int,
    size: Int,
    width: Int,
) = createBitmap(size, size).applyCanvas {
    val activeTrackPaint = android.graphics.Paint().apply {
        this.color = activeTrackColor
        this.style = Style.STROKE
        this.strokeWidth = width.toFloat()
        this.strokeCap = android.graphics.Paint.Cap.ROUND
    }
    val inactiveTrackPaint = android.graphics.Paint().apply {
        this.color = inactiveTrackColor
        this.style = Style.STROKE
        this.strokeWidth = width.toFloat()
    }
    val halfWidth = width / 2f
    val oval = RectF(
        halfWidth,
        halfWidth,
        size - halfWidth,
        size - halfWidth,
    )
    drawArc(oval, -90f, 360f, false, inactiveTrackPaint)
    drawArc(oval, -90f, progress * 360, false, activeTrackPaint)
}

@Composable
fun CircularProgressBar(
    @FloatRange(from = 0.0, to = 1.0) progress: Float,
    activeTrackColor: Color,
    inactiveTrackColor: Color,
    modifier: GlanceModifier = GlanceModifier,
    size: Dp = 62.scaledDp,
    width: Dp = 7f.scaledDp,
) {
    val context = LocalContext.current
    Image(
        modifier = modifier,
        provider = ImageProvider(
            drawCircularProgressBar(
                progress = progress,
                activeTrackColor = activeTrackColor.toArgb(),
                inactiveTrackColor = inactiveTrackColor.toArgb(),
                size = TypedValueCompat.dpToPx(
                    size.value,
                    context.resources.displayMetrics,
                ).toInt(),
                width = TypedValueCompat.dpToPx(
                    width.value,
                    context.resources.displayMetrics,
                ).toInt(),
            )
        ),
        contentDescription = null,
        contentScale = ContentScale.Fit,
    )
}
