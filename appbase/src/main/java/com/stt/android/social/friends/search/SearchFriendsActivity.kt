package com.stt.android.social.friends.search

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SearchFriendsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            SearchFriendsScreen(
                onBackClick = ::finish,
            )
        }
    }

    @Composable
    private fun SearchFriendsScreen(
        onBackClick: () -> Unit,
        modifier: Modifier = Modifier,
        viewModel: SearchFriendsViewModel = hiltViewModel(),
    ) {
        val state by viewModel.searchFriendsState.collectAsState()
        SearchFriendsContent(
            state = state,
            onBackClick = onBackClick,
            onQueryChange = viewModel::updateKeyword,
            onFriendClick = ::onFriendClick,
            onStatusClick = viewModel::onStatusClick,
            modifier = modifier,
        )
    }

    private fun onFriendClick(friend: Friend) {
        startActivity(
            BaseUserProfileActivity.newStartIntent(
                this,
                friend.username,
                false,
            )
        )
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context, trackPageName: String): Intent {
            return Intent(context, SearchFriendsActivity::class.java)
                .putExtra(AnalyticsEventProperty.PAGE_NAME, trackPageName)
        }
    }
}
