package com.stt.android.social.friends

import android.app.Activity
import android.content.Context
import android.graphics.BitmapFactory
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.IsSTFlavor
import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.KEY_SHOW_FOLLOWING_TAB
import com.stt.android.home.KEY_SHOW_PENDING_REQUESTS
import com.stt.android.home.people.InviteFriendsHelper
import com.stt.android.home.people.PeopleController
import com.stt.android.sharing.AnalysisData
import com.stt.android.sharing.AppSharingAnalysisImpl
import com.stt.android.sharing.ShareLinkInfo
import com.stt.android.sharing.SharingHelper
import com.stt.android.sharing.SharingInfo
import com.stt.android.sharing.SharingType
import com.stt.android.social.friends.discover.DiscoverFriendsState
import com.stt.android.social.friends.followers.FollowersState
import com.stt.android.social.friends.following.FollowingState
import com.stt.android.social.friends.usecase.FollowFriendUseCase
import com.stt.android.social.friends.usecase.RevokeFollowersUseCase
import com.stt.android.social.friends.utils.toFriend
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@OptIn(FlowPreview::class)
@HiltViewModel
class FriendsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @ApplicationContext private val context: Context,
    @IsSTFlavor private val isSTFlavor: Boolean,
    inviteFriendsHelper: InviteFriendsHelper,
    private val currentUserController: CurrentUserController,
    private val peopleController: PeopleController,
    private val followFriendUseCase: FollowFriendUseCase,
    private val revokeFollowersUseCase: RevokeFollowersUseCase,
    private val sharingHelper: SharingHelper,
    private val appSharingAnalysis: AppSharingAnalysisImpl,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {

    private val _currentPageFlow = MutableStateFlow(FriendsTab.DISCOVER.ordinal)
    val currentPageFlow = _currentPageFlow.asStateFlow()

    private val _facebookLoading = MutableStateFlow(false)
    val facebookLoading: StateFlow<Boolean> = _facebookLoading.asStateFlow()

    private val _friendsEventFlow = MutableSharedFlow<FriendsEvent>(
        replay = 0,
        extraBufferCapacity = 1,
    )
    val friendsEventFlow = _friendsEventFlow.asSharedFlow()

    private var _discoverFriendsStateFlow = MutableStateFlow(
        DiscoverFriendsState(
            showPhoneContacts = inviteFriendsHelper.showPhoneContact(),
            showFacebookFriends = !inviteFriendsHelper.showPhoneContact(),
            friends = emptyList(),
            loading = true,
        )
    )
    val discoverFriendsStateFlow = _discoverFriendsStateFlow.asStateFlow()

    private var _followingStateFlow = MutableStateFlow(FollowingState(emptyList(), true))
    val followingStateFlow = _followingStateFlow.asStateFlow()

    private var _followersStateFlow =
        MutableStateFlow(FollowersState(emptyList(), false, emptyList(), true))
    val followersStateFlow = _followersStateFlow.asStateFlow()

    val selectedFriendsCount: StateFlow<Int> = followersStateFlow.map { it.selectedFriends.size }
        .stateIn(viewModelScope, SharingStarted.Eagerly, 0)

    val editing: Boolean
        get() = _followersStateFlow.value.editing

    init {
        val showFollowingTab = savedStateHandle.get<Boolean>(KEY_SHOW_FOLLOWING_TAB) ?: false
        val showPendingRequests = savedStateHandle.get<Boolean>(KEY_SHOW_PENDING_REQUESTS) ?: false
        _currentPageFlow.update {
            when {
                showFollowingTab -> FriendsTab.FOLLOWING.ordinal
                showPendingRequests -> FriendsTab.FOLLOWERS.ordinal
                else -> FriendsTab.DISCOVER.ordinal
            }
        }
        viewModelScope.launch {
            _currentPageFlow
                .debounce(300)
                .collectLatest { page ->
                    loadCurrentPageData()
                    if (page != FriendsTab.FOLLOWERS.ordinal) {
                        setEditingMode(false)
                    }
                }
        }
    }

    fun loadCurrentPageData(delay: Long = 0L) {
        viewModelScope.launch {
            delay(delay)
            val currentPage = _currentPageFlow.value
            when (currentPage) {
                FriendsTab.DISCOVER.ordinal -> loadDiscoveryData()
                FriendsTab.FOLLOWING.ordinal -> loadFollowingData()
                FriendsTab.FOLLOWERS.ordinal -> loadFollowersData()
            }
        }
    }

    fun updateCurrentPage(page: Int) {
        _currentPageFlow.update { page }
    }

    private fun loadDiscoveryData() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            if (_discoverFriendsStateFlow.value.friends.any()) {
                updateRecentlyFriendStatusForDiscover()
                return@launch
            }
            runSuspendCatching {
                peopleController.fetchPeopleSuggestions().await().map { ufs ->
                    ufs.toFriend()
                }.let { friends ->
                    _discoverFriendsStateFlow.update {
                        it.copy(friends = friends, loading = false)
                    }
                }
            }.onFailure { e ->
                _discoverFriendsStateFlow.update {
                    it.copy(loading = false)
                }
                Timber.w(e, "Failed to load discover friends")
            }
        }
    }

    private fun loadFollowingData() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            if (_followingStateFlow.value.friends.any()) {
                updateRecentlyFriendStatusForFollowing()
                return@launch
            }
            runSuspendCatching {
                peopleController.loadUserFollowStatuses(FollowDirection.FOLLOWING).await()
                    .ignoreExistingRequested()
                    .map { ufs ->
                        ufs.toFriend()
                    }.let { friends ->
                        _followingStateFlow.update {
                            it.copy(
                                friends = friends,
                                loading = false,
                            )
                        }
                    }
            }.onFailure { e ->
                _followingStateFlow.update {
                    it.copy(
                        loading = false,
                    )
                }
                Timber.w(e, "Failed to load following")
            }
        }
    }

    private fun loadFollowersData() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            if (_followersStateFlow.value.friends.any()) {
                updateRecentlyFriendStatusForFollowers()
                return@launch
            }
            runSuspendCatching {
                peopleController.loadUserFollowStatuses(FollowDirection.FOLLOWER).await()
                    .mapNotNull { ufs ->
                        // Cannot show unfollowing users in followers list
                        if (ufs.status == FollowStatus.UNFOLLOWING) {
                            null
                        } else {
                            ufs.toFriend()
                        }
                    }.let { friends ->
                        _followersStateFlow.update {
                            it.copy(
                                friends = friends,
                                loading = false,
                            )
                        }
                    }
            }.onFailure { e ->
                _followersStateFlow.update {
                    it.copy(
                        loading = false,
                    )
                }
                Timber.w(e, "Failed to load followers")
            }
        }
    }

    private fun updateRecentlyFriendStatusForDiscover() {
        runSuspendCatching {
            _discoverFriendsStateFlow.update {
                it.copy(friends = it.friends.map { friend ->
                    peopleController.loadUfsFromDbForFollowUser(friend).toFriend()
                })
            }
        }
    }

    private suspend fun updateRecentlyFriendStatusForFollowing() {
        runSuspendCatching {
            val following =
                peopleController.loadUserFollowStatuses(FollowDirection.FOLLOWING).await()
            _followingStateFlow.update { state ->
                // Need to add new following friends
                val usernames = _followingStateFlow.value.friends.map { it.username }.toSet()
                val newFollowing = following
                    .filter { ufs ->
                        ufs.username !in usernames
                    }
                    .ignoreExistingRequested()
                    .map { ufs ->
                        ufs.toFriend()
                    }
                state.copy(friends = newFollowing + state.friends.map { friend ->
                    peopleController.loadUfsFromDbForFollowUser(friend).toFriend()
                })
            }
        }
    }

    private fun updateRecentlyFriendStatusForFollowers() {
        runSuspendCatching {
            _followersStateFlow.update {
                it.copy(friends = it.friends.map { friend ->
                    peopleController.loadUfsFromDbForFollowUser(friend).toFriend()
                })
            }
        }
    }

    fun onFriendClick(friend: Friend): Boolean {
        if (_followersStateFlow.value.editing) {
            _followersStateFlow.update {
                it.copy(
                    selectedFriends = if (it.selectedFriends.contains(friend)) {
                        it.selectedFriends - friend
                    } else {
                        it.selectedFriends + friend
                    }
                )
            }
            return true
        }
        return false
    }

    fun onStatusClick(friend: Friend) {
        viewModelScope.launch {
            val updated = runSuspendCatching {
                followFriendUseCase(friend)
            }.onFailure {
                Timber.w(it, "follow/unfollow ${friend.username} failed.")
            }.getOrNull() ?: return@launch
            _discoverFriendsStateFlow.update { state ->
                state.copy(
                    friends = state.friends.replaceByUpdated(updated)
                )
            }
            _followingStateFlow.update { state ->
                state.copy(
                    friends = state.friends.replaceByUpdated(updated)
                )
            }
            _followersStateFlow.update { state ->
                state.copy(
                    friends = state.friends.replaceByUpdated(updated)
                )
            }
        }
    }

    fun setEditingMode(editing: Boolean) {
        _followersStateFlow.update {
            it.copy(
                editing = editing,
                selectedFriends = emptyList(),
            )
        }
    }

    fun revokeSelectedFollowers() {
        viewModelScope.launch {
            runSuspendCatching {
                revokeFollowersUseCase(_followersStateFlow.value.selectedFriends)
            }.onSuccess {
                _followersStateFlow.update {
                    val updatedFriends = it.friends - it.selectedFriends.toSet()
                    it.copy(
                        friends = updatedFriends,
                        selectedFriends = emptyList(),
                        editing = updatedFriends.any(),
                    )
                }
            }.onFailure {
                Timber.w(it, "Failed to delete followers")
            }
        }
    }

    fun isUserLoggedIn(): Boolean {
        return currentUserController.isLoggedIn
    }

    fun isFacebookReady(): Boolean {
        return currentUserController.session?.isConnectedToFacebook == true
    }

    fun checkFacebookTokenValid() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                peopleController.validateFacebookToken().await()
            }.onSuccess {
                if (it) {
                    setFacebookLoading(false)
                    sendEvent(FacebookLoginSuccess)
                } else {
                    sendEvent(RequestLoginFacebook)
                }
            }.onFailure {
                setFacebookLoading(false)
                sendEvent(RequestLoginFacebook)
            }
        }
    }

    fun fbLoginSuccess(token: String) {
        linkWithFacebook(token)
    }

    fun fbLoginError(exception: Throwable) {
        setFacebookLoading(false)
        sendEvent(FacebookLoginError(exception))
    }

    fun setFacebookLoading(loading: Boolean) {
        _facebookLoading.update { loading }
    }

    fun shareAppLink(activity: Activity, fragmentManager: FragmentManager) {
        val sourceName = AnalyticsPropertyValue.FollowSourceProperty.FIND_FB_FRIENDS
        val supportsShareInApp = context.resources.getBoolean(R.bool.supports_invite_friends_in_app)
        val shareLink =
            context.getString(R.string.invite_friends_share_link, currentUserController.username)
        val shareInfo = SharingInfo(
            sharingType = SharingType.LINK,
            shareLinkInfo = ShareLinkInfo(
                linkUrl = shareLink,
                title = context.getString(
                    R.string.invite_friends_share_title,
                    context.getString(R.string.brand_name)
                ),
                description = context.getString(R.string.invite_friends_share_description),
                iconBitmap = BitmapFactory.decodeResource(
                    context.resources,
                    if (isSTFlavor) R.drawable.sportstracker_app_icon else R.drawable.suunto_app_icon
                ),
            ),
            shareSourceName = sourceName
        )
        if (supportsShareInApp) {
            sharingHelper.shareByApp(shareInfo, fragmentManager)
        } else {
            sharingHelper.shareBySystem(
                shareInfo, activity, AnalysisData(
                    eventName = AnalyticsEvent.H5SHARE,
                    analyticsProperties = appSharingAnalysis.geSharingLinkAnalysis(sourceName),
                )
            )
        }
    }

    private fun linkWithFacebook(accessToken: String) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                currentUserController.linkWithFacebook(accessToken)
            }.onFailure {
                setFacebookLoading(false)
                sendEvent(FacebookLoginError(it))
            }.onSuccess {
                setFacebookLoading(false)
                sendEvent(FacebookLoginSuccess)
            }
        }
    }

    private fun sendEvent(event: FriendsEvent) {
        _friendsEventFlow.tryEmit(event)
    }

    private fun List<Friend>.replaceByUpdated(updated: Friend) = map {
        if (it.username == updated.username) {
            updated
        } else {
            it
        }
    }

    private fun List<UserFollowStatus>.ignoreExistingRequested() =
        filter { // Not show existing "requested" people
            it.currentUserFollowStatus != FollowStatus.REJECTED &&
                it.currentUserFollowStatus != FollowStatus.PENDING
        }
}
