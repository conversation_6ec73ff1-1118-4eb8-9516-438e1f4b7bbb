package com.stt.android.social.userprofileV2

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatActivity.NOTIFICATION_SERVICE
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.userSettings
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.di.IsSTFlavor
import com.stt.android.domain.report.ReportUserUseCase
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.report.block.BlockUserUseCase
import com.stt.android.domain.report.block.GetUserBlockStatusUseCase
import com.stt.android.domain.report.block.UnblockUserUseCase
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.GetUserGearLatestUseCase
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.follow.FetchOtherUserFollowCountSummaryUseCase
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.user.subscription.GetCurrentPremiumSubscriptionStatusUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.FetchWorkoutStatsUseCase
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.usecase.FollowFriendUseCase
import com.stt.android.social.friends.usecase.RevokeFollowersUseCase
import com.stt.android.social.friends.utils.toFriend
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_FROM_NOTIFICATION
import com.stt.android.utils.STTConstants
import android.content.Intent
import com.stt.android.social.userprofileV2.usecase.UpdateAvatarUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import io.reactivex.disposables.CompositeDisposable
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import timber.log.Timber
import javax.inject.Inject
import kotlin.String
import javax.inject.Named
import rx.subjects.Subject
import rx.subscriptions.CompositeSubscription
import java.io.File

@HiltViewModel
class UserProfileViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    internal val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val getUserGearLatestUseCase: GetUserGearLatestUseCase,
    private val fetchWorkoutStatsUseCase: FetchWorkoutStatsUseCase,
    private val peopleController: PeopleController,
    private val fetchOtherUserFollowCountSummaryUseCase: FetchOtherUserFollowCountSummaryUseCase,
    private val getUserBlockStatusUseCase: GetUserBlockStatusUseCase,
    private val blockUserUseCase: BlockUserUseCase,
    private val unblockUserUseCase: UnblockUserUseCase,
    private val reportUserUseCase: ReportUserUseCase,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    getCurrentPremiumSubscriptionStatusUseCase: GetCurrentPremiumSubscriptionStatusUseCase,
    @Named("FOLLOWING") private val followingSubject: Subject<UserFollowStatus, UserFollowStatus>,
    @Named("FOLLOWERS") private val followersSubject: Subject<UserFollowStatus, UserFollowStatus>,
    savedStateHandle: SavedStateHandle,
    @FeatureTogglePreferences featureTogglePreferences: SharedPreferences,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val revokeFollowersUseCase: RevokeFollowersUseCase,
    private val followFriendUseCase: FollowFriendUseCase,
    @IsSTFlavor private val isSTFlavor: Boolean,
    private val updateAvatarUseCase: UpdateAvatarUseCase,
) : ViewModel() {
    private val username: String = savedStateHandle.get<String>(KEY_USER_NAME)
        ?.also { name ->
            if (savedStateHandle.get<Boolean>(KEY_FROM_NOTIFICATION) == true) {
                val nm = context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                nm.cancel(name.hashCode())
            }
        }
        ?: savedStateHandle.get<User>(KEY_USER)?.username
        ?: currentUserController.username
    val isCurrentUser = currentUserController.username == username
    private val _userFlow =
        MutableStateFlow(
            if (isCurrentUser) {
                currentUserController.currentUser
            } else {
                savedStateHandle[KEY_USER] ?: User.INVALID
            }
        )
    val user: User get() = _userFlow.value

    val measurementUnit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    val tempProfilePictureFile: File
        get() = updateAvatarUseCase.tempProfilePictureFile

    private val _userWorkoutSummaryState =
        MutableStateFlow(WorkoutSummaryStats(0.0, 0.0, 0, 0, emptyList()))
    val userWorkoutSummaryState = _userWorkoutSummaryState.asStateFlow()
    private val _followCountSummaryState =
        MutableStateFlow(FollowCountStats(followersCount = 0, followingCount = 0))
    val followCountSummaryState = _followCountSummaryState.asStateFlow()
    private val _userFollowStatusFlow = MutableStateFlow<UserFollowStatus?>(null)
    val userFollowStatusFlow = _userFollowStatusFlow.asStateFlow()
    private val _deviceTypeListState = MutableStateFlow<List<DeviceType>>(emptyList())
    val deviceTypeListState = _deviceTypeListState.asStateFlow()
    private val _moreMenuListState = MutableStateFlow<List<MoreMenu>>(emptyList())
    val moreMenuListState = _moreMenuListState.asStateFlow()
    private val _showRevokeFollowerAction = MutableStateFlow(false)

    // track reports for lifetime of VM to prevent API spam
    private var usernameReported = false

    private val compositeSubscription = CompositeSubscription()
    private val disposables = CompositeDisposable()
    private val _dialogTipsEvent = Channel<DialogTips>()
    val dialogTipsEvent = _dialogTipsEvent.receiveAsFlow()

    private val blockStatus: BlockStatus
        get() = _userBlockStatusFlow.value
    private val _userBlockStatusFlow = MutableStateFlow(
        BlockStatus(
            isUserBlocked = false,
            isBlockedByUser = false,
        )
    )
    private val userBlockStatusFlow = _userBlockStatusFlow.asStateFlow()

    private val _viewState = MutableStateFlow(
        UserProfileState(
            user = user,
            isCurrentUser = isCurrentUser,
            blockStatus = _userBlockStatusFlow.value,
            friend = null,
        )
    )
    val viewState: StateFlow<UserProfileState> = _viewState.asStateFlow()

    private val _eventChannel = Channel<UserProfileEvent>()
    val eventFlow = _eventChannel.receiveAsFlow()

    private val userDataFlow: Flow<Triple<User, BlockStatus, UserFollowStatus?>> = combine(
        _userFlow,
        userBlockStatusFlow,
        userFollowStatusFlow,
    ) { user, blockStatus, userFollowStatus ->
        Triple(user, blockStatus, userFollowStatus)
    }
        .flowOn(coroutinesDispatchers.io)
        .catch { throwable ->
            Timber.w(throwable, "Error loading user data")
            errorTips(throwable)
        }

    val newFriendsEnabled = featureTogglePreferences.getBoolean(
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS,
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS_DEFAULT
    )

    private var isInitLoaded = false

    init {
        if (!isCurrentUser) {
            viewModelScope.launch(coroutinesDispatchers.io) {
                getUserByUsernameUseCase.getUserByUsername(username)
                    .onEach { user ->
                        peopleController.loadUfsByUserLocally(user)
                    }
                    .catch { e ->
                        Timber.w(e, "Error loading user")
                        errorTips(e)
                    }
                    .collect { user ->
                        _userFlow.update { user }
                    }
            }
            viewModelScope.launch {
                combine(
                    _userBlockStatusFlow,
                    _showRevokeFollowerAction
                ) { blockStatus, showRevokeFollowerAction ->
                    blockStatus to showRevokeFollowerAction
                }.distinctUntilChanged().collect { (blockStatus, showRevokeFollowerAction) ->
                    reloadMoreMenuList(blockStatus, showRevokeFollowerAction)
                }
            }
        } else {
            viewModelScope.launch(coroutinesDispatchers.io) {
                userSettingsController.userSettings()
                    .catch { e ->
                        Timber.w(e, "Error loading user settings")
                    }
                    .collect {
                        reloadUser()
                    }
            }
        }

        compositeSubscription.add(followingSubject.subscribe { status ->
            Timber.d("followingSubject status changed: $status")
            if (status.username == username) {
                _userFollowStatusFlow.update { status }
            }
        })

        viewModelScope.launch {
            runSuspendCatching {
                val blockStatus = getUserBlockStatusUseCase(username)
                _userBlockStatusFlow.update { blockStatus }
            }.onFailure { e ->
                Timber.w(e, "Error loading block status")
                errorTips(e)
            }
        }

        viewModelScope.launch {
            userDataFlow.collect { (user, blockStatus, userFollowStatus) ->
                handleUserData(user, blockStatus, userFollowStatus)
            }
        }
    }

    sealed class DialogTips {
        data object OnUserReported : DialogTips()
        data object OnUserBlocked : DialogTips()
    }

    override fun onCleared() {
        super.onCleared()
        compositeSubscription.unsubscribe()
        disposables.clear()
    }

    private fun reloadMoreMenuList(blockStatus: BlockStatus, showRevokeFollowerAction: Boolean) {
        val list = arrayListOf<MoreMenu>()
        // Hide MAP for now
        if (!blockStatus.isBlockedByUser && !newFriendsEnabled) {
            list.add(MoreMenu.MAP)
        }
        if (showRevokeFollowerAction) {
            list.add(MoreMenu.REMOVE_FOLLOWER)
        }
        if (!blockStatus.isUserBlocked) {
            list.add(MoreMenu.BLOCK)
        } else {
            list.add(MoreMenu.UNBLOCK_USER)
        }
        list.add(MoreMenu.REPORT)
        _moreMenuListState.update {
            list
        }
    }

    val premiumSubscriptionStatus: StateFlow<CurrentPremiumSubscriptionStatus?> =
        if (currentUserController.username == username && isSTFlavor) {
            getCurrentPremiumSubscriptionStatusUseCase().flowOn(coroutinesDispatchers.io)
        } else {
            flowOf(null)
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(stopTimeoutMillis = 5000L),
            initialValue = null,
        )

    @SuppressLint("CheckResult")
    internal fun revokeFollower(userFollowStatus: UserFollowStatus) {
        viewModelScope.launch {
            runSuspendCatching {
                revokeFollowersUseCase(listOf(userFollowStatus.toFriend()))
            }.onFailure {
                errorTips(it)
                Timber.w(it, "Failed to revoke follower")
            }.onSuccess {
                _showRevokeFollowerAction.update { false }
                loadFollowCountSummary()
                loadFollowRelationship()
                sendEvent(FollowerRemoved)
            }
        }
    }

    fun blockUser() {
        viewModelScope.launch {
            runSuspendCatching {
                blockUserUseCase(username)
            }.onSuccess {
                _userBlockStatusFlow.update { it.copy(isUserBlocked = true) }
                _showRevokeFollowerAction.update { false }
                _dialogTipsEvent.send(DialogTips.OnUserBlocked)
                sendUserBlockedEvent()
            }.onFailure {
                errorTips(it)
                Timber.w(it, "Error blocking user")
            }
        }
    }

    fun reportUser() {
        if (usernameReported) {
            Timber.d("$username has already been reported.")
            sendEvent(UserDuplicateReported)
            return
        }
        viewModelScope.launch {
            runSuspendCatching {
                val notDuplicate = reportUserUseCase(username)
                sendEvent(
                    if (notDuplicate) UserReported else UserDuplicateReported
                )
            }.onSuccess {
                usernameReported = true
                _dialogTipsEvent.send(DialogTips.OnUserReported)
                sendUserReportedEvent()
            }.onFailure {
                Timber.w(it, "Error reporting user")
            }
        }
    }

    fun unblockUser() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                unblockUserUseCase(username)
                peopleController.hasFollowing().await()
                _userFollowStatusFlow.update {
                    it?.toFriend()?.let { friend ->
                        peopleController.loadUfsFromDbForFollowUser(friend)
                    }
                }
                _userBlockStatusFlow.update { it.copy(isUserBlocked = false) }
                sendUserUnBlockedEvent()
            }.onFailure {
                errorTips(it)
            }
        }
    }

    private fun sendUserUnBlockedEvent() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.USER_UNBLOCKED,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.UNBLOCKED_USER_ID, username)
        )
    }

    private fun sendUserBlockedEvent() {
        fetchRelationshipStatusAndThen(
            onSuccess = {
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.USER_BLOCKED,
                    AnalyticsProperties()
                        .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, it)
                        .put(AnalyticsEventProperty.BLOCKED_USER_ID, username)
                )
            },
            onError = {
                Timber.w(it, "sendUserBlockedEvent")
            }
        )
    }

    private fun sendUserReportedEvent() {
        fetchRelationshipStatusAndThen(
            onSuccess = {
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.USER_REPORTED,
                    AnalyticsProperties()
                        .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, it)
                        .put(AnalyticsEventProperty.BLOCKED_USER_ID, username)
                )
            },
            onError = {
                Timber.w(it, "sendUserReportedEvent")
            }
        )
    }

    private fun fetchRelationshipStatusAndThen(
        onSuccess: (String) -> Unit,
        onError: (Throwable) -> Unit,
    ) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                user.let(peopleController::getFollowRelationshipValueForAnalytics).let(onSuccess)
            }.onFailure(onError)
        }
    }

    private fun loadInitialData() {
        if (!isInitLoaded) {
            loadAggregatedWorkouts()
            loadFollowCountSummary()
            loadDeviceTypeList()
            if (isCurrentUser) {
                loadLatestBestRecord()
            } else {
                loadFollowRelationship()
            }
            isInitLoaded = true
        }
    }

    fun reloadUser() {
        loadAggregatedWorkouts()
        loadFollowCountSummary()
        if (isCurrentUser) {
            val settings = userSettingsController.settings
            _userFlow.update {
                currentUserController.currentUser.copy(
                    realName = settings.realName,
                    description = settings.description,
                    country = settings.country?.takeIf { it.isNotBlank() }
                        ?: currentUserController.currentUser.country,
                )
            }
        }
    }

    private fun handleUserData(
        user: User,
        blockStatus: BlockStatus,
        userFollowStatus: UserFollowStatus?
    ) {
        viewModelScope.launch {
            loadInitialData()
            _viewState.update {
                it.copy(
                    user = user,
                    isCurrentUser = isCurrentUser,
                    blockStatus = blockStatus,
                    friend = if (newFriendsEnabled && !isCurrentUser) {
                        userFollowStatus?.toFriend()
                    } else {
                        null
                    }
                )
            }
        }
    }

    private fun loadFollowRelationship() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                peopleController.loadUfsByUserLocally(user)
                val relationship = peopleController.getFollowRelationship(user)
                if (relationship.first) {
                    // Profile is following currently logged in user
                    _showRevokeFollowerAction.update { true }
                }
                Timber.d("loadFollowRelationship is $relationship")
                _followCountSummaryState.update {
                    it.copy(
                        followersClickEnabled = relationship.first,
                        followingClickEnabled = relationship.second
                    )
                }
            }.onFailure { e ->
                errorTips(e)
                Timber.w(e, "Error during load of aggregated data")
            }
        }
    }

    private fun loadDeviceTypeList() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val deviceTypeList = getUserGearLatestUseCase.getGearLatestByUsername(username)
                _deviceTypeListState.update {
                    deviceTypeList.map {
                        it.toDeviceType()
                    }
                }
            }.onFailure { e ->
                Timber.w(e, "Error during load of device type list")
            }
        }
    }

    private fun loadLatestBestRecord() {

        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {

            }.onFailure { e ->
            }
        }
    }

    fun follow(userFollowStatus: UserFollowStatus, viewSourceForAnalytics: String) {
        compositeSubscription.add(
            peopleController.follow(userFollowStatus, viewSourceForAnalytics)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    Timber.d("$userFollowStatus followed")
                }, { e ->
                    Timber.w(e, "Failed to follow $userFollowStatus")
                })
        )
    }

    fun unfollow(userFollowStatus: UserFollowStatus) {
        compositeSubscription.add(
            peopleController.unfollow(userFollowStatus)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    Timber.d("Unfollow status changed:")
                }, {
                    Timber.w(it, "Unable to handle follow status update")
                })
        )
    }

    private fun loadAggregatedWorkouts() {
        if (blockStatus.isBlockedByUser) {
            return
        }
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val workoutStats = fetchWorkoutStatsUseCase(username)
                val totalTime =
                    workoutStats.activityTypeStats.maxByOrNull { it.totalTime }?.totalTime ?: 0L
                val workoutSummaryStats = WorkoutSummaryStats(
                    totalDistanceSum = workoutStats.totalDistanceSum,
                    totalTimeSum = workoutStats.totalTimeSum,
                    totalNumberOfWorkoutsSum = workoutStats.totalNumberOfWorkoutsSum,
                    totalDays = workoutStats.totalDays,
                    activityTypeStats = workoutStats.activityTypeStats.map {
                        TopActivity(
                            activityType = ActivityType.valueOf(it.activityId),
                            duration = it.totalTime,
                            distance = it.totalDistance,
                            progress = it.totalTime.toFloat() / totalTime.toFloat(),
                            measurementUnit = measurementUnit
                        )
                    }
                )
                Timber.d("fetchWorkoutStatsUseCase $workoutStats")
                _userWorkoutSummaryState.update { workoutSummaryStats }
            }.onFailure { e ->
                Timber.w(e, "Error during load of aggregated data")
                errorTips(e)
            }
        }
    }

    private fun loadFollowCountSummary() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val followCountSummary = if (isCurrentUser) {
                    peopleController.followCountSummary
                } else if (!blockStatus.isBlockedByUser) {
                    fetchOtherUserFollowCountSummaryUseCase(username)
                } else {
                    return@launch
                }
                _followCountSummaryState.update {
                    it.copy(
                        followersCount = followCountSummary.followersCount,
                        followingCount = followCountSummary.followingCount,
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Error during load of user follow count")
            }
        }
    }

    private fun sendEvent(event: UserProfileEvent) {
        if (newFriendsEnabled) {
            _eventChannel.trySend(event)
        }
    }

    private fun errorTips(throwable: Throwable) {
        if (throwable is Exception) {
            _eventChannel.trySend(UserProfileError(throwable))
        }
    }

    fun onMoreMenuClick(menu: MoreMenu) {
        when (menu) {
            MoreMenu.REMOVE_FOLLOWER -> sendEvent(
                ConfirmDialogEvent(
                    title = R.string.dialog_title_remove_follower,
                    message = R.string.dialog_message_remove_follower,
                    confirmText = R.string.remove,
                    onConfirm = {
                        userFollowStatusFlow.value?.let { ufs ->
                            revokeFollower(ufs)
                        }
                    })
            )

            MoreMenu.BLOCK -> sendEvent(
                ConfirmDialogEvent(
                    title = R.string.dialog_title_block_user,
                    message = R.string.dialog_message_block_user,
                    confirmText = R.string.block,
                    onConfirm = {
                        blockUser()
                    })
            )

            MoreMenu.UNBLOCK_USER -> unblockUser()
            MoreMenu.REPORT -> sendEvent(
                ConfirmDialogEvent(
                    title = R.string.dialog_title_report_user,
                    message = R.string.dialog_message_report_user,
                    confirmText = R.string.report,
                    onConfirm = {
                        reportUser()
                    })
            )

            else -> {}
        }
    }

    fun onFollowButtonClicked() {
        if (blockStatus.isUserBlocked) {
            unblockUser()
            return
        }
        val friend = _userFollowStatusFlow.value?.toFriend() ?: return
        viewModelScope.launch {
            runSuspendCatching {
                followFriendUseCase(friend)
                _userFollowStatusFlow.update {
                    peopleController.loadUfsFromDbForFollowUser(friend)
                }
                loadFollowCountSummary()
            }.onFailure {
                errorTips(it)
                Timber.w(it, "follow/unfollow ${friend.username} failed.")
            }
        }
    }

    fun updateProfilePicture(data: Intent) {
        if (!isCurrentUser) return
        viewModelScope.launch {
            updateAvatarUseCase(data, context).collect { state ->
                state.user?.let { updated ->
                    _userFlow.update { updated }
                }
                _viewState.update {
                    it.copy(
                        uploadingAvatar = state.isLoading,
                    )
                }
                state.error?.let {
                    errorTips(it)
                }
            }
        }
    }
}

