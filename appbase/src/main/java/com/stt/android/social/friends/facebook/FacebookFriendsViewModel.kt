package com.stt.android.social.friends.facebook

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.usecase.FollowFriendUseCase
import com.stt.android.social.friends.usecase.FollowListOfFriendUseCase
import com.stt.android.social.friends.utils.toFriend
import com.stt.android.utils.awaitFirstNonNull
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FacebookFriendsViewModel @Inject constructor(
    private val peopleController: PeopleController,
    private val followListOfFriendUseCase: FollowListOfFriendUseCase,
    private val followFriendUseCase: FollowFriendUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _friendsStateFlow = MutableStateFlow(
        FacebookFriendsState(
            friends = emptyList(),
            addingAll = false,
            loading = true,
        )
    )
    val friendsStateFlow = _friendsStateFlow.asStateFlow()

    private val _eventFlow = MutableSharedFlow<FacebookFriendsEvent>(
        replay = 0,
        extraBufferCapacity = 1,
    )
    val eventFlow = _eventFlow.asSharedFlow()

    private var addingAllJob: Job? = null

    init {
        loadFacebookFriends()
    }

    fun loadFacebookFriends() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val friendList =
                    peopleController.fetchMappedFacebookFriends().awaitFirstNonNull().map {
                        it.toFriend()
                    }
                _friendsStateFlow.update {
                    it.copy(
                        loading = false,
                        friends = friendList,
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Load facebook friends failed.")
                sendEvent(FailedToLoad(e))
                _friendsStateFlow.update {
                    it.copy(
                        loading = false,
                    )
                }
            }
        }
    }

    fun onStatusClick(friend: Friend) {
        viewModelScope.launch {
            val updated = runSuspendCatching {
                followFriendUseCase(friend)
            }.onFailure { e ->
                sendEvent(FailedToFollow(friend, e))
                Timber.w(e, "follow/unfollow ${friend.username} failed.")
            }.getOrNull() ?: return@launch
            _friendsStateFlow.update { state ->
                state.copy(
                    friends = state.friends.map {
                        if (it.username == updated.username) {
                            updated
                        } else {
                            it
                        }
                    }
                )
            }
        }
    }

    fun onAddAllClick() {
        addingAllJob = viewModelScope.launch {
            _friendsStateFlow.update {
                it.copy(
                    addingAll = true,
                )
            }
            runSuspendCatching {
                followListOfFriendUseCase(_friendsStateFlow.value.friends)
            }.onSuccess {
                _friendsStateFlow.update {
                    it.copy(
                        addingAll = false,
                    )
                }
                updateRecentlyFriendStatus()
            }.onFailure { e ->
                Timber.w(e, "Failed to follow all friends")
                sendEvent(FailedToAddAll(e))
                _friendsStateFlow.update {
                    it.copy(
                        addingAll = false,
                    )
                }
            }
        }
    }

    fun undoAddAll() {
        addingAllJob?.cancel()
        addingAllJob = null
        _friendsStateFlow.update {
            it.copy(
                addingAll = false,
            )
        }
    }

    private fun updateRecentlyFriendStatus() {
        runSuspendCatching {
            _friendsStateFlow.update {
                it.copy(friends = it.friends.map { friend ->
                    peopleController.loadUfsFromDbForFollowUser(friend).toFriend()
                })
            }
        }.onFailure { e ->
            Timber.w(e, "Update recently friend status failed")
        }
    }

    private fun sendEvent(event: FacebookFriendsEvent) {
        _eventFlow.tryEmit(event)
    }
}
