package com.stt.android.follow;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

/**
 * Follow status with user information. Represents the relation between current user and the user
 * whose details are in this class.
 */
@DatabaseTable(tableName = UserFollowStatus.TABLE_NAME)
public class UserFollowStatus implements Parcelable {
    public static final String TABLE_NAME = "userFollowStatus";

    public abstract static class DbFields {
        public static final String ID = "id";
        public static final String USERNAME = "username";
        public static final String STATUS = "status";
        public static final String REAL_NAME = "realName";
        public static final String PROFILE_DESCRIPTION = "profileDescription";
        public static final String PROFILE_IMAGE_URL = "profileImageUrl";
        public static final String COVER_IMAGE_URL = "coverImageUrl";
        public static final String DIRECTION = "direction";
        public static final String CURRENT_USER_FOLLOW_STATUS = "currentUserFollowStatus";
        public static final String LOCALLY_CHANGED = "locallyChanged";
    }

    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.ID, id = true, canBeNull =
        false)
    private final String id;

    @NonNull
    @DatabaseField(columnName = DbFields.USERNAME, canBeNull = false)
    private final String username;

    @NonNull
    @DatabaseField(index = true, columnName = DbFields.STATUS, canBeNull = false)
    private final FollowStatus status;

    @Nullable
    @DatabaseField(columnName = DbFields.REAL_NAME)
    private final String realName;

    @Nullable
    @DatabaseField(columnName = DbFields.PROFILE_DESCRIPTION)
    private final String profileDescription;

    @Nullable
    @DatabaseField(columnName = DbFields.PROFILE_IMAGE_URL)
    private final String profileImageUrl;

    @Nullable
    @DatabaseField(columnName = DbFields.COVER_IMAGE_URL)
    private final String coverImageUrl;

    /**
     * Current (logged in) user is {@link FollowDirection#FOLLOWING} this user (whose
     * details are in this class) or this user is current user's {@link FollowDirection#FOLLOWER}
     * Remember that current user and this user can follow each other but an instance of this
     * class represents only one direction.
     */
    @NonNull
    @DatabaseField(index = true, columnName = DbFields.DIRECTION, canBeNull = false)
    private final FollowDirection direction;

    /**
     * The {@link FollowStatus} of the current (logged in) user in relation to this user (whose
     * details are in this class). When direction is {@link FollowDirection#FOLLOWING},
     * {@link UserFollowStatus#currentUserFollowStatus} will be the value of
     * {@link UserFollowStatus#status}
     * This is needed because whether user is following the target user or not is always shown in UI
     */
    @Nullable
    @DatabaseField(index = true, columnName = DbFields.CURRENT_USER_FOLLOW_STATUS)
    private final FollowStatus currentUserFollowStatus;

    @DatabaseField(dataType = DataType.BOOLEAN, columnName = DbFields.LOCALLY_CHANGED)
    private final boolean locallyChanged;

    /**
     * Only used by OrmLite.
     */
    public UserFollowStatus() {
        //noinspection ConstantConditions
        this(null, null, null, null, null, null, FollowDirection.UNKNOWN, null, false);
    }

    public UserFollowStatus(@NonNull String username, @NonNull FollowStatus status,
        @Nullable String realName, @Nullable String profileDescription,
        @Nullable String profileImageUrl, @Nullable String coverImageUrl,
        @NonNull FollowDirection direction, @Nullable FollowStatus currentUserFollowStatus,
        boolean locallyChanged) {
        id = createId(username, direction);
        this.username = username;
        this.status = status;
        this.realName = realName;
        this.profileDescription = profileDescription;
        this.profileImageUrl = profileImageUrl;
        this.coverImageUrl = coverImageUrl;
        this.direction = direction;
        this.currentUserFollowStatus = currentUserFollowStatus;
        this.locallyChanged = locallyChanged;
    }

    @NonNull
    public String getUsername() {
        return username;
    }

    @NonNull
    public FollowStatus getStatus() {
        return status;
    }

    @Nullable
    public String getRealName() {
        return realName;
    }

    @Nullable
    public String getProfileDescription() {
        return profileDescription;
    }

    @Nullable
    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    @Nullable
    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    @NonNull
    public FollowDirection getDirection() {
        return direction;
    }

    public FollowStatus getCurrentUserFollowStatus() {
        return (direction == FollowDirection.FOLLOWING && currentUserFollowStatus == null) ? status
            : currentUserFollowStatus;
    }

    public boolean isLocallyChanged() {
        return locallyChanged;
    }

    @NonNull
    public String getRealNameOrUsername() {
        if (realName == null || realName.trim().length() == 0) {
            return username;
        }
        return realName;
    }

    @NonNull
    public String getId() {
        return id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UserFollowStatus that = (UserFollowStatus) o;

        if (!id.equals(that.id)) {
            return false;
        }
        if (!username.equals(that.username)) {
            return false;
        }
        if (status != that.status) return false;
        if (realName != null ? !realName.equals(that.realName) : that.realName != null) {
            return false;
        }
        if (profileDescription != null ? !profileDescription.equals(that.profileDescription)
            : that.profileDescription != null) {
            return false;
        }
        if (profileImageUrl != null ? !profileImageUrl.equals(that.profileImageUrl)
            : that.profileImageUrl != null) {
            return false;
        }
        if (coverImageUrl != null ? !coverImageUrl.equals(that.coverImageUrl)
            : that.coverImageUrl != null) {
            return false;
        }
        if (direction != that.direction) return false;
        if (currentUserFollowStatus != that.currentUserFollowStatus) return false;
        if (locallyChanged != that.locallyChanged) return false;
        return true;
    }

    @Override
    public int hashCode() {
        int result = 0;
        result = 31 * result + id.hashCode();
        result = 31 * result + username.hashCode();
        result = 31 * result + status.hashCode();
        result = 31 * result + (realName != null ? realName.hashCode() : 0);
        result = 31 * result + (profileDescription != null ? profileDescription.hashCode() : 0);
        result = 31 * result + (profileImageUrl != null ? profileImageUrl.hashCode() : 0);
        result = 31 * result + (coverImageUrl != null ? coverImageUrl.hashCode() : 0);
        result = 31 * result + direction.hashCode();
        result = 31 * result + (currentUserFollowStatus != null ? currentUserFollowStatus.hashCode()
            : 0);
        result = 31 * result + (locallyChanged ? 1 : 0);
        return result;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(username);
        dest.writeString(status.name());
        dest.writeString(realName);
        dest.writeString(profileDescription);
        dest.writeString(profileImageUrl);
        dest.writeString(coverImageUrl);
        dest.writeString(direction.name());
        dest.writeString(currentUserFollowStatus != null ? currentUserFollowStatus.name() : null);
        dest.writeByte((byte) (locallyChanged ? 1 : 0));
    }

    public static final Parcelable.Creator<UserFollowStatus> CREATOR =
        new Creator<UserFollowStatus>() {
            @Override
            public UserFollowStatus[] newArray(int size) {
                return new UserFollowStatus[size];
            }

            @Override
            public UserFollowStatus createFromParcel(Parcel source) {
                String username = source.readString();
                FollowStatus status = FollowStatus.valueOf(source.readString());
                String realName = source.readString();
                String profileDescription = source.readString();
                String profileImageUrl = source.readString();
                String coverImageUrl = source.readString();
                FollowDirection direction = FollowDirection.valueOf(source.readString());
                FollowStatus currentUserFollowStatus = FollowStatus.valueOf(source.readString());
                boolean locallyChanged = source.readByte() == 1;
                return new UserFollowStatus(username, status, realName, profileDescription,
                    profileImageUrl, coverImageUrl, direction, currentUserFollowStatus,
                    locallyChanged);
            }
        };

    @NonNull
    public UserFollowStatus.Builder toBuilder() {
        return new UserFollowStatus.Builder(username, status, realName, profileDescription,
            profileImageUrl, coverImageUrl, direction, currentUserFollowStatus, locallyChanged);
    }

    @Override
    public String toString() {
        return "UserFollowStatus{"
            + "id='"
            + id
            + '\''
            + ", username='"
            + username
            + '\''
            + ", status="
            + status
            + ", realName='"
            + realName
            + '\''
            + ", profileDescription='"
            + profileDescription
            + '\''
            + ", profileImageUrl='"
            + profileImageUrl
            + '\''
            + ", coverImageUrl='"
            + coverImageUrl
            + '\''
            + ", direction="
            + direction
            + ", currentUserFollowStatus="
            + currentUserFollowStatus
            + ", locallyChanged="
            + locallyChanged
            + '}';
    }

    public static class Builder {
        private String username;
        private FollowStatus status;
        private String realName;
        private String profileDescription;
        private String profileImageUrl;
        private String coverImageUrl;
        private FollowDirection direction;
        private FollowStatus currentUserFollowStatus;
        private boolean locallyChanged;

        public Builder(@NonNull FollowDirection direction) {
            this.direction = direction;
        }

        Builder(String username, FollowStatus status, String realName, String profileDescription,
            String profileImageUrl, String coverImageUrl, FollowDirection direction,
            FollowStatus currentUserFollowStatus, boolean locallyChanged) {
            this.username = username;
            this.status = status;
            this.realName = realName;
            this.profileDescription = profileDescription;
            this.profileImageUrl = profileImageUrl;
            this.coverImageUrl = coverImageUrl;
            this.direction = direction;
            this.currentUserFollowStatus = currentUserFollowStatus;
            this.locallyChanged = locallyChanged;
        }

        public Builder setUsername(String username) {
            this.username = username;
            return this;
        }

        public Builder setStatus(FollowStatus status) {
            this.status = status;
            return this;
        }

        public Builder setRealName(String realName) {
            this.realName = realName;
            return this;
        }

        public Builder setProfileDescription(String profileDescription) {
            this.profileDescription = profileDescription;
            return this;
        }

        public Builder setProfileImageUrl(String profileImageUrl) {
            this.profileImageUrl = profileImageUrl;
            return this;
        }

        public Builder setCoverImageUrl(String coverImageUrl) {
            this.coverImageUrl = coverImageUrl;
            return this;
        }

        public Builder setDirection(@NonNull FollowDirection direction) {
            this.direction = direction;
            return this;
        }

        public Builder setCurrentUserFollowStatus(FollowStatus currentUserFollowStatus) {
            this.currentUserFollowStatus = currentUserFollowStatus;
            return this;
        }

        public Builder setLocallyChanged(boolean locallyChanged) {
            this.locallyChanged = locallyChanged;
            return this;
        }

        public UserFollowStatus build() {
            if (TextUtils.isEmpty(username)) {
                throw new IllegalStateException("Missing username");
            }
            if (status == null) {
                throw new IllegalStateException("Missing status");
            }
            if (direction == null) {
                throw new IllegalStateException("Missing direction");
            }
            return new UserFollowStatus(username, status, realName, profileDescription,
                profileImageUrl, coverImageUrl, direction, currentUserFollowStatus,
                locallyChanged);
        }
    }

    @NonNull
    public static String createId(String username, @NonNull FollowDirection direction) {
        return username + "_" + direction.name();
    }
}
