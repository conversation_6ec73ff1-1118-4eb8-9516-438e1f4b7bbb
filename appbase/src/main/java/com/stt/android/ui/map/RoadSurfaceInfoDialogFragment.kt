package com.stt.android.ui.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.stt.android.databinding.FragmentRoadSurfaceInfoDialogBinding
import com.stt.android.ui.utils.ExpandedBottomSheetDialogFragment

class RoadSurfaceInfoDialogFragment : ExpandedBottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentRoadSurfaceInfoDialogBinding.inflate(inflater)
        return binding.root
    }

    companion object {

        fun newInstance(): RoadSurfaceInfoDialogFragment = RoadSurfaceInfoDialogFragment()
    }
}
