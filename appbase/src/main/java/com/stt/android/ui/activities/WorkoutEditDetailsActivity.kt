package com.stt.android.ui.activities

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.app.ProgressDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.edit
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.snackbar.Snackbar
import com.google.maps.android.PolyUtil
import com.soy.algorithms.activityType.ActivityType
import com.soy.algorithms.tss.TSSCalculationMethod
import com.soy.algorithms.tss.TSSCalculator
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsEventProperty.TAGS_FIELD_EDITED_NAME
import com.stt.android.analytics.AnalyticsEventProperty.TAG_ADD_METHOD_MANUAL
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.AutomaticLocationOutcome
import com.stt.android.analytics.AnalyticsPropertyValue.ManualLocationContext
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.TagsAnalytics
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.VideoModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workout.tss.SupportedTSSCalculationMethodRepository
import com.stt.android.data.workout.tss.getAnalyticsName
import com.stt.android.di.AnalyticsSharedPreferences
import com.stt.android.domain.Point
import com.stt.android.domain.android.DaysSinceInstallationUseCase
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.AddWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.attributes.DeleteWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.FetchUnconfirmedWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.ui.extensions.minutesSinceActivityEnded
import com.stt.android.ui.extensions.targetWorkoutVisibility
import com.stt.android.ui.extensions.toWorkoutLocationOrNull
import com.stt.android.ui.fragments.WorkoutDetailsEditorFragment
import com.stt.android.ui.fragments.WorkoutDetailsEditorFragment.EditDetailField
import com.stt.android.ui.fragments.medialist.WorkoutEditMediaPickerFragment
import com.stt.android.ui.utils.DialogHelper
import com.stt.android.utils.STTConstants
import com.stt.android.viewmodel.WorkoutDetailsEditorViewModel
import com.stt.android.workouts.edit.SaveWorkoutHeaderService.Companion.enqueueManualWorkout
import com.stt.android.workouts.edit.SaveWorkoutHeaderService.Companion.enqueueWork
import com.stt.android.workouts.remove.RemoveWorkoutService.Companion.enqueueWork
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.math.roundToLong

@AndroidEntryPoint
class WorkoutEditDetailsActivity : AppCompatActivity() {
    @Inject
    internal lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    internal lateinit var firebaseAnalyticsTracker: FirebaseAnalyticsTracker

    @Inject
    internal lateinit var picturesController: PicturesController

    @Inject
    internal lateinit var videoModel: VideoModel

    @Inject
    internal lateinit var fetchUnconfirmedUseCase: FetchUnconfirmedWorkoutAttributesUpdateUseCase

    @Inject
    internal lateinit var addWorkoutAttributesUpdateUseCase: AddWorkoutAttributesUpdateUseCase

    @Inject
    internal lateinit var deleteWorkoutAttributesUpdateUseCase: DeleteWorkoutAttributesUpdateUseCase

    @Inject
    @AnalyticsSharedPreferences
    internal lateinit var analyticsSharedPrefs: SharedPreferences

    @Inject
    internal lateinit var supportedTSSCalculationMethodRepository: SupportedTSSCalculationMethodRepository

    @Inject
    internal lateinit var daysSinceInstallationUseCase: DaysSinceInstallationUseCase

    @Inject
    internal lateinit var tagsAnalytics: TagsAnalytics

    @Inject
    internal lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    private val workoutDetailsEditorViewModel: WorkoutDetailsEditorViewModel by viewModels()

    private val showSaveProgressHandler = Handler(Looper.getMainLooper())
    private var saveProgressDialog: ProgressDialog? = null
    private var saveWorkoutJob: Job? = null

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.workout_edit_details_activity)
        setupActionBar()

        if (workoutDetailsEditorViewModel.isNewWorkout && supportActionBar != null) {
            supportActionBar?.setTitle(R.string.add_workout)
        }

        val workoutId = intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, -1)
        intent.getStringExtra(EXTRA_NAVIGATION_SOURCE)?.let {
            analyticsSharedPrefs.edit {
                putString(AnalyticsEventProperty.SOURCE, it)
            }
        }

        // Attach the fragments only if it's the first time that this activity is created
        if (savedInstanceState == null) {
            val showEditLocation = intent.getBooleanExtra(EXTRA_SHOW_EDIT_LOCATION, false)
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.container,
                    WorkoutDetailsEditorFragment.newInstance(
                        if (workoutId != -1) workoutId else null,
                        false,
                        showEditLocation
                    ),
                    WorkoutDetailsEditorFragment.FRAGMENT_TAG
                ).commit()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissProgressDialog()
        saveWorkoutJob?.cancel()
    }

    private fun setupActionBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.apply {
            setTitle(R.string.edit_workout)
            setDisplayHomeAsUpEnabled(true)
        }
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        onCancelClicked()
    }

    private val isMediaEdited: Boolean
        get() {
            val fragment = workoutEditMediaPickerFragment
            return fragment != null && fragment.isMediaEdited
        }

    private fun saveWorkout() {
        // Only store if the user modified the default values
        if (workoutDetailsEditorViewModel.hasWorkoutChanged || isMediaEdited) {
            val originalSuuntoTags = workoutDetailsEditorViewModel.originalSuuntoTags
            val editedHeader = workoutDetailsEditorViewModel.workoutHeader
            saveWorkoutJob?.cancel()
            saveWorkoutJob = lifecycleScope.launch {
                showProgressDialog()
                runSuspendCatching {
                    val updatedWorkoutHeader = withContext(Dispatchers.IO) {
                        updateTSSWorkoutAttribute(editedHeader)
                        updateNonNullableWorkoutAttributes(editedHeader)
                        val workoutHeader = updateWorkoutLocation(editedHeader)
                        updateMedia(workoutHeader)
                    }

                    dismissProgressDialog()
                    if (isMediaEdited) {
                        Toast.makeText(
                            applicationContext,
                            R.string.workout_media_updated,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                    enqueueWorkoutUpdate(updatedWorkoutHeader)

                    sendAnalytics(updatedWorkoutHeader)
                    handleTagsAnalytics(
                        originalSuuntoTags,
                        updatedWorkoutHeader
                    )

                    finish()
                }.onFailure { e ->
                    Timber.w(e, "Unable to save workout")
                    dismissProgressDialog()
                    Snackbar.make(
                        findViewById(android.R.id.content),
                        getString(R.string.workout_save_error),
                        Snackbar.LENGTH_LONG
                    ).show()
                }
            }
        } else {
            setResult(RESULT_CANCELED)
            finish()
        }
    }

    private fun handleTagsAnalytics(
        oldSuuntoTags: List<SuuntoTag>,
        updatedWorkoutHeader: WorkoutHeader
    ) {
        val updatedSuuntoTags = updatedWorkoutHeader.suuntoTags
        val addedTags: List<SuuntoTag> = updatedSuuntoTags.subtract(oldSuuntoTags).toList()
        val removedTags: List<SuuntoTag> = oldSuuntoTags.subtract(updatedSuuntoTags).toList()

        addedTags.forEach { suuntoTag ->
            tagsAnalytics.trackWorkoutTagAdded(
                suuntoTag = suuntoTag,
                tagAddMethod = TAG_ADD_METHOD_MANUAL,
                workoutHeader = updatedWorkoutHeader
            )
        }

        removedTags.forEach { suuntoTag ->
            tagsAnalytics.trackWorkoutTagRemoved(
                suuntoTag = suuntoTag,
                workoutHeader = updatedWorkoutHeader
            )
        }
    }

    private suspend fun updateTSSWorkoutAttribute(workoutHeader: WorkoutHeader) {
        if (!workoutDetailsEditorViewModel.isTssUpdated) {
            return
        }

        val tss = workoutHeader.tss
        if (tss != null) {
            addWorkoutAttributesUpdateUseCase(
                params = addWorkoutAttributesUpdateUseCase.getTssUpdateParams(
                    workoutId = workoutHeader.id,
                    username = workoutHeader.username,
                    tss = tss,
                    requiresUserConfirmation = false,
                )
            )
        } else {
            deleteWorkoutAttributesUpdateUseCase(
                params = deleteWorkoutAttributesUpdateUseCase.getDeleteTssParams(
                    workoutId = workoutHeader.id,
                    username = workoutHeader.username
                )
            )
        }
    }

    private suspend fun updateNonNullableWorkoutAttributes(workoutHeader: WorkoutHeader) {
        val changedFields = workoutDetailsEditorViewModel.changedFields
        var newMaxSpeed: Double? = null
        var newAscent: Double? = null
        var newDescent: Double? = null
        var suuntoTags: List<SuuntoTag>? = null
        if (changedFields.contains(EditDetailField.MAX_SPEED)) {
            newMaxSpeed = workoutHeader.maxSpeed
        }
        if (changedFields.contains(EditDetailField.ASCENT)) {
            newAscent = workoutHeader.totalAscent
        }
        if (changedFields.contains(EditDetailField.DESCENT)) {
            newDescent = workoutHeader.totalDescent
        }
        if (changedFields.contains(EditDetailField.SUUNTO_TAGS)) {
            suuntoTags = workoutHeader.suuntoTags
        }
        if (newMaxSpeed != null || newAscent != null || newDescent != null || suuntoTags != null) {
            addWorkoutAttributesUpdateUseCase(
                params = AddWorkoutAttributesUpdateUseCase.Params(
                    workoutId = workoutHeader.id,
                    username = workoutHeader.username,
                    attributes = DomainWorkoutAttributes(
                        startPosition = null,
                        tss = null,
                        maxSpeed = newMaxSpeed,
                        totalAscent = newAscent,
                        totalDescent = newDescent,
                        suuntoTags = suuntoTags,
                    ),
                    requiresUserConfirmation = false,
                )
            )
        }
    }

    private suspend fun updateWorkoutLocation(workoutHeader: WorkoutHeader): WorkoutHeader {
        val updatedWorkoutHeader = if (workoutDetailsEditorViewModel.isWorkoutLocationUpdated) {
            val currentWorkoutLocation = workoutDetailsEditorViewModel.editedWorkoutLocation
            val polyline = if (currentWorkoutLocation == null) {
                null
            } else {
                PolyUtil.encode(
                    listOf(currentWorkoutLocation)
                )
            }
            val position = Point(
                currentWorkoutLocation?.longitude ?: 0.0,
                currentWorkoutLocation?.latitude ?: 0.0,
                null,
                0.0
            )
            workoutHeader.toBuilder()
                .startPosition(position)
                .centerPosition(position)
                .stopPosition(position)
                .polyline(polyline)
                .build()
        } else {
            workoutHeader
        }

        updateWorkoutLocationAttributes(workoutHeader)

        return updatedWorkoutHeader
    }

    private suspend fun updateWorkoutLocationAttributes(workoutHeader: WorkoutHeader) {
        if (!workoutDetailsEditorViewModel.isWorkoutLocationUpdated) {
            return
        }

        val unconfirmedLocation = workoutDetailsEditorViewModel.unconfirmedLocation
        val editedWorkoutLocation = workoutDetailsEditorViewModel.editedWorkoutLocation
        if (unconfirmedLocation != null) {
            trackLocationConfirmAutomaticLocationCompleted(
                if (unconfirmedLocation == editedWorkoutLocation) {
                    AutomaticLocationOutcome.SUGGESTED_LOCATION_CONFIRMED
                } else {
                    AutomaticLocationOutcome.NEW_LOCATION_CHOSEN
                }
            )
        }
        if (editedWorkoutLocation != null) {
            addWorkoutAttributesUpdateUseCase(
                params = addWorkoutAttributesUpdateUseCase.getLocationUpdateParams(
                    workoutId = workoutHeader.id,
                    username = workoutHeader.username,
                    latitude = editedWorkoutLocation.latitude,
                    longitude = editedWorkoutLocation.longitude,
                    requiresUserConfirmation = false,
                )
            )
            trackLocationAddManualLocationSavedEvent(
                workoutHeader,
                workoutDetailsEditorViewModel.startPosition
            )
        } else {
            deleteWorkoutAttributesUpdateUseCase(
                params = deleteWorkoutAttributesUpdateUseCase.getDeleteStartPositionParams(
                    workoutId = workoutHeader.id,
                    username = workoutHeader.username,
                ),
            )
            trackLocationAddedLocationDeletedEvent(workoutHeader)
        }
    }

    /**
     * Creates a job to store changes to the WorkoutHeader using SaveWorkoutService.
     *
     * @param workoutHeader Updated workout header.
     */
    private fun enqueueWorkoutUpdate(workoutHeader: WorkoutHeader) {
        // This is a new manual workout without HR.
        // Only store workout header
        if (workoutDetailsEditorViewModel.isNewWorkout) {
            enqueueManualWorkout(this, workoutHeader, true)
        } else {
            enqueueWork(this, workoutHeader, true)
        }
        if (workoutDetailsEditorViewModel.hasWorkoutChanged) {
            setResult(
                STTConstants.RequestResult.WORKOUT_EDITED,
                Intent().putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
            )
        } else if (isMediaEdited) {
            // Only media is edited
            setResult(STTConstants.RequestResult.MEDIA_EDITED)
        }
    }

    private suspend fun updateMedia(
        workoutHeader: WorkoutHeader
    ): WorkoutHeader {
        val fragment = workoutEditMediaPickerFragment
        var pictureCount = workoutHeader.pictureCount
        if (fragment != null) {
            pictureCount = fragment.pictureCount
            Timber.d("updateMedia: saving media, pictureCount=%d", pictureCount)
            fragment.saveMedia()
        }
        return workoutHeader.toBuilder()
            .pictureCount(pictureCount)
            .build()
    }

    private val workoutDetailsEditorFragment: WorkoutDetailsEditorFragment?
        get() = supportFragmentManager.findFragmentByTag(
            WorkoutDetailsEditorFragment.FRAGMENT_TAG
        ) as WorkoutDetailsEditorFragment?

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.workout_edit_details_activity, menu)
        if (workoutDetailsEditorViewModel.isNewWorkout) {
            menu.removeItem(R.id.delete)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.save -> {
                onSaveClicked()
                return true
            }
            R.id.delete -> {
                onDeleteClicked()
                return true
            }
            R.id.cancel,
            android.R.id.home -> {
                onCancelClicked()
                return true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun onSaveClicked() {
        hideKeyboard(this)
        val editedHeader = workoutDetailsEditorViewModel.workoutHeader
        val tss = editedHeader.tss
        val supportsMultipleCalculatedTssTypes = TSSCalculator.supportedCalculationMethods(
            activityType = ActivityType.valueOf(editedHeader.activityType.id),
            hasDefaultPaceZones = false,
            hasDefaultPowerZones = false,
            hasDynamicDfaZones = true,
        ).size > 1

        if (tss != null &&
            tss.calculationMethod !== TSSCalculationMethod.MANUAL &&
            supportsMultipleCalculatedTssTypes &&
            workoutDetailsEditorViewModel.isTssCalculationMethodChanged
        ) {
            askSetPreferredTssCalculationMethod(tss.calculationMethod)
        } else {
            saveWorkout()
        }
    }

    private fun askSetPreferredTssCalculationMethod(toMethod: TSSCalculationMethod) {
        val setMethodAsPreferred = AtomicBoolean(false)
        val alertDialog = AlertDialog.Builder(this)
            .setTitle(R.string.use_selected_tss_calculation_method_title)
            .setSingleChoiceItems(
                R.array.use_selected_tss_calculation_method_options,
                0
            ) { _: DialogInterface?, which: Int -> setMethodAsPreferred.set(which == 1) }
            .setPositiveButton(R.string.save) { _: DialogInterface?, _: Int ->
                workoutDetailsEditorViewModel.workoutHeader.let {
                    if (setMethodAsPreferred.get()) {
                        supportedTSSCalculationMethodRepository
                            .setUserSelectedCalculationMethodForType(it.activityType, toMethod)
                    }
                    saveWorkout()
                }
            }
            .create()
        alertDialog.setCanceledOnTouchOutside(false)
        alertDialog.show()
    }

    private fun onDeleteClicked() {
        DialogHelper.showDialog(
            this,
            R.string.delete,
            R.string.delete_workout,
            { _: DialogInterface?, _: Int -> deleteWorkout() },
            null
        )
    }

    private fun deleteWorkout() {
        val workoutHeaderToDelete = workoutDetailsEditorViewModel.workoutHeader
        enqueueWork(this, workoutHeaderToDelete)
        setResult(
            STTConstants.RequestResult.WORKOUT_DELETED,
            Intent().putExtra(
                STTConstants.ExtraKeys.WORKOUT_HEADER,
                workoutHeaderToDelete
            )
        )
        finish()
    }

    private fun onCancelClicked() {
        hideKeyboard(this)
        if (workoutDetailsEditorViewModel.hasWorkoutChanged || isMediaEdited) {
            DialogHelper.showDialog(
                this,
                0,
                R.string.cancel_confirm,
                { _: DialogInterface?, _: Int ->
                    setResult(RESULT_CANCELED)
                    finish()
                },
                null
            )
        } else {
            finish()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        // Forward results to EasyPermissions
        val pickerFragment: Fragment? = workoutEditMediaPickerFragment
        if (pickerFragment != null) {
            EasyPermissions.onRequestPermissionsResult(
                requestCode,
                permissions,
                grantResults,
                this,
                pickerFragment
            )
        }
    }

    private val workoutEditMediaPickerFragment: WorkoutEditMediaPickerFragment?
        get() {
            val fragment = workoutDetailsEditorFragment
            return if (fragment != null) {
                fragment.childFragmentManager.findFragmentByTag(
                    WorkoutEditMediaPickerFragment.FRAGMENT_TAG
                ) as WorkoutEditMediaPickerFragment?
            } else {
                null
            }
        }

    private fun sendAnalytics(workoutHeader: WorkoutHeader) {
        val sharingVisibility = SharingOption.getVisibility(
            workoutHeader.sharingOptions,
            workoutHeader.isShared
        )
        if (workoutDetailsEditorViewModel.isNewWorkout) {
            sendWorkoutSavedAnalytics(workoutHeader, sharingVisibility)
        } else {
            sendWorkoutEditedAnalytics(workoutHeader, sharingVisibility)
        }
    }

    private fun sendWorkoutSavedAnalytics(
        workoutHeader: WorkoutHeader,
        sharingVisibility: String
    ) {
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
            .put(
                AnalyticsEventProperty.DURATION_IN_MINUTES,
                (workoutHeader.totalTime / 60.0).roundToLong()
            )
            .put(
                AnalyticsEventProperty.DISTANCE_IN_METERS,
                workoutHeader.totalDistance.roundToLong()
            )
            .put(AnalyticsEventProperty.NUM_PHOTOS, 0)
            .putYesNo(
                AnalyticsEventProperty.HAS_DESCRIPTION,
                !TextUtils.isEmpty(workoutHeader.description)
            )
            .put(AnalyticsEventProperty.MANUALLY_ADDED, AnalyticsPropertyValue.YES)
            .put(
                AnalyticsEventProperty.DAYS_SINCE_FIRST_SESSION,
                daysSinceInstallationUseCase.getDaysSinceInstallation()
            )
            .put(AnalyticsEventProperty.VISIBILITY, sharingVisibility)

        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.WORKOUT_SAVED,
            properties
        )
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.WORKOUT_SAVED,
            properties.map
        )
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_SAVED, properties)
    }

    private fun sendWorkoutEditedAnalytics(
        workoutHeader: WorkoutHeader,
        sharingVisibility: String
    ) {
        lifecycleScope.launch {
            val achievementsCount = workoutDetailsEditorViewModel.getAchievementsCount()
            val source = analyticsSharedPrefs.getString(
                AnalyticsEventProperty.SOURCE,
                AnalyticsPropertyValue.SourceProperty.EDIT_BUTTON
            )

            val properties = AnalyticsProperties()
            val fieldsNames: MutableList<String> = ArrayList()
            for (field in workoutDetailsEditorViewModel.changedFields) {
                fieldsNames.add(field.toString())
            }
            if (fieldsNames.contains(EditDetailField.SUUNTO_TAGS.toString())) {
                // We have two different types of tags (suuntoTags and soon userTags);
                // backend is expecting just `Tags` as analytics
                fieldsNames.remove(EditDetailField.SUUNTO_TAGS.toString())
                fieldsNames.add(TAGS_FIELD_EDITED_NAME)
            }

            val tss = workoutHeader.tss
            if (tss != null && workoutDetailsEditorViewModel.isTssCalculationMethodChanged) {
                properties.put(
                    AnalyticsEventProperty.NEW_TSS_METHOD,
                    tss.calculationMethod.getAnalyticsName()
                )
            }
            if (workoutDetailsEditorViewModel.isWorkoutLocationUpdated) {
                fieldsNames.add("Location")
            }
            val workoutLocation = workoutHeader.startPosition.toWorkoutLocationOrNull()
            properties.put(AnalyticsEventProperty.CHANGES_COUNT, fieldsNames.size)
                .put(AnalyticsEventProperty.FIELDS_CHANGED, fieldsNames)
                .put(AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY, sharingVisibility)
                .put(AnalyticsEventProperty.NUM_PHOTOS, workoutHeader.pictureCount)
                .put(AnalyticsEventProperty.NUM_LIKES, workoutHeader.reactionCount)
                .put(AnalyticsEventProperty.NUM_COMMENTS, workoutHeader.commentCount)
                .putYesNo(
                    AnalyticsEventProperty.HAS_DESCRIPTION,
                    !workoutHeader.description.isNullOrBlank()
                )
                .put(
                    AnalyticsEventProperty.ACTIVITY_TYPE,
                    workoutHeader.activityType.simpleName
                )
                .put(
                    AnalyticsEventProperty.DURATION_IN_MINUTES,
                    (workoutHeader.totalTime.toFloat() / 60.0f).roundToLong()
                )
                .put(AnalyticsEventProperty.DISTANCE_IN_METERS, workoutHeader.totalDistance)
                .put(
                    AnalyticsEventProperty.DISTANCE_FROM_START_TO_END,
                    workoutHeader.calculateDistanceFromStartToEndOrNull()?.roundToInt()
                )
                .put(AnalyticsEventProperty.SOURCE, source)
                .put(AnalyticsEventProperty.NUM_PHOTOS_ADDED, 0)
                .put(AnalyticsEventProperty.NUM_VIDEOS_ADDED, 0)
                .put(AnalyticsEventProperty.NUMBER_OF_ACHIEVEMENTS, achievementsCount)
                .putYesNo(
                    AnalyticsEventProperty.DESCRIPTION_ADDED,
                    workoutDetailsEditorViewModel.isDescriptionAdded
                )
                .putYesNo(AnalyticsEventProperty.LOCATION_ADDED, workoutLocation != null)

            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.EDIT_WORKOUT_SAVED,
                properties
            )
            firebaseAnalyticsTracker.trackEvent(
                AnalyticsEvent.EDIT_WORKOUT_SAVED,
                properties
            )
        }
    }

    private fun trackLocationAddManualLocationSavedEvent(
        workoutHeader: WorkoutHeader?,
        startPosition: LatLng?
    ) {
        if (workoutHeader == null) return
        val properties = AnalyticsProperties()
        properties
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
            .put(
                AnalyticsEventProperty.DURATION_IN_MINUTES,
                (workoutHeader.totalTime / 60.0).roundToInt()
            )
            .put(
                AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY,
                workoutHeader.targetWorkoutVisibility()
            )
        if (startPosition == null) {
            properties.put(
                AnalyticsEventProperty.CONTEXT,
                if (workoutDetailsEditorViewModel.isNewWorkout) ManualLocationContext.ADD_MANUAL_WORKOUT else ManualLocationContext.EDIT_WORKOUT
            )
        }
        val event =
            if (startPosition == null) AnalyticsEvent.LOCATION_ADD_MANUAL_LOCATION_SAVED else AnalyticsEvent.LOCATION_ADDED_LOCATION_EDITING_SAVED
        amplitudeAnalyticsTracker.trackEvent(event, properties)
    }

    private fun trackLocationAddedLocationDeletedEvent(workoutHeader: WorkoutHeader?) {
        if (workoutHeader == null) return
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
            .put(
                AnalyticsEventProperty.DURATION_IN_MINUTES,
                (workoutHeader.totalTime / 60.0).roundToInt()
            )
            .put(
                AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY,
                workoutHeader.targetWorkoutVisibility()
            )
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.LOCATION_ADDED_LOCATION_DELETED,
            properties
        )
    }

    private fun trackLocationConfirmAutomaticLocationCompleted(outcome: String) {
        workoutDetailsEditorViewModel.workoutHeader.let {
            val properties = AnalyticsProperties()
                .put(AnalyticsEventProperty.ACTIVITY_TYPE, it.activityType.simpleName)
                .put(
                    AnalyticsEventProperty.MINUTES_SINCE_ACTIVITY_ENDED,
                    it.minutesSinceActivityEnded()
                )
                .put(
                    AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY,
                    it.targetWorkoutVisibility()
                )
                .put(AnalyticsEventProperty.OUTCOME, outcome)
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.LOCATION_CONFIRM_AUTOMATIC_LOCATION_COMPLETED,
                properties
            )
        }
    }

    private fun showProgressDialog() {
        dismissProgressDialog()
        val context: Context = this
        val showProgressDialogRunnable = Runnable {
            saveProgressDialog = ProgressDialog(context)
            saveProgressDialog?.setCancelable(false)
            saveProgressDialog?.setTitle(R.string.please_wait)
            saveProgressDialog?.show()
        }

        // Show progress dialog with a small delay so that when saving is quick, it does not flash
        // on the screen
        showSaveProgressHandler.postDelayed(showProgressDialogRunnable, 250L)
    }

    private fun dismissProgressDialog() {
        showSaveProgressHandler.removeCallbacksAndMessages(null)
        saveProgressDialog?.dismiss()
        saveProgressDialog = null
    }

    companion object {
        const val EXTRA_NAVIGATION_SOURCE =
            "com.stt.android.ui.activities.WorkoutEditDetailsActivity.NAVIGATION_SOURCE"
        const val EXTRA_SHOW_EDIT_LOCATION = "showEditLocation"

        const val EXTRA_WORKOUT_START_TIME = "workoutStartTime"

        @JvmStatic
        fun newStartIntentForNew(context: Context?): Intent =
            Intent(context, WorkoutEditDetailsActivity::class.java)

        @JvmStatic
        fun newStartIntentNewWithStartTime(context: Context?, startTimeMillis: Long): Intent =
            Intent(context, WorkoutEditDetailsActivity::class.java).apply {
                putExtra(EXTRA_WORKOUT_START_TIME, startTimeMillis)
            }

        /**
         * Use this to edit an already existing workout.
         *
         *
         * Please use [ startActivityForResult][ActivityCompat.startActivityForResult]
         * with `requestCode` [EDIT_WORKOUT][STTConstants.RequestCodes.EDIT_WORKOUT] to fire
         * the intent returned.
         *
         *
         * This activity will set the `resultCode` to one of
         * [EditWorkoutResult][STTConstants.EditWorkoutResult]
         */
        @JvmStatic
        fun newStartIntentForEditing(context: Context?, workoutId: Int, source: String?) =
            Intent(context, WorkoutEditDetailsActivity::class.java).apply {
                putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutId)
                putExtra(EXTRA_NAVIGATION_SOURCE, source)
            }

        // Taken from: https://stackoverflow.com/a/17789187/690993
        fun hideKeyboard(activity: Activity) {
            val imm = activity.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager?
            // Find the currently focused view, so we can grab the correct window token from it.
            var view = activity.currentFocus
            // If no view currently has focus, create a new one, just so we can grab a window token
            // from it
            if (view == null) {
                view = View(activity)
            }
            imm?.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }
}
