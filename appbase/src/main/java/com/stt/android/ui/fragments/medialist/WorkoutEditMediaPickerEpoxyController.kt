package com.stt.android.ui.fragments.medialist

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.mediaPickerGalleryButton
import com.stt.android.multimedia.picker.calculateId
import javax.inject.Inject

class WorkoutEditMediaPickerEpoxyController
@Inject constructor() : ViewStateEpoxyController<WorkoutMediaContainer>() {

    override fun buildModels(viewState: ViewState<WorkoutMediaContainer?>) {
        val data = viewState.data ?: return
        val count = data.media.size

        if (count > 0) {
            mediaPickerGalleryButton {
                id("gallery button")
                onClicked { _, _, _, _ -> data.onGalleryClicked.invoke() }
            }
        }

        viewState.data.media.forEachIndexed { index, mediaInfo ->
            workoutEditMediaPickerItem {
                id(mediaInfo.calculateId())
                mediaInfoForPicker(mediaInfo)
                enableRightPadding(index + 1 != count) // Horizontal spacing using padding
                itemSelected(false)
                showSelectionIcon(data.showSelectionIcon)
                showRemoveIcon(data.showRemoveIcon)
                onClicked { model, _, _, _ ->
                    model.mediaInfoForPicker()?.let { data.onClicked(it) }
                }

                onRemoveClicked { model, _, _, _ ->
                    model.mediaInfoForPicker()?.let { data.onRemoveClicked(it) }
                }
            }
        }
    }
}
