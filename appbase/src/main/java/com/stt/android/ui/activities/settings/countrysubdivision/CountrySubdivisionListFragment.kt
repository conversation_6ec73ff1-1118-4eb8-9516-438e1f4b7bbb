package com.stt.android.ui.activities.settings.countrysubdivision

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentCountrySubdivisionListBinding
import com.stt.android.home.settings.SettingsActivity
import com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionListActivity.Companion.ANALYTICS_CONTEXT
import com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionListActivity.Companion.SHOW_COUNTRY_SELECTION_BUTTON
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CountrySubdivisionListFragment :
    ViewStateListFragment2<CountrySubdivisionListContainer, CountrySubdivisionListViewModel>() {

    override val viewModel: CountrySubdivisionListViewModel by viewModels()
    override val layoutId = R.layout.fragment_country_subdivision_list

    private val binding: FragmentCountrySubdivisionListBinding get() = requireBinding()

    private lateinit var saveButton: Button

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        activity?.intent?.getStringExtra(ANALYTICS_CONTEXT)?.let {
            viewModel.analyticsContext = it
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        saveButton = binding.saveButton
        saveButton.setOnClickListener { saveUserCountrySubdivision() }
        binding.selectCountry.setOnClickListener { openCountryPreference() }

        activity?.intent?.getBooleanExtra(SHOW_COUNTRY_SELECTION_BUTTON, false)?.let {
            binding.selectCountry.visibility = if (it) View.VISIBLE else View.GONE
        }

        return binding.root
    }

    private fun saveUserCountrySubdivision() {
        viewModel.saveUserCountrySubdivision()
        activity?.finish()
    }

    private fun openCountryPreference() {
        viewModel.resetCountrySubdivision()
        context?.let {
            startActivity(
                SettingsActivity.newPreferenceStartIntent(
                    it,
                    R.string.country_preference
                )
            )
            activity?.finish()
        }
    }
}
