package com.stt.android.ui.map.selection

import android.content.Context
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.stt.android.R
import com.stt.android.databinding.ItemHeatmapSelectionBinding
import com.stt.android.domain.user.HeatmapType
import com.stt.android.core.R as CR

class HeatmapSelectionItem(
    val heatmapType: HeatmapType?,
    viewModel: MapSelectionViewModel,
    lifecycleOwner: LifecycleOwner,
    context: Context
) : BaseMapSelectionItem<ItemHeatmapSelectionBinding>(lifecycleOwner) {

    val title: String = heatmapType?.let { context.getString(it.titleResource) }
        ?: context.getString(R.string.heatmap_none)

    val color = heatmapType?.let { ContextCompat.getColor(context, it.colorResource) } ?: 0

    val iconResource = heatmapType?.iconResource

    val isSelected: LiveData<Boolean> = viewModel.selectedHeatmapType.map { heatmapType == it }

    val showPremiumOverlay: LiveData<Boolean> = viewModel.hasPremium.map { hasPremium ->
        !hasPremium && (heatmapType?.requiresPremium ?: false)
    }

    override fun getPrimaryTextColor(context: Context): Int =
        if (heatmapType?.requiresPremium == true) {
            context.getColor(CR.color.dark_grey_st)
        } else {
            super.getPrimaryTextColor(context)
        }

    override fun getLayout() = R.layout.item_heatmap_selection

    // Set max lines to the number of words in the title.
    // This prevents splitting the words to multiple lines instead of reducing font size.
    fun getMaxTitleLines(): Int {
        return (title.split("\\s".toRegex()).size).coerceAtLeast(1)
    }
}
