package com.stt.android.ui.activities

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.IntentCompat
import com.stt.android.R
import com.stt.android.appupdates.AppUpdatesActivity
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.remote.appversion.AppUpgradeInfo
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AppUpdateActivity : AppCompatActivity() {

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePrefs: SharedPreferences

    private var appUpgradeInfo: AppUpgradeInfo? = null

    private var appUpdateDialog: AlertDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            appUpgradeInfo = IntentCompat.getParcelableExtra(
                intent,
                KEY_APP_UPGRADE_INFO,
                AppUpgradeInfo::class.java
            )
            appUpgradeInfo?.let { showAppUpdateDialog(it) } ?: finish()
        } catch (e: Exception) {
            Timber.w(e, "AppUpdateActivity error")
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        appUpgradeInfo?.let { info ->
            if (info.isForceUpgrade() && appUpdateDialog?.isShowing != true) {
                showAppUpdateDialog(info)
            }
        }
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    private fun showAppUpdateDialog(upgradeInfo: AppUpgradeInfo) {
        val title: String
        val message: String
        val positiveButtonText = getString(R.string.update)
        val negativeButtonText: String
        val source: String
        if (upgradeInfo.isForceUpgrade()) {
            title = getString(R.string.app_update_force_update_title)
            message = getString(R.string.app_update_force_update_message)
            negativeButtonText = getString(R.string.quit)
            source = AppUpdatesActivity.SOURCE_MANDATORY_UPDATE_POPUP
        } else {
            title = getString(R.string.app_update_recommend_update_title)
            message = getString(R.string.app_update_recommend_update_message)
            negativeButtonText = getString(R.string.later)
            source = AppUpdatesActivity.SOURCE_RECOMMENDED_UPDATE_POPUP
        }

        val builder = AlertDialog.Builder(this, R.style.SimpleAlertDialog)
            .setTitle(title)
            .setMessage(message)
            .setCancelable(false)
            .setPositiveButton(positiveButtonText) { _, _ ->
                startActivity(AppUpdatesActivity.newStartIntent(this, source))
                if (!upgradeInfo.isForceUpgrade()) {
                    finish()
                }
            }
            .setNegativeButton(negativeButtonText) { _, _ ->
                if (upgradeInfo.isForceUpgrade()) {
                    finishAffinity()
                } else {
                    finish()
                }
            }
        appUpdateDialog = builder.create().apply {
            show()
        }
    }

    companion object {
        private const val KEY_APP_UPGRADE_INFO = "com.stt.android.KEY_APP_UPGRADE_INFO"

        @JvmStatic
        fun newIntent(context: Context, appUpgradeInfo: AppUpgradeInfo): Intent {
            val intent = Intent(context, AppUpdateActivity::class.java)
            intent.putExtra(KEY_APP_UPGRADE_INFO, appUpgradeInfo)
            return intent
        }
    }
}
