package com.stt.android.ui.fragments;

import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.github.xizzhu.simpletooltip.ToolTipView;
import com.stt.android.R;
import com.stt.android.databinding.FragmentSimilarWorkoutsBinding;
import com.stt.android.domain.user.workout.SimilarWorkoutSummary;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.infomodel.SummaryItem;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.tasks.SimilarWorkoutLoader;
import com.stt.android.ui.activities.SimilarWorkoutsActivity;
import com.stt.android.ui.fragments.workout.BaseWorkoutHeaderFragment;
import com.stt.android.ui.utils.ToolTipHelper;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;
import javax.inject.Inject;
import timber.log.Timber;

/**
 * Fragment that shows basic information regarding similar workouts. That is, shows the rank of the
 * given workout compared to similar ones and other details.
 */
@AndroidEntryPoint
public class SimilarWorkoutsFragment extends BaseWorkoutHeaderFragment
    implements View.OnClickListener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.ui.fragments.SimilarWorkoutsFragment.FRAGMENT_TAG";

    public static SimilarWorkoutsFragment newInstance(WorkoutHeader workoutHeader) {
        SimilarWorkoutsFragment fragment = new SimilarWorkoutsFragment();

        Bundle args = new Bundle();
        // We don't need the polyline, so remove it to avoid TransactionTooLargeException
        args.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER,
            workoutHeader.toBuilder().polyline(null).build());
        fragment.setArguments(args);

        return fragment;
    }

    @Inject
    SimilarWorkoutLoader similarWorkoutLoader;

    @Inject
    InfoModelFormatter infoModelFormatter;

    @NonNull
    private final CompositeDisposable disposable = new CompositeDisposable();

    private FragmentSimilarWorkoutsBinding binding;
    private ToolTipView toolTipView;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentSimilarWorkoutsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        binding.similarRouteContainer.setOnClickListener(this);
        binding.similarDistanceContainer.setOnClickListener(this);
        showTooltipIfNeeded();
        loadSimilarWorkouts(getWorkoutHeader());
    }

    @Override
    public void onDestroy() {
        disposable.dispose();
        super.onDestroy();
    }

    private void loadSimilarWorkouts(@NonNull WorkoutHeader workoutHeader) {
        disposable.add(
            similarWorkoutLoader.loadSimilarWorkoutSummaryRx(workoutHeader)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(this::populateUi, e -> {
                    Timber.w(e, "Failed to load similar workouts");
                    populateUi(null);
                })
        );
    }

    private void populateUi(@Nullable SimilarWorkoutSummary similarWorkoutSummary) {
        if (!isAdded() || binding == null || similarWorkoutSummary == null) {
            return;
        }

        View rootView = getView();
        Context context = getContext();
        if (rootView == null || context == null) {
            return;
        }

        boolean hasSimilarDistance = similarWorkoutSummary.getHasSimilarDistance();
        boolean hasSimilarRoute = similarWorkoutSummary.getHasSimilarRoute();
        if (!hasSimilarDistance && !hasSimilarRoute) {
            rootView.setVisibility(View.GONE);
            return;
        }
        binding.comparisonsTitle.setVisibility(View.VISIBLE);

        if (hasSimilarRoute) {
            setSimilarRouteVisibility(View.VISIBLE);

            SimilarWorkoutSummary.Rank similarRouteRank = similarWorkoutSummary.getSimilarRoute();
            binding.similarRouteTimeDifference.setText(getString(
                similarRouteRank.getRank() == 1 ? R.string.fastest_by : R.string.from_your_best,
                infoModelFormatter.formatValue(SummaryItem.DURATION, similarRouteRank.getTimeFromBest()).getValue()));

            binding.similarRouteRank.setText(context.getResources().getString(
                R.string.numeric_rank,
                similarRouteRank.getRank(),
                similarRouteRank.getTotal()
            ));

            showTooltipIfNeeded();
        }

        if (hasSimilarDistance) {
            setSimilarDistanceVisibility(View.VISIBLE);

            SimilarWorkoutSummary.Rank similarDistanceRank = similarWorkoutSummary.getSimilarDistance();
            binding.similarDistanceTimeDifference.setText(getString(
                similarDistanceRank.getRank() == 1 ? R.string.fastest_by : R.string.from_your_best,
                infoModelFormatter.formatValue(SummaryItem.DURATION, similarDistanceRank.getTimeFromBest()).getValue()));

            binding.similarDistanceRank.setText(context.getResources().getString(
                R.string.numeric_rank,
                similarDistanceRank.getRank(),
                similarDistanceRank.getTotal()
            ));
        }
    }

    private void setSimilarDistanceVisibility(int visibility) {
        if (binding != null) {
            binding.similarDistanceContainer.setVisibility(visibility);
            binding.similarDistance.setVisibility(visibility);
            binding.similarDistanceTimeDifference.setVisibility(visibility);
            binding.similarDistanceRank.setVisibility(visibility);
        }
    }

    private void setSimilarRouteVisibility(int visibility) {
        if (binding != null) {
            binding.similarRouteContainer.setVisibility(visibility);
            binding.similarRoute.setVisibility(visibility);
            binding.similarRouteTimeDifference.setVisibility(visibility);
            binding.similarRouteRank.setVisibility(visibility);
        }
    }

    @Override
    protected void onWorkoutHeaderUpdated(WorkoutHeader updateWorkoutHeader) {
        WorkoutHeader current = getWorkoutHeader();
        if (!current.getActivityType().equals(updateWorkoutHeader.getActivityType())
            || current.getStartTime() != updateWorkoutHeader.getStartTime()
            || Double.compare(current.getTotalDistance(), updateWorkoutHeader.getTotalDistance())
            != 0) {
            // we don't want to re-fresh the UI if relevant info is not changed
            loadSimilarWorkouts(updateWorkoutHeader);
        }
    }

    private void showTooltipIfNeeded() {
        View rootView = getView();
        if (rootView == null || binding == null) {
            return;
        }

        ViewGroup tooltipContainer = binding.tooltipContainer;
        if (binding.similarRouteContainer.getVisibility() != View.VISIBLE) {
            // we don't show the tooltip if there's no similar workouts for similar routes
            return;
        }

        Context context = requireContext();
        if (!ToolTipHelper.hasShownToolTip(context, ToolTipHelper.KEY_COMPARE_WORKOUT_TOOL_TIP)) {
            toolTipView =
                ToolTipHelper.createToolTipView(context, binding.similarRouteContainer, tooltipContainer,
                    R.string.tool_tip_compare_workout, Gravity.BOTTOM);
            toolTipView.show();
            ToolTipHelper.markToolTipAsShown(context, ToolTipHelper.KEY_COMPARE_WORKOUT_TOOL_TIP);
        }
    }

    @Override
    public void onClick(View v) {
        startActivity(SimilarWorkoutsActivity.newStartIntent(getActivity(), getWorkoutHeader(),
            v == binding.similarRouteContainer ? SimilarWorkoutsListFragment.SimilarTag.BY_ROUTE
                : SimilarWorkoutsListFragment.SimilarTag.BY_DISTANCE));

        if (toolTipView != null) {
            toolTipView.remove();
            toolTipView = null;
        }
    }
}
