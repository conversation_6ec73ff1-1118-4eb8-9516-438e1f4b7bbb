package com.stt.android.ui.components

import android.app.Dialog
import android.os.Bundle
import android.text.InputType
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import com.stt.android.R
import com.stt.android.databinding.DialogStringValueBinding
import timber.log.Timber

class StringValueDialogFragment : DialogFragment() {

    private var selectedListener: StringValueSelectedListener? = null
    private var valueChangedListener: Editor.ValueChangedListener<String>? = null
    var positiveButton: Button? = null
    private var _binding: DialogStringValueBinding? = null
    val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (selectedListener == null) {
            selectedListener =
                (targetFragment as? StringValueSelectedListener)
                    ?: (activity as? StringValueSelectedListener)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(KEY_VALUE_INPUT, binding.stringValueEditor.value)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.dialog_string_value,
            null,
            false
        )

        val stringValueEditor: StringValueEditor = binding.stringValueEditor
        var title = ""
        var message = ""
        var allowEmptyOrBlankValues = true
        var minCharacters = 0

        arguments?.apply {
            getString(TITLE)?.let { title = it }
            getString(MESSAGE)?.let { message = it }
            allowEmptyOrBlankValues = getBoolean(ALLOW_EMPTY_OR_BLANK, allowEmptyOrBlankValues)
            stringValueEditor.setMaxCharacterCount(getInt(MAX_CHARACTERS), countAsUTF8Bytes = false)
            stringValueEditor.value = extractInitialValue(this, savedInstanceState)
            stringValueEditor.setInputType(getInt(INPUT_TYPE))
            minCharacters = getInt(MIN_CHARACTERS)
        }

        binding.stringDialogMessage.text = message

        val dialog = AlertDialog.Builder(requireContext())
            .setPositiveButton(R.string.save) { _, _ ->
                val valueFromEditor = stringValueEditor.value.trim()
                selectedListener?.valueUpdatedFromDialog(
                    tag,
                    valueFromEditor
                )
                dismiss()
            }
            .setNegativeButton(R.string.cancel, null)
            .setView(binding.root)
            .setTitle(title)
            .create()

        dialog.setOnShowListener {
            fun updateEmptyError(text: CharSequence?) {
                val isValid = (allowEmptyOrBlankValues || text?.isNotBlank() == true) && (text?.length
                    ?: 0) >= minCharacters
                stringValueEditor.editorValue.error = if (isValid) {
                    null
                } else {
                    resources.getString(R.string.min_character_limit, minCharacters)
                }

                dialog.getButton(AlertDialog.BUTTON_POSITIVE).isEnabled = isValid
            }

            stringValueEditor.editorValue.doOnTextChanged { text, _, _, _ -> updateEmptyError(text) }
            updateEmptyError(stringValueEditor.value)
        }

        return dialog
    }

    private fun extractInitialValue(arguments: Bundle, savedInstanceState: Bundle?): String? {
        if (savedInstanceState?.containsKey(KEY_VALUE_INPUT) == true) {
            return savedInstanceState.getString(KEY_VALUE_INPUT)
        }
        return arguments.getString(INITIAL_VALUE)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.unbind()
        _binding = null
        positiveButton = null
    }

    override fun onStart() {
        super.onStart()
        try {
            positiveButton = (dialog as AlertDialog).getButton(Dialog.BUTTON_POSITIVE)
            binding.stringValueEditor.setOnValueChangedListener {
                valueChangedListener?.onValueChanged(it)
            }
        } catch (e: ClassCastException) {
            Timber.w(e)
        }
    }

    fun setStringValueSelectedListener(listener: StringValueSelectedListener) {
        selectedListener = listener
    }

    fun setOnValueChangeListener(listener: Editor.ValueChangedListener<String>) {
        valueChangedListener = listener
    }

    interface StringValueSelectedListener {
        fun valueUpdatedFromDialog(tag: String?, value: String)
    }

    companion object {
        private const val TITLE = "title"
        private const val MESSAGE = "message"
        private const val MAX_CHARACTERS = "maxCharacters"
        private const val INITIAL_VALUE = "initialValue"
        private const val INPUT_TYPE = "inputType"
        private const val ALLOW_EMPTY_OR_BLANK = "allowEmptyOrBlank"

        private const val KEY_VALUE_INPUT = "KEY_VALUE_INPUT"
        private const val MIN_CHARACTERS = "minCharacters"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            title: String? = null,
            message: String? = null,
            stringValueType: StringDialogValueType,
            initialValue: String,
            inputType: Int = InputType.TYPE_TEXT_FLAG_MULTI_LINE
        ): StringValueDialogFragment {
            val dialogFragment = StringValueDialogFragment()
            dialogFragment.arguments = Bundle().apply {
                putString(TITLE, title)
                putString(MESSAGE, message)
                putInt(MAX_CHARACTERS, stringValueType.maxCharacterCount)
                putString(INITIAL_VALUE, initialValue)
                putInt(INPUT_TYPE, inputType)
                putBoolean(ALLOW_EMPTY_OR_BLANK, stringValueType.allowEmptyOrBlankValues)
                putInt(MIN_CHARACTERS, stringValueType.minCharacterCount)
            }
            return dialogFragment
        }
    }
}
