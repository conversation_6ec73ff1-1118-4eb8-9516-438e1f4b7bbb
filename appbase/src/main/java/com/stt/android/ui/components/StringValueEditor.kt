package com.stt.android.ui.components

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.util.AttributeSet
import android.view.Gravity
import android.view.inputmethod.EditorInfo
import com.stt.android.utils.Utf8ByteLengthInputFilter

class StringValueEditor(
    context: Context,
    val attrs: AttributeSet
) : InlineEditor<String>(context, attrs) {

    init {
        editorValue.inputType = InputType.TYPE_TEXT_FLAG_MULTI_LINE
        editorValue.isSingleLine = false
        editorValue.gravity = Gravity.START
        editorValue.imeOptions = EditorInfo.IME_ACTION_DONE
    }

    override fun convertToValue(text: Editable?): String {
        return text.toString()
    }

    fun setMaxCharacterCount(maxCharacterCount: Int, countAsUTF8Bytes: Boolean = false) {
        if (countAsUTF8Bytes) {
            // Limit to maxCharacterCount bytes when encoded as UTF-8
            editorValue.filters = arrayOf(Utf8ByteLengthInputFilter(maxCharacterCount))
        } else {
            // Limit to maxCharacterCount UTF-16 code points. Special characters outside of the
            // Basic Multilingual Plane like emojis may form a surrogate pair and take up two
            // code points
            editorValue.filters = arrayOf(InputFilter.LengthFilter(maxCharacterCount))
        }
    }

    fun setInputType(inputType: Int) {
        editorValue.inputType = inputType
    }
}
