package com.stt.android.ui.activities.map

import androidx.lifecycle.ViewModel
import com.stt.android.domain.workout.ActivityType
import com.stt.android.extensions.useSkiMapStyle
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.models.MapSelectionModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class OngoingWorkoutViewModel
@Inject constructor(
    private val mapSelectionModel: MapSelectionModel
) : ViewModel() {

    @JvmField
    var originalMapType: MapType? = null

    @JvmField
    var activityType: ActivityType? = null

    @JvmField
    var forcedMapTypeChange = false

    fun forceSkiMapIfNeeded() {
        if (activityType?.useSkiMapStyle == true) {
            MapTypeHelper.find(MAP_TYPE_SKI)
                ?.let { skiMapType ->
                    forcedMapTypeChange = true
                    mapSelectionModel.selectedMapType = skiMapType
                }
        }
    }

    fun restoreMapTypeIfNeeded() {
        if (forcedMapTypeChange) {
            originalMapType?.let {
                mapSelectionModel.selectedMapType = it
            }
        }
    }
}
