package com.stt.android.home.explore.routes

import android.content.Context
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.workouts.WorkoutHeader

interface RoutePlannerNavigator {
    fun startCreateRouteActivityOrRedirectToLogin(context: Context, currentUserController: CurrentUserController)
    fun startActivityForCreatingRouteFromWorkoutOrRedirectToLogin(
        currentUserController: CurrentUserController,
        context: Context,
        workoutHeader: WorkoutHeader
    )
}
