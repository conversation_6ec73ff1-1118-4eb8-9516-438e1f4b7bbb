package com.stt.android.home.dashboardv2.ui.widgets

import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.stt.android.R
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLMegaBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.AutoSizeText
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWidgetHeader
import com.stt.android.home.dashboardv2.ui.widgets.common.PremiumForeground
import com.stt.android.home.dashboardv2.ui.widgets.common.WidgetText
import com.stt.android.home.dashboardv2.widgets.ProgressWidgetInfo

@Composable
internal fun ProgressWidget(
    widgetInfo: ProgressWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
                .clickable(
                    enabled = !editMode,
                    onClick = onClick,
                    onLongClick = onLongClick,
                )
                .padding(MaterialTheme.spacing.medium)
                .graphicsLayer {
                    if (widgetInfo.premiumRequired && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        renderEffect = RenderEffect.createBlurEffect(
                            10f,
                            10f,
                            Shader.TileMode.CLAMP,
                        ).asComposeRenderEffect()
                    }
                },
        ) {
            val (header, title, subtitle, ctl, atl, tsb) = createRefs()

            CommonWidgetHeader(
                editMode = editMode,
                headerRes = R.string.dashboard_widget_progress_name,
                subheaderText = stringResource(R.string.today),
                iconRes = R.drawable.dashboard_widget_progress,
                colorRes = R.color.dashboard_widget_progress,
                onRemoveClick = onRemoveClick,
                modifier = Modifier.constrainAs(header) {
                    top.linkTo(parent.top)
                },
            )

            if (widgetInfo.title != null) {
                AutoSizeText(
                    text = stringResource(widgetInfo.title),
                    modifier = Modifier.constrainAs(title) {
                        top.linkTo(header.bottom)
                        bottom.linkTo(ctl.top)
                        height = Dimension.percent(0.4f)
                    },
                    alignment = Alignment.CenterStart,
                    maxTextSize = MaterialTheme.typography.bodyXLMegaBold.fontSize,
                    color = widgetInfo.titleColor ?: MaterialTheme.colorScheme.onSurface,
                    style = MaterialTheme.typography.bodyXLMegaBold,
                )
            } else if (widgetInfo.subtitle != null) {
                Text(
                    text = stringResource(widgetInfo.subtitle),
                    modifier = Modifier.constrainAs(subtitle) {
                        top.linkTo(header.bottom)
                        bottom.linkTo(ctl.top)
                    },
                    style = MaterialTheme.typography.body,
                )
            }

            ProgressValue(
                title = R.string.home_dashboard_widgets_progress_ctl_abbrev,
                value = widgetInfo.ctl,
                horizontalAlignment = Alignment.Start,
                modifier = Modifier.constrainAs(ctl) {
                    bottom.linkTo(parent.bottom)
                }
            )

            ProgressValue(
                title = R.string.home_dashboard_widgets_progress_atl_abbrev,
                value = widgetInfo.atl,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.constrainAs(atl) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                }
            )

            ProgressValue(
                title = R.string.home_dashboard_widgets_progress_tsb_abbrev,
                value = widgetInfo.tsb,
                horizontalAlignment = Alignment.End,
                modifier = Modifier.constrainAs(tsb) {
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                }
            )
        }

        if (widgetInfo.premiumRequired) {
            PremiumForeground(
                editMode = editMode,
                onRemoveClick = onRemoveClick,
            )
        }
    }
}

@Composable
private fun ProgressValue(
    @StringRes title: Int,
    value: Int?,
    horizontalAlignment: Alignment.Horizontal,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = horizontalAlignment,
    ) {
        WidgetText(
            text = stringResource(title),
            color = MaterialTheme.colorScheme.mediumGrey,
            style = MaterialTheme.typography.bodySmall,
        )

        WidgetText(
            text = value?.toString() ?: "-",
            style = MaterialTheme.typography.bodyXLargeBold,
        )
    }
}

@Preview
@Composable
private fun ProgressWidgetPreview(
    @PreviewParameter(ProgressWidgetInfoProvider::class) widgetInfo: ProgressWidgetInfo
) {
    M3AppTheme {
        Card(
            modifier = Modifier.size(170.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors().copy(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.lightGrey),
        ) {
            ProgressWidget(
                widgetInfo = widgetInfo,
                editMode = false,
                onClick = {},
                onLongClick = {},
                onRemoveClick = {},
            )
        }
    }
}

private class ProgressWidgetInfoProvider :
    PreviewParameterProvider<ProgressWidgetInfo> {
    override val values: Sequence<ProgressWidgetInfo> = sequenceOf(
        ProgressWidgetInfo(
            premiumRequired = false,
            title = R.string.tss_phase_description_productive_training_title,
            titleColor = Color(0xFFDB315A),
            subtitle = null,
            ctl = 23,
            atl = 11,
            tsb = -20,
        ),
        ProgressWidgetInfo(
            premiumRequired = false,
            title = R.string.tss_phase_description_losing_fitness_or_recovering_title,
            titleColor = Color(0xFFF1ADBD),
            subtitle = null,
            ctl = 23,
            atl = 11,
            tsb = 20,
        ),
        ProgressWidgetInfo(
            premiumRequired = false,
            title = null,
            titleColor = null,
            subtitle = R.string.tss_form_insight_no_activity_data,
            ctl = null,
            atl = null,
            tsb = null,
        ),
        ProgressWidgetInfo(
            premiumRequired = true,
            title = R.string.tss_phase_description_productive_training_title,
            titleColor = Color(0xFFDB315A),
            subtitle = null,
            ctl = 23,
            atl = 11,
            tsb = -20,
        ),
    )
}
