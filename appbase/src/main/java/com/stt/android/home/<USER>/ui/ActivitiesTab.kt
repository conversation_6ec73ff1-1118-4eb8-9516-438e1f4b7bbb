package com.stt.android.home.dashboardv2.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.ActivitiesTabViewModel
import com.stt.android.home.dashboardv2.ui.activities.Explore
import com.stt.android.home.dashboardv2.ui.activities.Sportie
import com.stt.android.home.dashboardv2.ui.activities.Workout
import com.stt.android.newfeed.EmarsyInlineAppViewCard
import com.stt.android.newfeed.ExploreCardData
import com.stt.android.newfeed.FeedEmptyFollowCardData
import com.stt.android.newfeed.SportieCardData
import com.stt.android.newfeed.WelcomeFeedCardData
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.newfeed.WorkoutFeedCardData
import com.stt.android.ui.components.workout.WorkoutShareInfo

@Composable
internal fun ActivitiesTab(
    viewModel: ActivitiesTabViewModel,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    listState: LazyListState,
    modifier: Modifier = Modifier,
) {
    val viewData = viewModel.viewState.collectAsState().value

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(color = MaterialTheme.colorScheme.surface),
    ) {
        FilterTags(
            selectedTag = viewData.selectedFilterTag,
            onTagClick = {
                viewModel.selectFilterTag(it)
                listState.requestScrollToItem(0)
            },
        )

        when (viewData) {
            is ActivitiesTabViewModel.ViewData.Loading -> Loading(viewData)

            is ActivitiesTabViewModel.ViewData.Activities -> Activities(
                viewData = viewData,
                viewEvent = viewEvent,
                onReactionClick = viewModel::reactToWorkout,
                onShareClick = viewModel::shareWorkout,
                onScrolledToTop = viewModel::onScrolledToTop,
                listState = listState,
            )

            is ActivitiesTabViewModel.ViewData.NoActivity -> NoActivity()

            is ActivitiesTabViewModel.ViewData.NoFollowing -> NoFollowing(
                viewEvent = viewEvent,
            )

            is ActivitiesTabViewModel.ViewData.Error -> Error(
                onRetryClick = viewModel::load,
            )
        }
    }
}

@Composable
private fun Loading(
    viewData: ActivitiesTabViewModel.ViewData.Loading,
    modifier: Modifier = Modifier,
) {
    if (!viewData.showLoadingSpinner) {
        return
    }

    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        CircularProgressIndicator(
            modifier = Modifier
                .align(Alignment.Center)
                .size(48.dp),
            color = MaterialTheme.colorScheme.primary,
        )
    }
}

@Composable
private fun Activities(
    viewData: ActivitiesTabViewModel.ViewData.Activities,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onReactionClick: (WorkoutHeader) -> Unit,
    onShareClick: (WorkoutHeader, WorkoutShareInfo) -> Unit,
    onScrolledToTop: () -> Unit,
    listState: LazyListState,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize(),
        state = listState,
        contentPadding = PaddingValues(bottom = MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
    ) {
        items(
            items = viewData.cards,
            key = { feedCardInfo ->
                when (feedCardInfo) {
                    is WorkoutCardInfo -> feedCardInfo.workoutHeader.id

                    // We don't want the key of these two to change, even if the contents are changed.
                    is SportieCardData -> SportieCardData::class.hashCode()
                    is ExploreCardData -> ExploreCardData::class.hashCode()

                    is EmarsyInlineAppViewCard,
                    is FeedEmptyFollowCardData,
                    is WelcomeFeedCardData,
                    is WorkoutFeedCardData -> throw IllegalArgumentException("Not supported card type - $feedCardInfo")
                }
            },
        ) { feedCard ->
            when (feedCard) {
                is WorkoutCardInfo -> Workout(
                    workoutCard = feedCard,
                    viewEvent = viewEvent,
                    onReactionClick = onReactionClick,
                    onShareClick = onShareClick,
                )
                is SportieCardData -> Sportie(
                    sportieCard = feedCard,
                    viewEvent = viewEvent,
                )
                is ExploreCardData -> Explore(
                    exploreCard = feedCard,
                    viewEvent = viewEvent,
                )
                is EmarsyInlineAppViewCard,
                is FeedEmptyFollowCardData,
                is WelcomeFeedCardData,
                is WorkoutFeedCardData -> throw IllegalArgumentException("Not supported card type - $feedCard")
            }
        }
    }

    if (viewData.shouldScrollToTop) {
        listState.requestScrollToItem(0)
        onScrolledToTop()
    }
}

@Composable
private fun NoActivity(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.spacing.medium),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
    ) {
        Image(
            painter = painterResource(R.drawable.ic_empty_other),
            contentDescription = null,
            modifier = Modifier.size(128.dp),
        )

        Text(
            text = stringResource(R.string.no_tracked_activity),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.darkGrey,
        )
    }
}

@Composable
private fun NoFollowing(
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.spacing.medium),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
    ) {
        Image(
            painter = painterResource(R.drawable.ic_find_people_outline),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.xlarge),
        )

        Text(
            text = stringResource(R.string.find_people_tips),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.nearBlack,
            textAlign = TextAlign.Center,
        )

        Button(
            onClick = { viewEvent(DashboardScreenViewEvent.FindPeople) },
            modifier = Modifier
                .fillMaxWidth(),
            shape = RoundedCornerShape(MaterialTheme.spacing.small),
        ) {
            Text(
                text = stringResource(R.string.find_people).uppercase(),
            )
        }
    }
}

@Composable
private fun Error(
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = stringResource(R.string.error_0),
            style = MaterialTheme.typography.bodyLarge,
        )

        PrimaryButton(
            text = stringResource(R.string.retry),
            onClick = onRetryClick,
            modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
        )
    }
}
