package com.stt.android.home.dashboardv2.edit

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.home.dashboardv2.repository.DashboardConfigRepository
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.dashboardv2.widgets.dataloader.WidgetsLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
internal class AddWidgetViewModel @Inject constructor(
    widgetsLoader: WidgetsLoader,
    private val dashboardConfigRepository: DashboardConfigRepository,
) : ViewModel() {
    val viewData: StateFlow<AddWidgetViewData> = widgetsLoader.unselectedWidgets()
        .map { widgets ->
            AddWidgetViewData.Loaded(
                widgets = widgets,
            )
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(stopTimeoutMillis = 5.seconds.inWholeMilliseconds),
            initialValue = AddWidgetViewData.Loading,
        )

    fun addWidget(toAdd: WidgetType) {
        dashboardConfigRepository.addWidget(toAdd)
    }
}
