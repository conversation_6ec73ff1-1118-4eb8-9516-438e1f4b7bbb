package com.stt.android.home.diary.diarycalendar.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.pluralStringResource
import com.stt.android.R
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity
import java.time.LocalDate

@Composable
internal fun DiaryCalendarTitle(
    granularity: Granularity,
    startDate: LocalDate,
    endDate: LocalDate,
    workoutCount: Int,
    onShareClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                text = formatTitle(context, granularity, startDate, endDate),
                style = MaterialTheme.typography.bodyMegaBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = pluralStringResource(R.plurals.exercises_plural, workoutCount, workoutCount),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        TextButton(onClick = onShareClicked) {
            Text(
                text = context.getString(R.string.share),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}
