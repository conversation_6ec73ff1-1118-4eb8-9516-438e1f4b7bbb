package com.stt.android.home.diary.diarycalendar.planner.analytics

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalytics
import timber.log.Timber
import javax.inject.Inject

class AiPlannerAnalytics @Inject constructor(
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val emarsysAnalytics: EmarsysAnalytics,
) {
    fun trackProgramDescriptionScreen(
        programId: String,
        programName: String,
    ) = trackSafe(AnalyticsEvent.AI_PROGRAM_DESCRIPTION_SCREEN) { event ->
        with(AnalyticsProperties()) {
            put(PROGRAM_ID, programId)
            put(PROGRAM_NAME, programName)
            amplitudeAnalyticsTracker.trackEvent(event, this)
        }
    }

    fun trackPlanSurveyStarted(
        planId: String,
        planName: String,
        planDuration: Int,
    ) = trackSafe(AnalyticsEvent.AI_PLAN_SURVEY_STARTED) { event ->
        with(AnalyticsProperties()) {
            put(PLAN_ID, planId)
            put(PLAN_NAME, planName)
            put(PLAN_DURATION, planDuration)
            amplitudeAnalyticsTracker.trackEvent(event, this)
        }
    }

    fun trackPlanSurveySubmitted(
        planId: String,
        planName: String,
        planDuration: Int,
    ) = trackSafe(AnalyticsEvent.AI_PLAN_SURVEY_SUBMITTED) { event ->
        with(AnalyticsProperties()) {
            put(PLAN_ID, planId)
            put(PLAN_NAME, planName)
            put(PLAN_DURATION, planDuration)
            amplitudeAnalyticsTracker.trackEvent(event, this)
            emarsysAnalytics.trackEventWithProperties(event, this.map)
        }
    }

    fun trackMyPlanScreen(
        activePlanId: String? = null,
        activePlanName: String? = null,
        currentPlanWeek: Int? = null,
        planDuration: Int? = null,
    ) = trackSafe(AnalyticsEvent.AI_MY_PLAN_SCREEN) { event ->
        with(AnalyticsProperties()) {
            put(ACTIVE_PLAN_ID, activePlanId ?: "none")
            put(ACTIVE_PLAN_NAME, activePlanName)
            put(CURRENT_PLAN_WEEK, currentPlanWeek)
            put(PLAN_DURATION, planDuration)
            amplitudeAnalyticsTracker.trackEvent(event, this)
            emarsysAnalytics.trackEventWithProperties(event, this.map)
        }
    }

    fun trackTrainingPlanEnded(
        planId: String,
        planName: String,
        week: Int,
    ) = trackSafe(AnalyticsEvent.AI_TRAINING_PLAN_ENDED) { event ->
        with(AnalyticsProperties()) {
            put(PLAN_ID, planId)
            put(PLAN_NAME, planName)
            put(WEEK, week)
            amplitudeAnalyticsTracker.trackEvent(event, this)
            emarsysAnalytics.trackEventWithProperties(event, this.map)
        }
    }

    fun trackPlannedWorkoutDetailsScreen(
        planId: String,
        planName: String,
    ) = trackSafe(AnalyticsEvent.AI_PLANNED_WORKOUT_DETAILS_SCREEN) { event ->
        with(AnalyticsProperties()) {
            put(PLAN_ID, planId)
            put(PLAN_NAME, planName)
            amplitudeAnalyticsTracker.trackEvent(event, this)
        }
    }

    fun trackPlanOverviewScreen(
        planId: String,
        planName: String,
        currentPlanWeek: Int,
        planDuration: Int,
    ) = trackSafe(AnalyticsEvent.AI_PLAN_OVERVIEW_SCREEN) { event ->
        with(AnalyticsProperties()) {
            put(PLAN_ID, planId)
            put(PLAN_NAME, planName)
            put(CURRENT_PLAN_WEEK, currentPlanWeek)
            put(PLAN_DURATION, planDuration)
            amplitudeAnalyticsTracker.trackEvent(event, this)
        }
    }

    private inline fun trackSafe(eventName: String, block: (String) -> Unit) {
        kotlin.runCatching {
            block(eventName)
        }.onFailure {
            Timber.w(it, "Tracking $eventName event failed.")
        }
    }

    companion object {
        private const val PROGRAM_ID = "ProgramID"
        private const val PROGRAM_NAME = "ProgramName"

        private const val PLAN_ID = "PlanID"
        private const val PLAN_NAME = "PlanName"
        private const val PLAN_DURATION = "PlanDurationInWeeks"

        private const val ACTIVE_PLAN_ID = "ActivePlanID"
        private const val ACTIVE_PLAN_NAME = "ActivePlanName"
        private const val CURRENT_PLAN_WEEK = "CurrentPlanWeek"

        private const val WEEK = "Week"
    }
}
