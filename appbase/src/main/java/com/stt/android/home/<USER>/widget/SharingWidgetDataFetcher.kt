package com.stt.android.home.dashboard.widget

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

class SharingWidgetDataFetcher(
    private val widgetDataFetcher: WidgetDataFetcher,
    private val scope: CoroutineScope
) : WidgetDataFetcher {
    private var loadJob: Job? = null
    private val sharedLoadedWidgetDataFlow = MutableSharedFlow<LoadedWidgetData>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    init {
        scope.launch {
            sharedLoadedWidgetDataFlow
                .subscriptionCount
                .collectLatest {
                    if (it == 0) {
                        loadJob?.cancel()
                        loadJob = null
                    }
                }
        }
    }

    override fun widgetDataFlow(): Flow<LoadedWidgetData> {
        if (loadJob == null || loadJob?.isActive == false) {
            loadJob = scope.launch {
                widgetDataFetcher.widgetDataFlow().collect {
                    sharedLoadedWidgetDataFlow.emit(it)
                }
            }
        }

        return sharedLoadedWidgetDataFlow.distinctUntilChanged()
    }
}
