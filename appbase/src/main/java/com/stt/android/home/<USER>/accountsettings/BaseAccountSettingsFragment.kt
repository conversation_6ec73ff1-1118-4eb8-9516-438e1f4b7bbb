package com.stt.android.home.settings.accountsettings

import android.os.Bundle
import android.view.MenuItem
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateFragment2
import com.stt.android.home.settings.deleteaccount.DeleteAccountActivity
import com.stt.android.home.settings.exportmydata.ExportMyDataActivity
import com.stt.android.home.settings.resetpassword.ResetPasswordActivity

abstract class BaseAccountSettingsFragment : ViewStateFragment2<
    AccountSettingsViewState,
    AccountSettingsViewModel,
    >() {

    override val viewModel: AccountSettingsViewModel by viewModels()

    // FragmentAccountSettingsBinding
    override val layoutId = R.layout.fragment_account_settings

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        viewModel.onResetPasswordClick.observeK(this) {
            resetPasswordOnClicked()
        }

        viewModel.onDeleteAccountClick.observeK(this) {
            deleteAccountOnClicked()
        }

        viewModel.onChangeEmailClicked.observeK(this) {
            changeEmailOnClicked(it)
        }

        viewModel.onExportMyDataClicked.observeK(this) {
            activity?.apply {
                startActivity(ExportMyDataActivity.newStartIntent(this))
            }
        }
    }

    protected open fun resetPasswordOnClicked() {
        activity?.apply {
            startActivity(ResetPasswordActivity.newStartIntent(this))
        }
    }

    protected open fun deleteAccountOnClicked() {
        activity?.apply {
            startActivity(DeleteAccountActivity.newStartIntent(this))
        }
    }

    protected abstract fun changeEmailOnClicked(email: String?)

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                activity?.onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onStateChanged(state: ViewState<AccountSettingsViewState?>) {}

    companion object {
        const val TAG = "AccountSettingsFragment"
    }
}
