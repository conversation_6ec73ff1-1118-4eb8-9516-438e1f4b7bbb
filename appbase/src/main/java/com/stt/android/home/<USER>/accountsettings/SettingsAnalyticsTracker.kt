package com.stt.android.home.settings.accountsettings

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import javax.inject.Inject

interface SettingsAnalyticsTracker {
    fun trackEvent(eventName: String)
}

class SettingsAnalyticsTrackerImpl @Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : SettingsAnalyticsTracker {

    override fun trackEvent(eventName: String) {
        emarsysAnalytics.trackEvent(eventName)
        amplitudeAnalyticsTracker.trackEvent(eventName)
    }
}
