package com.stt.android.home

import android.content.Context
import android.content.Intent
import androidx.core.app.TaskStackBuilder
import com.stt.android.domain.diary.models.GraphDataType

interface HomeActivityNavigator {
    fun newStartIntent(context: Context): Intent
    fun newStartIntent(context: Context, fromNewMapNotification: Boolean): Intent
    fun newStartIntent(
        context: Context,
        fromNewMapNotification: Boolean,
        newRoute: <PERSON>olean,
        showHeartRateSetting: Boolean
    ): Intent

    fun newStartIntentToHome(context: Context): Intent
    fun newStartIntentToDiaryWorkoutList(
        context: Context,
        primaryGraphType: GraphDataType? = null,
        secondaryGraphType: GraphDataType? = null
    ): Intent

    fun newStartIntentToDiaryStepsTab(context: Context): Intent
    fun newStartIntentToDiaryCaloriesTab(context: Context): Intent
    fun newStartIntentToDiarySleepTab(context: Context): Intent
    fun newStartIntentToDiaryRecoveryTab(context: Context): Intent
    fun newStartIntentToDiaryProgressTab(context: Context): Intent
    fun newStartIntentToPeopleTab(
        context: Context,
        fromFollowNotification: Boolean,
        showPendingRequests: Boolean,
        showFollowingTab: Boolean
    ): Intent

    fun getTaskStackBuilder(context: Context, intent: Intent): TaskStackBuilder
    fun newStartIntentToExploreTab(
        context: Context,
        showMaps: Boolean,
        analyticsSource: String? = null
    ): Intent

    fun newStartIntentToDiaryCalendar(
        content: Context,
        source: String,
        showActivitiesList: Boolean = false
    ): Intent

    fun newStartIntentToAiPlanner(
        context: Context,
    ): Intent
}
