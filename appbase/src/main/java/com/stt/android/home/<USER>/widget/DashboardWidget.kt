package com.stt.android.home.dashboard.widget

import android.graphics.drawable.Drawable
import android.view.View
import androidx.annotation.DrawableRes
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.stt.android.R
import java.time.LocalDate

/**
 * Helper interface for adding Epoxy props needed by most dashboard widgets.
 * Works only with custom View based Epoxy models
 */
interface DashboardWidget {
    /**
     * Used only if bound view is DisableableDashboardWidgetView
     */
    @set:[ModelProp]
    var displayedAsEnabled: Boolean

    @set:[ModelProp]
    var customizationModeEnabled: <PERSON><PERSON><PERSON>

    @set:[ModelProp]
    var showRemoveButton: Boolean

    /**
     * Even if not used by all models directly, causes diffing to rebind the View
     * when the date changes. Useful if empty or null widget data doesn't trigger
     * the widget with daily data to update otherwise
     */
    @set:[ModelProp]
    var today: LocalDate

    @set:[CallbackProp]
    var onClick: View.OnClickListener?

    @set:[CallbackProp]
    var onLongClick: View.OnLongClickListener?

    @set:[CallbackProp]
    var onRemoveButtonClick: View.OnClickListener?

    fun bindDashboardWidgetView(view: DashboardWidgetView)
}

/**
 * Companion for [DashboardWidget] for binding the common Epoxy props
 */
interface DashboardWidgetView {
    val removeButton: View
    val clickContainer: View
}

/**
 * Companion for [DashboardWidget] for binding the common Epoxy props
 *
 * Variation of [DashboardWidgetView] for widgets that have a separate
 * disabled view state when they have no data
 */
interface DisableableDashboardWidgetView : DashboardWidgetView {
    fun bindDisplayedAsEnabled(enabled: Boolean)
}

/**
 * Helper for implementing common dashboard widget functionality
 *  - Attaches different click handlers (normal, long click and remove button)
 *  - Disables ripple from the click handler when there is no normal click handler
 *  - Shows and hides remove button
 *  - Tells the [DisableableDashboardWidgetView] to switch between enabled and disabled states
 */
class DashboardWidgetDelegate(
    @DrawableRes private val nonClickableBackgroundResId: Int = R.drawable.dashboard_widget_bg_disabled
) : DashboardWidget {
    override var displayedAsEnabled: Boolean = true

    override var customizationModeEnabled: Boolean = false

    override var showRemoveButton: Boolean = false

    override var today: LocalDate = LocalDate.now()

    override var onClick: View.OnClickListener? = null

    override var onLongClick: View.OnLongClickListener? = null

    override var onRemoveButtonClick: View.OnClickListener? = null

    private var initialBindDone: Boolean = false
    private var originalBackground: Drawable? = null
    private var nonClickableBackground: Drawable? = null

    override fun bindDashboardWidgetView(view: DashboardWidgetView) {
        doInitialBindIfNeeded(view)

        view.removeButton.setOnClickListener(onRemoveButtonClick)
        view.removeButton.isVisible = showRemoveButton

        with(view.clickContainer) {
            setOnClickListener(onClick)

            val isClickEnabled = onClick != null
            isClickable = isClickEnabled
            background = if (isClickEnabled) {
                originalBackground
            } else {
                nonClickableBackground
            }

            setOnLongClickListener(onLongClick)
            isLongClickable = onLongClick != null
        }

        (view as? DisableableDashboardWidgetView)?.bindDisplayedAsEnabled(displayedAsEnabled)
    }

    private fun doInitialBindIfNeeded(view: DashboardWidgetView) {
        if (initialBindDone) return

        originalBackground = view.clickContainer.background
        nonClickableBackground =
            ResourcesCompat.getDrawable(
                view.clickContainer.resources,
                nonClickableBackgroundResId,
                null
            )

        initialBindDone = true
    }
}
