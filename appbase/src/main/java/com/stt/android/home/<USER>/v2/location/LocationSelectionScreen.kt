package com.stt.android.home.settings.v2.location

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LocationSelectionScreen(
    onFinish: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: LocationSelectionViewModel = hiltViewModel(),
) {
    BackHandler {
        if (!viewModel.onBackPressed()) {
            onFinish()
        }
    }

    val selectionMode by viewModel.selectionMode.collectAsStateWithLifecycle()
    val countries by viewModel.countries.collectAsStateWithLifecycle()
    val states by viewModel.states.collectAsStateWithLifecycle()
    val selectedCountryCode by viewModel.selectedCountryCode.collectAsStateWithLifecycle()
    val selectedStateCode by viewModel.selectedStateCode.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()

    val title = when (selectionMode) {
        LocationSelectionViewModel.SelectionMode.COUNTRY -> stringResource(R.string.location)
        LocationSelectionViewModel.SelectionMode.STATE -> stringResource(R.string.select_state)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = title.uppercase())
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = {
                            if (!viewModel.onBackPressed()) {
                                onFinish()
                            }
                        },
                        contentDescription = stringResource(R.string.back),
                    )
                },
            )
        },
        modifier = modifier,
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) {
            when (selectionMode) {
                LocationSelectionViewModel.SelectionMode.COUNTRY -> {
                    CountryList(
                        countries = countries,
                        selectedCountryCode = selectedCountryCode,
                        onCountrySelected = { country ->
                            viewModel.selectCountry(country.countryCode)
                            if (country.countryCode != Locale.US.country) {
                                onFinish()
                            }
                        },
                    )
                }

                LocationSelectionViewModel.SelectionMode.STATE -> {
                    StateList(
                        states = states,
                        selectedStateCode = selectedStateCode,
                        onStateSelected = { state ->
                            viewModel.selectState(state.stateCode)
                            onFinish()
                        },
                    )
                }
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
}

@Composable
private fun CountryList(
    countries: List<LocationSelectionViewModel.CountryItem>,
    selectedCountryCode: String,
    onCountrySelected: (LocationSelectionViewModel.CountryItem) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
    ) {
        items(countries, key = { it.countryCode }) { country ->
            CountryListItem(
                country = country,
                isSelected = country.countryCode == selectedCountryCode,
                onClick = { onCountrySelected(country) },
            )
            HorizontalDivider()
        }
    }
}

@Composable
private fun StateList(
    states: List<LocationSelectionViewModel.StateItem>,
    selectedStateCode: String,
    onStateSelected: (LocationSelectionViewModel.StateItem) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
    ) {
        items(states, key = { it.stateCode }) { state ->
            StateListItem(
                state = state,
                isSelected = state.stateCode == selectedStateCode,
                onClick = { onStateSelected(state) }
            )
            HorizontalDivider()
        }
    }
}

@Composable
private fun CountryListItem(
    country: LocationSelectionViewModel.CountryItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickableThrottleFirst { onClick() }
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = country.displayName,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f),
        )

        if (country.hasState) {
            if (isSelected) {
                Text(
                    text = stringResource(R.string.selected),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                )
            }
            Icon(
                painter = painterResource(R.drawable.ic_right_arrow),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        } else {
            RadioButton(
                selected = isSelected,
                onClick = null,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colorScheme.primary,
                    unselectedColor = MaterialTheme.colorScheme.mediumGrey,
                ),
            )
        }
    }
}

@Composable
private fun StateListItem(
    state: LocationSelectionViewModel.StateItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickableThrottleFirst { onClick() }
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = state.displayName,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f),
        )

        RadioButton(
            selected = isSelected,
            onClick = null,
            colors = RadioButtonDefaults.colors(
                selectedColor = MaterialTheme.colorScheme.primary,
                unselectedColor = MaterialTheme.colorScheme.mediumGrey,
            ),
        )
    }
}

@Preview
@Composable
private fun CountryListItemPreview() {
    val countries = listOf(
        LocationSelectionViewModel.CountryItem("Finland", "FI", false),
        LocationSelectionViewModel.CountryItem("England", "EN", false),
        LocationSelectionViewModel.CountryItem("United States", "US", true),
        LocationSelectionViewModel.CountryItem("China", "CN", true),
    )
    M3AppTheme {
        Column {
            countries.forEachIndexed { index, country ->
                CountryListItem(
                    country = country,
                    isSelected = index % 2 == 0,
                    onClick = {},
                )
            }
        }
    }
}
