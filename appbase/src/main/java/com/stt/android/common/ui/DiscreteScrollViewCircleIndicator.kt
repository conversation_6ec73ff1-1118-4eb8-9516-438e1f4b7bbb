package com.stt.android.common.ui

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
import com.yarolegovich.discretescrollview.DiscreteScrollView
import me.relex.circleindicator.CircleIndicator2

class DiscreteScrollViewCircleIndicator @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : CircleIndicator2(context, attrs, defStyleAttr) {
    private var currentScrollView: DiscreteScrollView? = null
    private var currentAdapter: RecyclerView.Adapter<*>? = null
    private var currentItemCount: Int = 0

    private val itemChangeListener =
        DiscreteScrollView.OnItemChangedListener<RecyclerView.ViewHolder> { _, adapterPosition ->
            animatePageSelected(adapterPosition)
        }

    private val adapterObserver = object : AdapterDataObserver() {
        override fun onChanged() {
            val count = currentAdapter?.itemCount
            val current = currentScrollView?.currentItem
            if (count != null && current != null && count != currentItemCount) {
                createIndicators(count, current)
                currentItemCount = count
            }
        }
    }

    /**
     * Start listening to DiscreteScrollView's item changes and changes in it's adapter's
     * item count. If DiscreteScrollView's adapter instance is changed, this should be called again.
     */
    fun setDiscreteScrollView(scrollView: DiscreteScrollView) {
        currentScrollView?.removeItemChangedListener(itemChangeListener)
        currentAdapter?.unregisterAdapterDataObserver(adapterObserver)

        scrollView.adapter?.run {
            createIndicators(itemCount, scrollView.currentItem)
            registerAdapterDataObserver(adapterObserver)
            currentItemCount = itemCount
            currentAdapter = this
        }
        scrollView.addOnItemChangedListener(itemChangeListener)
        currentScrollView = scrollView
    }
}
