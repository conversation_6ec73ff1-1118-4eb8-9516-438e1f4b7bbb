package com.stt.android.utils

import com.stt.android.BuildConfig
import java.util.Locale

object FlavorUtils {
    private const val SUUNTO_FLAVOR = "suunto"
    private const val CHINA_FLAVOR = "china"
    private const val GLOBAL_FLAVOR = "playstore"
    private const val ST_FLAVOR = "sportstracker"

    val isSuuntoApp: Boolean = BuildConfig.FLAVOR_brand == SUUNTO_FLAVOR

    val isSuuntoAppGlobal: Boolean = isSuuntoApp && BuildConfig.FLAVOR_store == GLOBAL_FLAVOR

    val isSuuntoAppChina: Boolean = isSuuntoApp && BuildConfig.FLAVOR_store == CHINA_FLAVOR

    val isSportsTracker : Boolean = BuildConfig.FLAVOR_brand == ST_FLAVOR

    fun isCurrentFlavor(flavorName: String): Boolean = when {
        isSuuntoAppGlobal -> flavorName.lowercase(Locale.US) == "suuntoapp"
        isSuuntoAppChina -> flavorName.lowercase(Locale.US) == "suuntochina"
        isSportsTracker -> flavorName.lowercase(Locale.US) == "sportstracker"
        else -> false
    }
}
