package com.stt.android.utils

import androidx.lifecycle.SavedStateHandle

@Throws(IllegalArgumentException::class)
fun <E : Enum<E>> SavedStateHandle.getEnumExtra(name: String, eClass: Class<E>, defaultEnum: E): E {
    return get<String>(name)?.let {
        java.lang.Enum.valueOf(eClass, it)
    } ?: defaultEnum
}

fun SavedStateHandle.getBooleanExtra(name: String, defaultValue: Boolean): Boolean {
    return get<Boolean>(name) ?: defaultValue
}
