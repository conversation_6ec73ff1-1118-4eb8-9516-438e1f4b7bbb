package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.workouts.extensions.SummaryExtension;
import java.sql.SQLException;

public class DatabaseUpgrade26To27Helper extends DatabaseUpgradeHelper {

    public DatabaseUpgrade26To27Helper(SQLiteDatabase db,
        ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        TableUtils.createTableIfNotExists(connectionSource, SummaryExtension.class);
    }
}
