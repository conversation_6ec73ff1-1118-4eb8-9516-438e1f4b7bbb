package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.ImageInformation;
import java.sql.SQLException;

class DatabaseUpgrade22To23<PERSON><PERSON>per extends DatabaseUpgradeHelper {
    DatabaseUpgrade22To23Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, ImageInformation.TABLE_NAME,
            ImageInformation.DbFields.WIDTH);
        DatabaseHelper.addColumnIfNotExist(db, ImageInformation.TABLE_NAME,
            ImageInformation.DbFields.HEIGHT);
    }
}
