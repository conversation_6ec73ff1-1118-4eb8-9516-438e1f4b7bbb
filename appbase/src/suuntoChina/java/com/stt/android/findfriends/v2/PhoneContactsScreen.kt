package com.stt.android.findfriends.v2

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingDialog
import com.stt.android.compose.widgets.M3SearchBar
import com.stt.android.findfriends.FollowingState
import com.stt.android.findfriends.PhoneContactsData
import com.stt.android.findfriends.PhoneContactsViewModel
import com.stt.android.social.friends.composables.FriendView
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf

@Composable
fun PhoneContactsScreen(
    onBackClick: () -> Unit,
    onInviteFriendsClick: (String) -> Unit,
    getErrorMessageResId: (Int?) -> Int,
    modifier: Modifier = Modifier,
    viewModel: PhoneContactsViewModel = hiltViewModel(),
) {
    LaunchedEffect(Unit) {
        viewModel.loadPhoneContacts()
    }
    val context = LocalContext.current
    val snackbarHostState = remember {
        SnackbarHostState()
    }
    val state by viewModel.phoneContactsStateFlow.collectAsState()
    val loading = state.loading
    val phoneContacts = state.phoneContacts
    val searchedPhoneContacts = state.searchedPhoneContacts
    val keyword = state.keyword
    val showSearch = state.searching

    Scaffold(
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState)
        },
        topBar = {
            if (showSearch) {
                M3SearchBar(
                    query = keyword,
                    onQueryChange = { viewModel.search(it) },
                    placeholder = stringResource(R.string.search_contact_hint),
                    onCancel = { viewModel.cancelSearch() },
                    cancelText = stringResource(R.string.cancel),
                    modifier = Modifier.background(MaterialTheme.colorScheme.surface),
                )
            } else {
                TopBar(
                    onBackClick = onBackClick,
                    onSearchClick = { viewModel.search("") },
                )
            }
        },
        containerColor = MaterialTheme.colorScheme.surface,
        modifier = modifier,
    ) { paddingValues ->
        AnimatedVisibility(visible = loading, exit = fadeOut()) {
            LoadingDialog(
                hintText = stringResource(id = R.string.please_wait),
                progressIndicatorColor = MaterialTheme.colorScheme.primary,
            )
        }
        AnimatedVisibility(
            visible = !loading,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            PhoneContactsContent(
                phoneContacts = phoneContacts,
                showSearchResult = showSearch,
                keyword = keyword,
                searchedPhoneContacts = searchedPhoneContacts,
                onFollowStateClick = {
                    when (it.followingState) {
                        FollowingState.Unregistered -> {
                            onInviteFriendsClick(it.phones.first())
                        }
                        else -> {
                            viewModel.handleFollowState(it)
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    )
                    .padding(paddingValues),
            )

            if (viewModel.errorException.value != null) {
                LaunchedEffect(viewModel.errorException.value) {
                    val errorMessageId = getErrorMessageResId(viewModel.errorException.value?.code)
                    snackbarHostState.showSnackbar(
                        context.getString(
                            if (errorMessageId == 0) {
                                R.string.error_generic
                            } else {
                                errorMessageId
                            }
                        )
                    )
                }
            }
        }

    }
}

@Composable
fun PhoneContactsContent(
    phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    showSearchResult: Boolean,
    keyword: String,
    searchedPhoneContacts: ImmutableList<PhoneContactsData>,
    onFollowStateClick: (PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        if (showSearchResult) {
            if (searchedPhoneContacts.isEmpty()) {
                if (keyword.isNotBlank()) {
                    EmptyContent(
                        modifier = Modifier.fillMaxWidth(),
                    )
                }
            } else {
                SearchedListPhoneContact(
                    phoneContacts = searchedPhoneContacts,
                    onFollowStateClick = onFollowStateClick,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        } else {
            if (phoneContacts.isEmpty()) {
                EmptyContent(
                    modifier = Modifier.fillMaxWidth(),
                )
            } else {
                ListPhoneContact(
                    phoneContacts = phoneContacts,
                    onFollowStateClick = onFollowStateClick,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
private fun EmptyContent(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 200.dp),
        contentAlignment = Alignment.TopCenter,
    ) {
        Text(
            text = stringResource(R.string.search_phone_contacts_empty),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.secondary,
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TopBar(
    onBackClick: () -> Unit,
    onSearchClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                text = stringResource(R.string.phone_contact_title).uppercase(),
                style = MaterialTheme.typography.header,
            )
        },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = onBackClick) {
                Icon(
                    painter = painterResource(R.drawable.all_ic_arrow_left),
                    contentDescription = stringResource(R.string.back),
                )
            }
        },
        actions = {
            IconButton(
                onClick = onSearchClick,
            ) {
                Icon(
                    painter = painterResource(com.stt.android.compose.ui.R.drawable.search_outline),
                    contentDescription = stringResource(R.string.search),
                )
            }
        }
    )
}

@Composable
private fun ListPhoneContact(
    phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    onFollowStateClick: (item: PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
) {
    val context = LocalContext.current
    LazyColumn(
        modifier = modifier,
        state = lazyListState
    ) {
        phoneContacts.forEach { (key, value) ->
            stickyHeader {
                Text(
                    text = key,
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.secondaryContainer)
                        .padding(MaterialTheme.spacing.medium)
                        .fillMaxWidth(),
                    style = MaterialTheme.typography.body,
                )
            }
            items(value) {
                FriendView(
                    friend = it.toFriend(context),
                    onClick = {},
                    onStatusClick = {
                        onFollowStateClick(it)
                    },
                )
            }
        }
    }
}

@Composable
private fun SearchedListPhoneContact(
    phoneContacts: ImmutableList<PhoneContactsData>,
    onFollowStateClick: (item: PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
) {
    val context = LocalContext.current
    LazyColumn(
        modifier = modifier,
        state = lazyListState
    ) {
        items(phoneContacts) {
            FriendView(
                friend = it.toFriend(context),
                onClick = {},
                onStatusClick = {
                    onFollowStateClick(it)
                },
            )
        }
    }
}

@Preview
@Composable
private fun PhoneContactsContentPreview() {
    val data = persistentMapOf(
        "A" to persistentListOf(PhoneContactsData("Asia", persistentListOf("1369622589"))),
        "B" to persistentListOf(
            PhoneContactsData("Ben", persistentListOf("1369622584")),
            PhoneContactsData(
                "Ben1",
                persistentListOf("1369622584"),
                suuntoName = "ben1",
                userName = "ben1",
                followingState = FollowingState.Followed
            ),
            PhoneContactsData(
                "Ben2",
                persistentListOf("1369622584"),
                suuntoName = "ben2",
                userName = "ben2",
                followingState = FollowingState.Requested
            ),
            PhoneContactsData(
                "Ben3",
                persistentListOf("1369622584"),
                suuntoName = "ben3",
                userName = "ben3",
                followingState = FollowingState.Unfollowed
            ),
            PhoneContactsData(
                "Ben4",
                persistentListOf("1369622584"),
                suuntoName = "ben4",
                userName = "ben4",
                followingState = FollowingState.Friends
            ),
        )
    )
    M3AppTheme {
        PhoneContactsContent(data, true, "xx", persistentListOf(), {})
    }
}

