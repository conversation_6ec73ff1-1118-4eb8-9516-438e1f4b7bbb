<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="androidx.core.content.ContextCompat" />
        <import type="android.view.View"/>

        <variable
            name="title"
            type="int"/>

        <variable
            name="description"
            type="Integer"/>
        <variable
            name="raiseElevation"
            type="boolean"/>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?suuntoBackground"
        android:elevation="@{ raiseElevation ? @dimen/watch_updates_list_item_raised_elevation : @dimen/watch_updates_list_item_no_elevation }"
        android:orientation="vertical"
        android:paddingBottom="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_medium">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:text="@{context.getString(title)}"
            android:textAllCaps="true"
            tools:text="Applications"
            style="@style/Body.Small.Bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:lineSpacingMultiplier="1.3"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_small"
            android:text='@{description != null ? context.getString(description) : ""}'
            android:visibility="@{description != null ? View.VISIBLE : View.GONE}"
            tools:text="Choose the apps whose notifications you want to see on your watch. "
            style="@style/Body.Small" />

    </LinearLayout>

</layout>
