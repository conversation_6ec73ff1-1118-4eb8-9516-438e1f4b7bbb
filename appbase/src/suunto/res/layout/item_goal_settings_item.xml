<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="item"
            type="com.stt.android.home.settings.goalsettings.GoalSettingsItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/diary_item_height"
        android:layout_marginBottom="@dimen/size_divider"
        style="@style/FeedCard"
        android:elevation="0dp">

        <View
            android:id="@+id/ripple_effect_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <include
            layout="@layout/goal_wheel"
            android:id="@+id/goal_setting_goal_wheel"
            android:layout_width="@dimen/activity_data_goal_wheel_small"
            android:layout_height="@dimen/activity_data_goal_wheel_small"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            app:item="@{item.goalWheel}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/goal_item_header"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/goal_item_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:text="@{item.header}"
            android:textAllCaps="false"
            android:textColor="@{item.enabled ? @color/black : @color/light_gray}"
            app:layout_constraintBottom_toTopOf="@id/goal_item_sub_header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/goal_setting_goal_wheel"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_goneMarginBottom="@dimen/size_spacing_medium"
            tools:text="Daily sleep goal"
            style="@style/HeaderLabel.Medium" />

        <TextView
            android:id="@+id/goal_item_sub_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:text="@{item.subHeader}"
            android:textColor="@{item.enabled ? @color/black : @color/light_gray}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/goal_item_header"
            app:layout_constraintStart_toStartOf="@id/goal_item_header"
            app:layout_constraintTop_toBottomOf="@id/goal_item_header"
            tools:text="8 h 00 min"
            style="@style/Body.Medium" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
