package com.stt.android.di.recovery

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.data.Local
import com.stt.android.data.recovery.RecoveryDataLocalDataSource
import com.stt.android.data.recovery.RecoveryRemoteSyncJob
import com.stt.android.data.recovery.RoomRecoveryDataLocalDataSource
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.data.source.local.recovery.RecoveryDataDao
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.multibindings.IntoMap

@Module
abstract class RecoveryDataModule {

    @Binds
    @IntoMap
    @WorkerKey(RecoveryRemoteSyncJob::class)
    abstract fun bindRecoveryRemoteFactory(factory: RecoveryRemoteSyncJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @Local
    abstract fun bindRecoveryLocalDataSource(
        roomRecoveryDataLocalDataSource: RoomRecoveryDataLocalDataSource
    ): RecoveryDataLocalDataSource

    companion object {
        @Provides
        fun provideRecoveryDataDao(daoFactory: DaoFactory): RecoveryDataDao {
            return daoFactory.recoveryDataDao
        }
    }
}
