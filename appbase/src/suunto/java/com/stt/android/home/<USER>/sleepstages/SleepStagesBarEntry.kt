package com.stt.android.home.dayview.sleepstages

import com.github.mikephil.charting.data.BarEntry

/**
 * sleep stage graph don't need y value ,so set the default value = 0
 * bar height is fixed, the position of yAxis is determined by stageType. stageType >=0
 * xEnd - xStart is the width of bar
 */
data class SleepStageBarEntry(
    val xStart: Float,
    val xEnd: Float,
    val stageType: SleepStageType
) : BarEntry(xStart, 0f)

sealed class SleepStageType {
    open val type = 0

    object Deep : SleepStageType() {
        override val type = 3
    }

    object Core : SleepStageType() {
        override val type = 2
    }

    object REM : SleepStageType() {
        override val type = 1
    }

    object Awake : SleepStageType() {
        override val type = 0
    }
}
