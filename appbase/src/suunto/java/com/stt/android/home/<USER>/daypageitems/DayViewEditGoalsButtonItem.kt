package com.stt.android.home.dayview.daypageitems

import android.view.View
import com.stt.android.R
import com.stt.android.common.ui.ClickableItem
import com.stt.android.databinding.ItemDayViewEditGoalsButtonBinding

data class DayViewEditGoalsButtonItem(
    private val onClick: () -> Unit /* onClick must be a method reference and not a lambda so that equals()
     * works correctly when GroupAdapter is diff'ing the list contents */
) : ClickableItem<ItemDayViewEditGoalsButtonBinding>() {
    override fun getLayout() = R.layout.item_day_view_edit_goals_button

    override fun getId() = DayPageItemIds.DAY_PAGE_EDIT_GOALS_ITEM_ID

    override fun onClick(v: View?) = onClick()
}
