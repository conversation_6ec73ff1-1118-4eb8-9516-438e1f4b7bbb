package com.stt.android.home.settings.zones

object ZonesDescriptionPath {
    const val ZONES_DESCRIPTION = "ZONES_DESCRIPTION"
    const val ZONES_DEFINE_DESCRIPTION = "ZONES_DEFINE_DESCRIPTION"
}

enum class ZonesDescriptionType(val path: String){
    HR_INTENSITY_ZONES(ZonesDescriptionPath.ZONES_DESCRIPTION),
    HR_ZONES(ZonesDescriptionPath.ZONES_DESCRIPTION),
    MAX_HR_ZONES(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION),
    HR_RESERVE_ZONE(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION),
    LACTATE_THRESHOLD_HR_ZONES(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION),
    RUNNING_PACE_ZONES(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION),
    RUNNING_POWER_ZONES(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION),
    CYCLING_POWER_ZONES(ZonesDescriptionPath.ZONES_DEFINE_DESCRIPTION)
}
