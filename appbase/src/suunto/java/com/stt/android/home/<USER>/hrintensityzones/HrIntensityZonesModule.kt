package com.stt.android.home.settings.hrintensityzones

import com.stt.android.di.ConnectivityModule
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesProvider
import dagger.Binds
import dagger.Module

@Module
@ConnectivityModule
abstract class HrIntensityZonesModule {

    @Binds
    abstract fun bindHrIntensityZonesProvider(hrIntensityZonesProviderImpl: HrIntensityZonesProviderImpl): HrIntensityZonesProvider
}
