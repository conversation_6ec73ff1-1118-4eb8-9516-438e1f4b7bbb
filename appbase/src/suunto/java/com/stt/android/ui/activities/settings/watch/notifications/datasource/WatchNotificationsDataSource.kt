package com.stt.android.ui.activities.settings.watch.notifications.datasource

import com.stt.android.watch.SuuntoWatchModel
import javax.inject.Inject

class WatchNotificationsDataSource @Inject constructor(
    private val watchModel: SuuntoWatchModel
) : NotificationsDataSource {

    override suspend fun setNotificationsEnabled(enabled: Boolean) {
        watchModel.setNotificationEnabled(enabled)
    }

    override suspend fun fetchNotificationsEnabled() = watchModel.fetchNotificationEnabled()

    override suspend fun setNotificationCategoryEnabled(
        call: <PERSON>ole<PERSON>,
        sms: <PERSON><PERSON><PERSON>,
        application: <PERSON>olean
    ) {
        watchModel.setNotificationCategoryEnabled(call, sms, application)
    }
}
