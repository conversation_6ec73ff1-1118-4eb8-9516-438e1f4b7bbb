package com.stt.android.watch.pair.devicelist

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class PairingHelpDeviceListModule {

    @Binds
    abstract fun bindController(controller: PairingHelpDeviceListController): ViewStateEpoxyController<PairingHelpDeviceListContainer>
}
