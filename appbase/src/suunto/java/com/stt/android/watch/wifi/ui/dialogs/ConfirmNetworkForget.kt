package com.stt.android.watch.wifi.ui.dialogs

import androidx.compose.material.AlertDialog
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.core.utils.EventThrottler
import com.stt.android.core.utils.onClick
import java.util.Locale

@Composable
fun ConfirmForgetNetwork(
    onConfirmRequest: () -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier
) {
    AppTheme {
        val eventThrottler = remember { EventThrottler() }

        AlertDialog(
            modifier = modifier,
            onDismissRequest = onDismissRequest,
            title = {
                Text(
                    text = stringResource(id = R.string.wifi_details_forget_network),
                    style = MaterialTheme.typography.bodyXLargeBold,
                    color = MaterialTheme.colors.onSurface
                )
            },
            text = {
                Text(
                    text = stringResource(id = R.string.wifi_details_forget_network_dialog_text),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.onSurface
                )
            },
            confirmButton = {
                TextButton(
                    onClick = eventThrottler.onClick {
                        onConfirmRequest()
                        onDismissRequest()
                    }
                ) {
                    Text(text = stringResource(id = R.string.yes).uppercase(Locale.getDefault()))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = eventThrottler.onClick(onDismissRequest)
                ) {
                    Text(
                        text = stringResource(id = R.string.no).uppercase(Locale.getDefault())
                    )
                }
            }
        )
    }
}

@Preview
@Composable
private fun ConfirmForgetNetworkPreview() {
    ConfirmForgetNetwork(
        onConfirmRequest = { },
        onDismissRequest = { }
    )
}
