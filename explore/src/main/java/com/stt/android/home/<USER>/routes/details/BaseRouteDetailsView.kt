package com.stt.android.home.explore.routes.details

import androidx.annotation.StringRes
import androidx.lifecycle.LifecycleCoroutineScope
import com.soy.algorithms.ascent.VerticalDelta
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.domain.Point
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.routes.planner.waypoints.PointsWithDistances
import com.stt.android.views.MVPView

interface BaseRouteDetailsView : MVPView {
    fun setFollowRouteVisibility(visible: Boolean)
    fun onFollowRouteSelected(routeId: String?)
    fun onFollowRouteSelectedWhileTracking(
        routeId: String?,
        activityType: ActivityType?
    )

    fun onRouteDeleted(success: Boolean)
    fun onFindWaypointsCompleted(waypoints: PointsWithDistances)

    fun showEditSpeedDialog(initialValue: String, @StringRes unitRes: Int)
    fun showSpeedUpdatedFailed()

    fun getLifecycleScope(): LifecycleCoroutineScope

    fun updateAllDataDisplayed(distanceAndDuration: Pair<Double, Long>, verticalDelta: VerticalDelta?)

    fun highlightRouteByIndex(climbGuidance: ClimbGuidance, segmentIndex: Int, pointIndex: Int)

    fun highlightRouteByIndex(highlightPoints: List<Point>, translucentPoints: List<Point>)
}
