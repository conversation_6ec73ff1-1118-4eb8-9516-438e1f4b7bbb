package com.stt.android.home.explore.searchv2

import androidx.compose.material.AlertDialog
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.core.utils.EventThrottler
import com.stt.android.core.utils.onClick
import com.stt.android.home.explore.R
import java.util.Locale

@Composable
fun MapSearchDeleteDialog(
    onConfirmRequest: () -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val eventThrottler = remember { EventThrottler() }

    M3AppTheme {
        AlertDialog(
            modifier = modifier,
            onDismissRequest = onDismissRequest,
            title = {
                Text(
                    text = stringResource(id = R.string.map_search_delete_dialog_title),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            },
            text = {
                Text(
                    text = stringResource(id = R.string.map_search_delete_dialog_content),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.onSurface
                )
            },
            confirmButton = {
                TextButton(
                    onClick = eventThrottler.onClick {
                        onConfirmRequest()
                        onDismissRequest()
                    }
                ) {
                    Text(
                        text = stringResource(id = com.stt.android.R.string.yes).uppercase(
                            Locale.getDefault()
                        ),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = eventThrottler.onClick(onDismissRequest)
                ) {
                    Text(
                        text = stringResource(id = com.stt.android.R.string.cancel).uppercase(Locale.getDefault()),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        )
    }
}
