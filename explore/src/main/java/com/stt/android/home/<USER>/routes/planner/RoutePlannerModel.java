package com.stt.android.home.explore.routes.planner;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.gojuno.koptional.None;
import com.gojuno.koptional.Optional;
import com.google.android.gms.maps.model.LatLng;
import com.soy.algorithms.ascent.VerticalDelta;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.domain.Point;
import com.stt.android.domain.routes.GetRouteUseCase;
import com.stt.android.domain.routes.GetTopRouteUseCase;
import com.stt.android.domain.routes.ImportRouteUseCase;
import com.stt.android.domain.routes.Route;
import static com.stt.android.domain.routes.Route.DEFAULT_AVG_SPEED_IN_MS;
import com.stt.android.domain.routes.RouteProducer;
import com.stt.android.domain.routes.RouteSegment;
import com.stt.android.domain.routes.RouteSimplifierKt;
import com.stt.android.domain.routes.RouteTool;
import com.stt.android.domain.routes.RouteVerticalDeltaCalc;
import com.stt.android.domain.routes.RouteVisibility;
import com.stt.android.domain.routes.RouteWatchSyncState;
import com.stt.android.domain.routes.TopRoute;
import com.stt.android.domain.routes.WorkoutToRouteUseCase;
import static com.stt.android.domain.user.MeasurementUnitKt.KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.home.explore.routes.RouteAnalytics;
import com.stt.android.home.explore.routes.RouteUtils;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointAction;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointsActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointAction;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.MovePointResult;
import com.stt.android.home.explore.routes.planner.actionresult.ReverseRouteResult;
import com.stt.android.home.explore.routes.planner.waypoints.NearestPointResult;
import com.stt.android.home.explore.routes.planner.waypoints.WaypointUtils;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType;
import com.stt.android.maps.SuuntoCameraOptions;
import com.stt.android.routes.PointExtKt;
import com.stt.android.ui.controllers.WorkoutDataLoaderController;
import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Deque;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import kotlin.collections.CollectionsKt;
import timber.log.Timber;

/**
 * This class is not thread-safe!! Be careful, specially when adding and removing segments!
 */
public class RoutePlannerModel {

    private static final double INITIAL_ROUTE_SIMPLIFICATION_DISTANCE = 4.0; // meters
    private static final int ROUTE_SIMPLIFICATION_THRESHOLD = 15000; // number of points

    @SuppressWarnings("WeakerAccess")
    private final ArrayList<RouteSegment> segments = new ArrayList<>();
    /**
     * Stack that holds actions done for route, used for undo feature
     */
    @SuppressWarnings("WeakerAccess")
    final Deque<RoutePlannerOperation> actionStack = new ArrayDeque<>();
    /**
     * Routing engine used in this model when creating segments
     */
    @NonNull
    private final RoutingApiModel routingApiModel;
    @NonNull
    private final CurrentUserController currentUserController;
    private String routeId;
    @Nullable
    private SuuntoCameraOptions initialCameraPosition;
    @Nullable
    private LatLng initialRouteStartPoint;
    @Nullable
    private LatLng initialRouteEndPoint;
    @Nullable
    private RoutingMode initialRoutingMode;
    private final ImportRouteUseCase importRouteUseCase;
    private final WorkoutToRouteUseCase workoutToRouteUseCase;
    private final GetRouteUseCase getRouteUseCase;
    private final GetTopRouteUseCase getTopRouteUseCase;
    private final RouteAnalytics routeAnalytics;
    private final WorkoutDataLoaderController workoutDataLoaderController;

    @SuppressWarnings("WeakerAccess")
    @Nullable
    Route originalRoute;
    @Nullable
    Route routeInProgress;
    @Nullable
    String routeInProgressId;
    @Nullable
    LatLng routeInProgressStartPoint;
    @Nullable
    private ImportFileInfo importFileInfo;
    private WorkoutHeader workoutHeader;
    /**
     * Route distance in meters.
     */
    private double distance;

    /**
     * Route ascent in meters.
     */
    @Nullable
    private VerticalDelta verticalDelta;

    /**
     * Speed (m/s) that the user has selected in order to estimate the route total time.
     */
    private double avgSpeed = DEFAULT_AVG_SPEED_IN_MS;
    @Nullable
    private LatLng startPoint;
    @Nullable
    private LatLng pendingWaypoint;
    private List<ActivityType> activityTypes;
    @NonNull
    private String routeName = "";
    /**
     * If we have a previous route (i.e. {@link #originalRoute} is not null) we create a new one if
     * the new route name is different (see {@link #updateRouteName(String)}). If the new route name
     * is the same as original one then we will update the original route.
     */
    private boolean createNewRoute = true;
    private boolean fetchingRoute = false;
    private boolean isAlreadyInitializedFromHeader = false;
    boolean watchEnabled = true;
    boolean turnByTurnEnabled;
    int countImportedWaypointsIgnored = 0;

    /**
     * Whether a replication operation is performed
     */
    private boolean copyRoute = false;

    private boolean watchRouteListFull = false;

    @Nullable
    RouteProducer routeProducer;

    @Nullable
    String externalUrl;

    private final RouteTool routeTool;

    @Nullable
    private String topRouteId;

    @Nullable
    private Integer topRouteActivityType;

    @Inject
    public RoutePlannerModel(@NonNull RoutingApiModel routingApiModel,
        @NonNull CurrentUserController currentUserController,
        @NonNull ImportRouteUseCase importRouteUseCase,
        @NonNull WorkoutToRouteUseCase workoutToRouteUseCase,
        GetRouteUseCase getRouteUseCase,
        GetTopRouteUseCase getTopRouteUseCase,
        RouteAnalytics routeAnalytics,
        WorkoutDataLoaderController workoutDataLoaderController,
        RouteTool routeTool) {
        this.routingApiModel = routingApiModel;
        this.currentUserController = currentUserController;
        this.importRouteUseCase = importRouteUseCase;
        this.workoutToRouteUseCase = workoutToRouteUseCase;
        this.getRouteUseCase = getRouteUseCase;
        this.getTopRouteUseCase = getTopRouteUseCase;
        this.routeAnalytics = routeAnalytics;
        this.workoutDataLoaderController = workoutDataLoaderController;
        this.routeTool = routeTool;
    }

    public void init(
        @Nullable String routeId,
        @Nullable SuuntoCameraOptions initialCameraPosition,
        @Nullable LatLng initialRouteStartPoint,
        @Nullable LatLng initialRouteEndPoint,
        @Nullable RoutingMode initialRoutingMode,
        @Nullable ImportFileInfo importFileInfo,
        @Nullable WorkoutHeader workoutHeader,
        @Nullable Boolean copyRoute,
        @Nullable Boolean watchRouteListFull,
        @Nullable String topRouteId,
        @Nullable Integer topRouteActivityType
    ) {
        this.routeId = routeId;
        this.initialCameraPosition = initialCameraPosition;
        this.initialRouteStartPoint = initialRouteStartPoint;
        this.initialRouteEndPoint = initialRouteEndPoint;
        this.initialRoutingMode = initialRoutingMode;
        this.importFileInfo = importFileInfo;
        this.workoutHeader = workoutHeader;
        this.copyRoute = Boolean.TRUE.equals(copyRoute);
        this.watchRouteListFull = Boolean.TRUE.equals(watchRouteListFull);
        this.topRouteId = topRouteId;
        this.topRouteActivityType = topRouteActivityType;
    }

    public static double getDefaultSpeed(@NonNull RoutingMode routingMode) {
        switch (routingMode) {
            case BIKE:
                return 17 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
            case RACING_BIKE:
                return 24 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
            case MTB:
                return 13 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
            case FOOT:
                return 5 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
            default:
                return DEFAULT_AVG_SPEED_IN_MS;
        }
    }

    @Nullable
    public SuuntoCameraOptions getInitialCameraPosition() {
        return initialCameraPosition;
    }

    @NonNull
    public Completable initializeRoute() {
        Function<Route, Completable> onRouteLoadedCompletable = route -> {
            Timber.d("onRouteLoadedCompletable() called [ route = %s ]", route);
            boolean isSavedRoute = !route.isInProgress();
            updateActivities(RouteUtils.activityIdsToActivityTypeList(route));
            updateAvgSpeed(route.getAverageSpeed());
            removeAllSegments();
            addStartPoint(route.getStartPoint().getLatitude(),
                route.getStartPoint().getLongitude());
            watchEnabled = isImportedRoute() || isWorkoutToRoute() || route.getWatchEnabled();
            List<RouteSegment> segmentsWithSanitizedPosition =
                RouteUtils.sanitizeRouteSegmentPositionValues(route.getKey(), route.getSegments());
            List<RouteSegment> connectedSegments =
                RouteUtils.connectSegmentsIfNeeded(segmentsWithSanitizedPosition);
            List<RouteSegment> splitSegments = splitAtWaypointsAndAddSegments(connectedSegments);
            if (isSavedRoute) {
                originalRoute = route.copyWithSegments(splitSegments);
                updateRouteName(route.getName());
                routeAnalytics.trackRoutePlanningEditRoute(route);
            } else {
                routeInProgress = route;
            }
            routeProducer = route.getProducer();
            externalUrl = route.getExternalUrl();
            return Completable.complete();
        };

        Completable showRouteInProgress;
        if (routeInProgressId != null && !TextUtils.isEmpty(routeInProgressId)) {
            showRouteInProgress = getRouteUseCase.getRouteRx(routeInProgressId)
                .flatMapCompletable(onRouteLoadedCompletable);
        } else {
            showRouteInProgress = Completable.complete();
        }

        if (isWorkoutToRoute() && !isAlreadyInitializedFromHeader) { // Save workout to route
            isAlreadyInitializedFromHeader = true;
            Timber.d("Converting workout to route");
            if (workoutHeader == null) {
                return Completable.error(new IllegalArgumentException(
                    "Workout header is null, cannot convert route"));
            }

            return workoutDataLoaderController.loadWorkoutDataObs(workoutHeader)
                .flatMapObservable((workoutIdAndData) -> {
                    WorkoutData workoutData = workoutIdAndData.second;
                    if (workoutData == null || workoutData.getRoutePoints() == null) {
                        return Observable.error(new IllegalArgumentException(
                            "Missing route points from workout"));
                    }
                    return Observable.fromIterable(CollectionsKt.filterNotNull(
                        workoutData.getRoutePoints()));
                })
                .map(geoPoint -> new Point(
                    geoPoint.getLongitude(),
                    geoPoint.getLatitude(),
                    geoPoint.hasAltitude() ? geoPoint.getAltitude() : null))
                .toList()
                .flatMap(convertedLatLngs -> workoutToRouteUseCase.convertWorkoutToRoute(
                    workoutHeader.getActivityType().getId(),
                    convertedLatLngs,
                    workoutHeader.getTotalAscent(),
                    currentUserController.getUsername(),
                    ""))
                .flatMapCompletable(onRouteLoadedCompletable).andThen(showRouteInProgress);
        } else if (isImportedRoute()) { // GPX file import
            Timber.d("Importing route");
            // Analytics event must be triggered before GPX handling is started
            this.routeAnalytics.trackRouteImport(importFileInfo.getButtonImport());
            return importRouteUseCase.convertToRoute(importFileInfo.getFileUri(),
                    currentUserController.getUsername(), importFileInfo.getFileName())
                .flatMapCompletable(result -> {
                    countImportedWaypointsIgnored = result.getCountWaypointsIgnored();
                    return onRouteLoadedCompletable.apply(result.getRoute());
                }).andThen(showRouteInProgress);
        } else if (!TextUtils.isEmpty(topRouteId) && topRouteActivityType != null) {
            Maybe<Route> getTopRoute = getTopRouteUseCase.getTopRouteRx(topRouteId, topRouteActivityType)
                .map(TopRoute::copyAsRouteForEditing);
            return getTopRoute.flatMapCompletable(onRouteLoadedCompletable)
                .andThen(showRouteInProgress);
        } else if (TextUtils.isEmpty(routeInProgressId) && (TextUtils.isEmpty(routeId)
            || originalRoute != null)) { // Creating a new route
            Timber.d("Creating new route");
            return addInitialRoutePoints();
        } else if (!TextUtils.isEmpty(
            routeId)) { // Updating the existing route or continue editing restored route
            Timber.d("Edit existing route");
            Maybe<Route> getRoute = getRouteUseCase.getRouteRx(routeId);
            if (copyRoute) { // If copy operation, reset route id、key...
                getRoute = getRoute.map(
                    route -> route.copyAsNew(route.getWatchEnabled() && !watchRouteListFull));
            }
            return getRoute.flatMapCompletable(onRouteLoadedCompletable)
                .andThen(showRouteInProgress);
        } else {
            return showRouteInProgress;
        }
    }

    public void setMaxDistanceFromRoute(long maxDistanceFromRoute) {
        routingApiModel.setMaxDistanceFromRoute(maxDistanceFromRoute);
    }

    public void addStartPoint(double latitude, double longitude) {
        this.startPoint = new LatLng(latitude, longitude);
        actionStack.push(AddStartPointOperation.INSTANCE);
    }

    public void addPendingWaypoint(LatLng latLng) {
        this.pendingWaypoint = latLng;
    }

    @Nullable
    public LatLng getStartPoint() {
        return startPoint;
    }

    @Nullable
    public LatLng getPendingWaypoint() {
        return pendingWaypoint;
    }

    public List<ActivityType> getActivityTypes() {
        return activityTypes;
    }

    /**
     * This is used only when we have error moving starting point and need to fallback to original
     * location
     */
    public void setStartPoint(@Nullable LatLng startPoint) {
        this.startPoint = startPoint;
    }

    /**
     * @return an {@link Observable} that will emit a list of new route segments based on the
     * current {@link #routingApiModel routing engine} and {@link RoutingMode RoutingMode} or empty
     * if there's no need to add any segments (e.g. start and end points are the same)
     */
    public Single<List<RouteSegment>> appendRoutePoint(double latitude, double longitude,
        @NonNull RoutingMode mode) {
        if (startPoint == null) {
            throw new IllegalStateException("Start point not defined. Please call addStartPoint");
        }

        LatLng endPoint = new LatLng(latitude, longitude);

        // Let's figure the start point for this segment
        LatLng startPoint;
        int segmentPosition = segments.size();
        if (segmentPosition == 0) {
            // It's the first segment so use the route start point
            startPoint = this.startPoint;
        } else {
            // Use the last segment end point as start
            Point point = segments.get(segmentPosition - 1).getEndPoint();
            startPoint = new LatLng(point.getLatitude(), point.getLongitude());
        }

        // Don 't do anything if the new end point is the same as either start of the route or
        // the end of the previous segment (here startPoint)
        if (startPoint.equals(endPoint)) {
            return Single.just(new ArrayList<>());
        }

        List<Point> prevRoutePoints = segmentPosition > 0
            ? segments.get(segmentPosition - 1).getRoutePoints()
            : null;
        Point previousRouteEnd = prevRoutePoints != null && prevRoutePoints.size() > 1
            ? prevRoutePoints.get(prevRoutePoints.size() - 1)
            : null;
        return createAndAddUpdateSegments(segmentPosition, mode, startPoint, endPoint,
            previousRouteEnd).doOnSuccess(
            routeSegments -> actionStack.push(new AppendSegmentsOperation(routeSegments)));
    }

    public Maybe<AddWaypointsActionResult> addWaypoints(
        @NonNull List<NearestPointResult> results,
        @Nullable String waypointName,
        @Nullable WaypointType waypointType,
        @Nullable String waypointDescription
    ) {
        return Maybe.fromCallable(
            () -> {
                AddWaypointsActionResult actionResults = WaypointUtils.addWaypoints(
                    segments, results,
                    waypointName, waypointType, waypointDescription
                );
                for (AddWaypointAction action : actionResults.getActions()) {
                    for (int i = segments.size() - 1; i >= 0; i--) {
                        final RouteSegment segment = segments.get(i);
                        if (segment.equalsIgnorePosition(action.getOriginalSegment())) {
                            segments.remove(i);
                            // Mind the order, add second first, first second
                            segments.add(i, action.getSecondSegment());
                            segments.add(i, action.getFirstSegment());
                            break;
                        }
                    }
                }
                updateSegmentPositions();
                AddWaypointsActionResult updatedResults = updateResultPositions(actionResults);
                actionStack.push(new AddWaypointOperation(updatedResults));
                updateDistanceLengthAndAscent();
                return updatedResults;
            });
    }

    @NonNull
    private AddWaypointsActionResult updateResultPositions(
        AddWaypointsActionResult actionResults) {
        ArrayList<AddWaypointAction> updated = new ArrayList<>();
        for (AddWaypointAction action : actionResults.getActions()) {
            int indexFirst = indexOf(action.getFirstSegment());
            int indexSecond = indexOf(action.getSecondSegment());
            if (indexFirst != -1 && indexSecond != -1) {
                updated.add(
                    new AddWaypointAction(
                        action.getOriginalSegment(),
                        segments.get(indexFirst),
                        segments.get(indexSecond)
                    )
                );
            }
        }
        return new AddWaypointsActionResult(updated);
    }

    @NonNull
    private MovePointResult updateResultPositions(MovePointResult result) {
        ArrayList<RouteSegment> updated = new ArrayList<>();
        for (RouteSegment newSegment : result.getNewSegments()) {
            int position = indexOf(newSegment);
            updated.add(newSegment.copy(
                newSegment.getStartPoint(),
                newSegment.getEndPoint(),
                position,
                newSegment.getRoutePoints(),
                newSegment.getAscent(),
                newSegment.getDescent(),
                newSegment.getPlannerUuid()
            ));
        }
        return result.copy(result.getOriginalSegments(), updated, result.getOriginalStartPoint());
    }

    public Maybe<EditWaypointActionResult> editWaypoints(
        @NonNull List<Point> waypoints,
        @NonNull String waypointName,
        @NonNull WaypointType waypointType,
        @Nullable String waypointDescription
    ) {
        return Maybe.fromCallable(
            () -> {
                EditWaypointActionResult actionResult = WaypointUtils.editWaypoints(
                    segments, waypoints,
                    waypointName, waypointType, waypointDescription
                );
                applyEditWaypointResult(actionResult);
                actionStack.push(new EditWaypointOperation(actionResult));
                return actionResult;
            });
    }

    public Maybe<EditWaypointActionResult> deleteWaypoints(
        @NonNull List<Point> waypoints
    ) {
        return Maybe.fromCallable(
            () -> {
                EditWaypointActionResult actionResult =
                    WaypointUtils.deleteWaypoints(segments, waypoints);
                applyEditWaypointResult(actionResult);
                actionStack.push(new EditWaypointOperation(actionResult));
                return actionResult;
            });
    }

    private void applyEditWaypointResult(EditWaypointActionResult actionResult) {
        for (EditWaypointAction action : actionResult.getActions()) {
            for (int i = segments.size() - 1; i >= 0; i--) {
                final RouteSegment segment = segments.get(i);
                if (segment.equalsIgnorePosition(action.getOriginal())) {
                    segments.remove(i);
                    segments.add(i, action.getUpdated());
                    break;
                }
            }
        }
    }

    public int getWaypointsCountForAnalytics(boolean inOriginalRoute) {
        // Turn-by-turn waypoints are not included in count
        if (inOriginalRoute) {
            return originalRoute != null
                ? routeTool.calculateWaypointCount(originalRoute.getSegments(), false)
                : 0;
        } else {
            return getWaypointCount(false);
        }
    }

    public int getTurnByTurnWaypointsCountForAnalytics(boolean inOriginalRoute) {
        // Count only turn-by-turn waypoints
        if (inOriginalRoute) {
            return originalRoute != null
                ? routeTool.calculateTurnByTurnWaypointCount(originalRoute.getSegments())
                : 0;
        } else {
            return getTurnByTurnWaypointCount();
        }
    }

    public void setRouteInProgressId(@Nullable String id) {
        routeInProgressId = id;
    }

    @Nullable
    public String getRouteInProgressId() {
        if (routeInProgress != null) {
            return routeInProgress.getId();
        } else {
            return null;
        }
    }

    /**
     * Creates new segments with the given parameters and inserts them at {@code position} or
     * appends them at the end if {@code position} equals the size of {@link #segments}
     */
    @NonNull
    private Single<List<RouteSegment>> createAndAddUpdateSegments(
        int position,
        @NonNull RoutingMode mode,
        @NonNull final LatLng startPoint,
        @NonNull final LatLng endPoint,
        final Point previousRouteEnd
    ) {
        int turnByTurnWaypointCount =
            getTurnByTurnWaypointCount();
        return createSegment(mode, startPoint, endPoint, previousRouteEnd, position,
            turnByTurnWaypointCount)
            .flatMapIterable(routeSegment -> {
                List<RouteSegment> routeSegments =
                    RouteUtils.splitAtWaypointsIfNeeded(
                        Collections.singletonList(routeSegment));
                correctSegmentsStartEndPoint(endPoint, routeSegments);
                for (RouteSegment segment : routeSegments) {
                    int pos = segment.getPosition();
                    if (pos == segments.size()) {
                        segments.add(pos, segment);
                    } else {
                        segments.set(pos, segment);
                    }
                }

                updateDistanceLengthAndAscent();
                return routeSegments;
            })
            .toList();
    }

    private void correctSegmentsStartEndPoint(
        LatLng endPointLatLng,
        List<RouteSegment> routeSegments
    ) {
        RouteSegment firstSegment = CollectionsKt.firstOrNull(routeSegments);
        if (startPoint != null && firstSegment != null && firstSegment.getPosition() == 0
            && (startPoint.longitude != firstSegment.getStartPoint().getLongitude()
            || startPoint.latitude != firstSegment.getStartPoint().getLatitude())) {
            Point startPoint = firstSegment.getStartPoint();
            Point newStartPoint = startPoint.copy(
                this.startPoint.longitude,
                this.startPoint.latitude,
                startPoint.getAltitude(),
                startPoint.getRelativeDistance(),
                startPoint.getName(),
                startPoint.getType(),
                startPoint.getDescription()
            );
            RouteSegment newFirstSegment = firstSegment.copy(
                newStartPoint,
                firstSegment.getEndPoint(),
                firstSegment.getPosition(),
                firstSegment.getRoutePoints(),
                firstSegment.getAscent(),
                firstSegment.getDescent(),
                firstSegment.getPlannerUuid()
            );
            routeSegments.set(0, newFirstSegment);
        }

        RouteSegment lastSegment = CollectionsKt.lastOrNull(routeSegments);
        if (lastSegment != null &&
            (lastSegment.getEndPoint().getLatitude() != endPointLatLng.latitude
                || lastSegment.getEndPoint().getLongitude() != endPointLatLng.longitude)) {
            Point endPoint = lastSegment.getEndPoint();
            Point newEndPoint = endPoint.copy(
                endPointLatLng.longitude,
                endPointLatLng.latitude,
                endPoint.getAltitude(),
                endPoint.getRelativeDistance(),
                endPoint.getName(),
                endPoint.getType(),
                endPoint.getDescription()
            );
            RouteSegment newLastSegment = lastSegment.copy(
                lastSegment.getStartPoint(),
                newEndPoint,
                lastSegment.getPosition(),
                lastSegment.getRoutePoints(),
                lastSegment.getAscent(),
                lastSegment.getDescent(),
                lastSegment.getPlannerUuid()
            );
            routeSegments.remove(routeSegments.size() - 1);
            routeSegments.add(newLastSegment);
        }
    }

    /**
     * Creates new segments with the given parameters. Segments do not affect current model.
     * Positions start from 0 and should be updated later.
     */
    @NonNull
    private Observable<RouteSegment> createSegment(
        @NonNull RoutingMode mode,
        @NonNull final Point startPoint,
        @NonNull final Point endPoint,
        final Point previousRouteEnd,
        @Nullable String waypointName,
        @Nullable Integer waypointType
    ) {
        LatLng start = PointExtKt.toLatLng(startPoint);
        LatLng end = PointExtKt.toLatLng(endPoint);
        int turnByTurnWaypointCount =
            getTurnByTurnWaypointCount();
        return createSegment(mode, start, end, previousRouteEnd, 0, turnByTurnWaypointCount)
            .map(segment ->
                WaypointUtils.updateWaypointToRouteSegment(segment, waypointName, waypointType)
            )
            .flatMapIterable(routeSegment -> RouteUtils.splitAtWaypointsIfNeeded(
                Collections.singletonList(routeSegment)));
    }

    public int getTurnByTurnWaypointCount() {
        return segments.size() > 0 ? routeTool.calculateTurnByTurnWaypointCount(segments) : 0;
    }

    public int getWaypointCount(boolean includeTurnByTurnWaypoints) {
        return segments.size() > 0 ? routeTool.calculateWaypointCount(segments,
            includeTurnByTurnWaypoints) : 0;
    }

    public void addSegments(@NonNull List<RouteSegment> routeSegments) {
        segments.addAll(routeSegments);
        pushAppendSegmentOperations(routeSegments);
        updateDistanceLengthAndAscent();
    }

    private void pushAppendSegmentOperations(@NonNull List<RouteSegment> routeSegments) {
        // Split added segments to AppendSegmentsOperations using waypoint/planning point segments
        // Without doing it, the undo will misbehave when editing an existing route.
        ArrayList<RouteSegment> added = new ArrayList<>();
        for (int i = 0; i < routeSegments.size(); i++) {
            RouteSegment segment = routeSegments.get(i);
            added.add(segment);
            if (!segment.isTurnByTurnWaypointSegment()) {
                actionStack.push(new AppendSegmentsOperation(new ArrayList<>(added)));
                added.clear();
            }
        }
    }

    private List<RouteSegment> splitAtWaypointsAndAddSegments(
        @NonNull List<RouteSegment> routeSegments) {
        List<RouteSegment> splitSegments = RouteUtils.splitAtWaypointsIfNeeded(routeSegments);
        addSegments(splitSegments);
        return splitSegments;
    }

    private Observable<RouteSegment> createSegment(
        @NonNull RoutingMode mode,
        @NonNull LatLng startPoint,
        @NonNull LatLng newPoint,
        Point previousRouteEnd,
        int position,
        int turnByTurnWaypointCount) {
        routingApiModel.setTurnByTurnWaypointCount(turnByTurnWaypointCount);

        Observable<RouteSegment> createSegment;
        switch (mode) {
            case BIKE:
                createSegment =
                    routingApiModel.createBikeSegment(startPoint, newPoint, previousRouteEnd,
                        position);
                break;
            case FOOT:
                createSegment =
                    routingApiModel.createFootSegment(startPoint, newPoint, previousRouteEnd,
                        position);
                break;
            case MTB:
                createSegment =
                    routingApiModel.createMtbSegment(startPoint, newPoint, previousRouteEnd,
                        position);
                break;
            case RACING_BIKE:
                createSegment =
                    routingApiModel.createRacingBikeSegment(startPoint, newPoint, previousRouteEnd,
                        position);
                break;
            default:
            case STRAIGHT:
                createSegment =
                    routingApiModel.createStraightSegment(startPoint, newPoint, previousRouteEnd,
                        position, false);
                break;
        }
        return createSegment.
            subscribeOn(Schedulers.io());
    }

    void updateDistanceLengthAndAscent() {
        List<Point> points = (List<Point>) segments.stream().flatMap(routeSegment -> routeSegment.getRoutePoints().stream()).collect(
            Collectors.toList());
        distance = RouteUtils.calculateDistanceByPoints(points);
        verticalDelta = RouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(points);
    }

    public ArrayList<RouteSegment> getSegments() {
        return segments;
    }

    public void updateAvgSpeed(double avgSpeed) {
        this.avgSpeed = avgSpeed;
    }

    public double getAvgSpeed() {
        return avgSpeed;
    }

    /**
     * @return the route distance in meters
     */
    public double getDistance() {
        return distance;
    }

    /**
     * @return the route ascent in meters. Null is returned if no ascent info available in any of
     * the route segments
     */
    @Nullable
    public VerticalDelta getVerticalDelta() {
        return verticalDelta;
    }

    /**
     * Calculates the duration of this route given an average speed.
     *
     * @return duration in seconds
     */
    public double getDuration() {
        return getDistance() / avgSpeed;
    }

    @NonNull
    public List<RouteSegment> removeLatestSegments(int count) {
        if (segments.isEmpty()) {
            return new ArrayList<>();
        }
        ArrayList<RouteSegment> removedSegments = new ArrayList<>();
        int breakingPoint = segments.size() - count;
        for (int i = segments.size() - 1; i >= 0; i--) {
            removedSegments.add(segments.remove(i));
            if (i == breakingPoint) break;
        }
        updateDistanceLengthAndAscent();
        return removedSegments;
    }

    private int indexOf(RouteSegment segment) {
        if (segments.isEmpty()) return -1;
        for (int i = 0; i < segments.size(); i++) {
            RouteSegment routeSegment = segments.get(i);
            if (routeSegment.equalsIgnorePosition(segment)) {
                return i;
            }
        }
        return -1;
    }

    private void removeSegment(RouteSegment segment) {
        if (segments.isEmpty()) return;
        for (int i = 0; i < segments.size(); i++) {
            RouteSegment routeSegment = segments.get(i);
            if (routeSegment.equalsIgnorePosition(segment)) {
                segments.remove(i);
                return;
            }
        }
    }

    public void removeAllSegments() {
        segments.clear();
        actionStack.clear();
        updateDistanceLengthAndAscent();
    }

    public boolean isFetchingRoute() {
        return fetchingRoute;
    }

    public void setFetchingRoute(boolean fetchingRoute) {
        this.fetchingRoute = fetchingRoute;
    }

    public Route getNewOrUpdatedRoute(boolean isFinal, boolean checkNameIfNeed)
        throws EmptyRouteException, InvalidRouteNameException, FetchingInProgressException {
        String name = isFinal ? routeName.trim() : "route_in_progress";
        if (TextUtils.isEmpty(name) && checkNameIfNeed) {
            throw new InvalidRouteNameException();
        }
        if (segments.isEmpty()) {
            throw new EmptyRouteException();
        }
        if (fetchingRoute) {
            throw new FetchingInProgressException();
        }

        RouteSegment firstSegment = segments.get(0);
        Point startPoint = new Point(firstSegment.getStartPoint().getLongitude(),
            firstSegment.getStartPoint().getLatitude(), firstSegment.getStartPoint().getAltitude());

        RouteSegment lastSegment = segments.get(segments.size() - 1);
        Point stopPoint =
            new Point(lastSegment.getEndPoint().getLongitude(),
                lastSegment.getEndPoint().getLatitude(),
                lastSegment.getEndPoint().getAltitude());

        // TODO better way to do center point
        Point centerPoint = new Point((stopPoint.getLongitude() + startPoint.getLongitude()) / 2.0D,
            (stopPoint.getLatitude() + startPoint.getLatitude()) / 2.0D, startPoint.getAltitude());

        if (!createNewRoute && originalRoute != null && isFinal) {
            return originalRoute.copyJava(
                name,
                distance,
                RouteUtils.getActivityIdsFromActivityTypes(activityTypes),
                startPoint,
                centerPoint,
                stopPoint,
                RouteVerticalDeltaCalc.recalculateVerticalData(segments),
                watchEnabled,
                avgSpeed,
                watchEnabled ? RouteWatchSyncState.PENDING : RouteWatchSyncState.IGNORED,
                true,
                System.currentTimeMillis(),
                System.currentTimeMillis(),
                false,
                turnByTurnEnabled,
                routeProducer,
                externalUrl
            );
        } else if (routeInProgress != null) {
            return routeInProgress.copyJava(
                name,
                distance,
                RouteUtils.getActivityIdsFromActivityTypes(activityTypes),
                startPoint,
                centerPoint,
                stopPoint,
                RouteVerticalDeltaCalc.recalculateVerticalData(segments),
                watchEnabled,
                avgSpeed,
                watchEnabled ? RouteWatchSyncState.PENDING : RouteWatchSyncState.IGNORED,
                isFinal,
                System.currentTimeMillis(),
                System.currentTimeMillis(),
                !isFinal,
                turnByTurnEnabled,
                routeProducer,
                externalUrl
            );
        } else {
            routeInProgress = new Route(
                currentUserController.getUsername(),
                name,
                RouteUtils.getActivityIdsFromActivityTypes(activityTypes),
                distance,
                verticalDelta == null ? 0.0 : verticalDelta.getAscent(),
                verticalDelta == null ? 0.0 : verticalDelta.getDescent(),
                startPoint,
                centerPoint,
                stopPoint,
                true,
                RouteVerticalDeltaCalc.recalculateVerticalData(segments),
                avgSpeed,
                !isFinal,
                watchEnabled,
                watchEnabled ? RouteWatchSyncState.PENDING : RouteWatchSyncState.IGNORED,
                false,
                0,
                RouteVisibility.DEFAULT,
                UUID.randomUUID().toString(),
                0,
                "",
                System.currentTimeMillis(),
                System.currentTimeMillis(),
                System.currentTimeMillis(),
                turnByTurnEnabled,
                routeProducer,
                externalUrl
            );
            return routeInProgress;
        }
    }

    @Nullable
    public Route getRouteInProgress() {
        return routeInProgress;
    }

    public void resetRouteInProgress() {
        routeInProgress = null;
        routeInProgressId = null;
    }

    public void setRouteInProgressStartPoint(@Nullable LatLng routeInProgressStartPoint) {
        this.routeInProgressStartPoint = routeInProgressStartPoint;
    }

    public Single<MovePointResult> moveSegmentEndPoint(
        int segmentHash,
        double newEndPointLatitude,
        double newEndPointLongitude,
        @NonNull RoutingMode mode
    ) {
        // Find affected segments
        List<RouteSegment> originalSegments =
            routeTool.affectedSegmentsOnMoveEndPoint(segments, segmentHash);
        // Find the moved segment
        RouteSegment movedSegment = findSegment(segmentHash);
        if (originalSegments.isEmpty() || movedSegment == null) {
            return Single.just(new MovePointResult());
        }
        RouteSegment lastSegment = segments.get(segments.size() - 1);
        // Get the position of the first affected segment
        RouteSegment firstOriginalSegment = originalSegments.get(0);
        RouteSegment lastOriginalSegment = originalSegments.get(originalSegments.size() - 1);
        Point lastOriginalRoutePoint = lastOriginalSegment.getRoutePoints()
            .get(lastOriginalSegment.getRoutePoints().size() - 1);
        int position = indexOf(firstOriginalSegment);

        Integer waypointType = null;
        String waypointName = null;
        if (!movedSegment.isTurnByTurnWaypointSegment()) {
            // Store previous waypoint type and name so that we can remember those for the last
            // segment of 'firstSet' below.
            waypointType = movedSegment.waypointType();
            waypointName = movedSegment.waypointName();
        }

        // Remove affectedSegments
        segments.removeAll(originalSegments);

        // Start routing new segments
        Point startPoint = originalSegments.get(0).getStartPoint();
        Point firstRoutePoint = originalSegments.get(0).getRoutePoints().get(0);
        Point newPoint =
            new Point(newEndPointLongitude, newEndPointLatitude, null, 0.0, null, null);
        Point endpoint = originalSegments.get(originalSegments.size() - 1).getEndPoint();

        // Routing new segments
        Single<List<RouteSegment>> firstSet = createSegment(
            mode, startPoint, newPoint, firstRoutePoint, waypointName, waypointType
        ).toList();

        Single<Optional<List<RouteSegment>>> secondSet =
            originalSegments.size() > 1 && !movedSegment.equalsIgnorePosition(lastSegment)
                ? createSegment(mode, newPoint, endpoint, newPoint, null, null)
                .toList()
                .map(segments -> routeTool.appendEndPoint(segments, lastOriginalRoutePoint))
                .map(Optional::toOptional)
                : Single.just((Optional<List<RouteSegment>>) None.INSTANCE);

        return Single.zip(firstSet, secondSet,
            (segmentsBeforeNewPoint, segmentsAfterNewPointOptional) -> {
                List<RouteSegment> segmentsAfterNewPoint =
                    segmentsAfterNewPointOptional.toNullable();

                ArrayList<RouteSegment> newSegments = new ArrayList<>(
                    segmentsAfterNewPoint != null && !segmentsAfterNewPoint.isEmpty()
                        // Connect new segments together
                        ? routeTool.appendEndPoint(segmentsBeforeNewPoint, newPoint)
                        : segmentsBeforeNewPoint
                );
                if (segmentsAfterNewPoint != null && !segmentsAfterNewPoint.isEmpty()) {
                    newSegments.addAll(segmentsAfterNewPoint);
                    routeTool.updateSegmentWaypoint(lastOriginalSegment, newSegments);
                }
                // Add new segments to the model
                segments.addAll(position, newSegments);
                MovePointResult result =
                    new MovePointResult(originalSegments, newSegments, null);
                // Update segment positions
                updateSegmentPositions();
                MovePointResult updatedResult = updateResultPositions(result);
                actionStack.push(new MovePointOperation(updatedResult));
                updateDistanceLengthAndAscent();
                return updatedResult;
            });
    }

    @Nullable
    public RouteSegment findSegment(int segmentHashCode) {
        for (RouteSegment segment : segments) {
            if (segment.hashCodeIgnorePosition() == segmentHashCode) return segment;
        }
        return null;
    }

    private Completable addInitialRoutePoints() {
        if (routeInProgressStartPoint != null && !hasStartPoint()) {
            addStartPoint(routeInProgressStartPoint.latitude, routeInProgressStartPoint.longitude);
        } else if (initialRouteStartPoint != null && actionStack.isEmpty()) {
            addStartPoint(initialRouteStartPoint.latitude, initialRouteStartPoint.longitude);

            if (initialRouteEndPoint != null && initialRoutingMode != null) {
                return appendRoutePoint(
                    initialRouteEndPoint.latitude,
                    initialRouteEndPoint.longitude,
                    initialRoutingMode
                )
                    .ignoreElement();
            }
        }

        return Completable.complete();
    }

    /**
     * @return null if there's no segment to update otherwise it will emit a {@link MovePointResult}
     */
    @Nullable
    Single<MovePointResult> moveStartPointTo(
        double latitude,
        double longitude,
        @NonNull RoutingMode mode
    ) {
        if (segments.size() == 0) {
            // No segments, just the start point
            MovePointResult movePointResult = new MovePointResult(
                Collections.emptyList(),
                Collections.emptyList(),
                this.startPoint
            );
            this.startPoint = new LatLng(latitude, longitude);
            actionStack.push(new MoveStartPointOperation(movePointResult));
            return Single.just(movePointResult);
        } else {
            // Find affected segments
            List<RouteSegment> originalSegments =
                routeTool.affectedSegmentsOnMoveStartPoint(segments);
            // Get the position of the first affected segment
            RouteSegment lastOriginalSegment = originalSegments.get(originalSegments.size() - 1);
            // Remove affectedSegments
            segments.removeAll(originalSegments);
            LatLng originalStartPoint = this.startPoint;
            this.startPoint = new LatLng(latitude, longitude);
            // Start routing new segments
            Point newPoint = new Point(longitude, latitude, 0.0, 0.0, null, null);
            Point endpoint = lastOriginalSegment.getEndPoint();
            Single<List<RouteSegment>> createdSegments =
                createSegment(mode, newPoint, endpoint, null, null, null).toList();

            return createdSegments.map(created -> {
                ArrayList<RouteSegment> newSegments = new ArrayList<>(created);
                routeTool.updateSegmentWaypoint(lastOriginalSegment, newSegments);
                // Add new segments to the model
                segments.addAll(0, newSegments);
                MovePointResult result =
                    new MovePointResult(originalSegments, newSegments, originalStartPoint);
                // Update segment positions
                updateSegmentPositions();
                MovePointResult updatedResult = updateResultPositions(result);
                actionStack.push(new MoveStartPointOperation(updatedResult));
                updateDistanceLengthAndAscent();
                return updatedResult;
            });
        }
    }

    public boolean isWorkoutToRoute() {
        return workoutHeader != null && workoutHeader.getPolyline() != null;
    }

    public boolean haveInitialRoutePoints() {
        return initialRouteStartPoint != null && initialRouteEndPoint != null;
    }

    boolean isImportedRoute() {
        return importFileInfo != null;
    }

    public boolean hasStartPoint() {
        return startPoint != null;
    }

    public boolean hasPendingWaypoint() {
        return pendingWaypoint != null;
    }

    public boolean isEdited() {
        if (originalRoute == null) {
            return hasStartPoint();
        }

        if (originalRoute.getTurnWaypointsEnabled() != turnByTurnEnabled) {
            return true;
        }

        if (!originalRoute.getName().trim().equals(routeName.trim())) {
            return true;
        }
        return !originalRoute.getSegments().equals(getSegments());
    }

    public boolean isExistingRoute() {
        return originalRoute != null;
    }

    public void updateActivities(List<ActivityType> newActivities) {
        this.activityTypes = newActivities;
    }

    public void removeStartPoint() {
        this.startPoint = null;
    }

    public void removePendingWaypoint() {
        this.pendingWaypoint = null;
    }

    /**
     * Updates the in-memory route name.
     *
     * @param routeName the new route name
     * @return true if a new route would be created.
     */
    public boolean updateRouteName(String routeName) {
        this.routeName = routeName.trim();
        createNewRoute = originalRoute == null || isWorkoutToRoute();
        return createNewRoute;
    }

    /**
     * @return true if the route will be copied.
     */
    public boolean isActionCopyRoute() {
        return copyRoute;
    }

    @NonNull
    public String getRouteName() {
        return routeName;
    }

    RoutePlannerOperation undoLastAction() {
        if (!actionStack.isEmpty()) {
            RoutePlannerOperation op = actionStack.pop();
            if (op instanceof AddStartPointOperation) {
                removeStartPoint();
            } else if (op instanceof AppendSegmentsOperation) {
                AppendSegmentsOperation operation = (AppendSegmentsOperation) op;
                int count = operation.getSegments().size();
                removeLatestSegments(count);
            } else if (op instanceof MovePointOperation) {
                MovePointOperation operation = (MovePointOperation) op;
                undoMovePoint(operation.getMoveRoutePointResult());
            } else if (op instanceof MoveStartPointOperation) {
                MoveStartPointOperation operation = (MoveStartPointOperation) op;
                undoMovePoint(operation.getMoveRoutePointResult());
            } else if (op instanceof AddWaypointOperation) {
                AddWaypointOperation operation = (AddWaypointOperation) op;
                undoAddWaypoint(operation.getAddWaypointResult());
            } else if (op instanceof EditWaypointOperation) {
                EditWaypointOperation operation = (EditWaypointOperation) op;
                undoEditWaypoint(operation.getEditWaypointResult());
            } else if (op instanceof ReversedRouteOperation) {
                ReversedRouteOperation operation = (ReversedRouteOperation) op;
                undoReverseRoute(operation.getReverseRouteResult());
            }
            return op;
        }
        return NoOperation.INSTANCE;
    }

    private void undoAddWaypoint(AddWaypointsActionResult addWaypointResult) {
        if (addWaypointResult == null) return;

        for (AddWaypointAction action : addWaypointResult.getActions()) {
            int i1 = indexOf(action.getFirstSegment());
            int i2 = indexOf(action.getSecondSegment());
            if (i1 != -1 && i2 != -1) {
                removeSegment(action.getFirstSegment());
                removeSegment(action.getSecondSegment());
                segments.add(i1, action.getOriginalSegment());
            }
        }
        updateSegmentPositions();
    }

    private void undoEditWaypoint(EditWaypointActionResult editWaypointResult) {
        if (editWaypointResult == null) return;

        for (EditWaypointAction action : editWaypointResult.getActions()) {
            int index = indexOf(action.getUpdated());
            if (index != -1) {
                removeSegment(action.getUpdated());
                segments.add(index, action.getOriginal());
            }
        }
        updateSegmentPositions();
    }

    private void updateSegmentPositions() {
        for (int i = 0; i < segments.size(); i++) {
            RouteSegment segment = segments.get(i);
            segments.set(i, segment.copy(
                segment.getStartPoint(),
                segment.getEndPoint(),
                i,
                segment.getRoutePoints(),
                segment.getAscent(),
                segment.getDescent(),
                segment.getPlannerUuid()
            ));
        }
    }

    private void undoMovePoint(MovePointResult result) {
        List<RouteSegment> original = result.getOriginalSegments();
        List<RouteSegment> newSegments = result.getNewSegments();
        if (original.size() == 0 || newSegments.size() == 0) return;
        int index = indexOf(newSegments.get(0));
        if (index == -1) {
            // segments is empty
            return;
        }
        for (RouteSegment newSegment : newSegments) {
            removeSegment(newSegment);
        }
        for (int i = original.size() - 1; i >= 0; i--) {
            segments.add(index, original.get(i));
        }
        updateSegmentPositions();
        updateDistanceLengthAndAscent();
    }

    public void toggleAddToWatch(boolean isEnabled) {
        this.watchEnabled = isEnabled;
    }

    public boolean isSimplificationThresholdExceeded() {
        return countRoutePoints() > ROUTE_SIMPLIFICATION_THRESHOLD;
    }

    public List<RouteSegment> getSimplifiedSegments() {
        int originalPointCount = countRoutePoints();
        List<RouteSegment> simplifiedSegments = RouteSimplifierKt.simplifyRouteSegments(
            segments,
            ROUTE_SIMPLIFICATION_THRESHOLD,
            INITIAL_ROUTE_SIMPLIFICATION_DISTANCE
        );

        int simplifiedCount = 0;
        for (RouteSegment segment : simplifiedSegments) {
            simplifiedCount += segment.getRoutePoints().size();
        }

        Timber.w("Route simplified from %s points to %s points", originalPointCount,
            simplifiedCount);
        return simplifiedSegments;
    }

    public ReverseRouteResult reverseSegments() {
        if (startPoint == null) {
            throw new IllegalStateException("Start point not defined. Please call addStartPoint");
        }
        if (segments.isEmpty()) {
            throw new IllegalStateException("segments is empty");
        }
        List<RouteSegment> originalSegments = new ArrayList<>(segments);
        List<RouteSegment> newSegments = getReverseSegments(0);
        segments.clear();
        segments.addAll(newSegments);
        updateDistanceLengthAndAscent();

        ReverseRouteResult result = new ReverseRouteResult(originalSegments, newSegments);
        actionStack.push(new ReversedRouteOperation(result));
        return result;
    }

    private List<RouteSegment> getReverseSegments(int startPosition) {
        if (startPoint == null) {
            throw new IllegalStateException("Start point not defined. Please call addStartPoint");
        }
        if (segments.isEmpty()) {
            throw new IllegalStateException("segments is empty");
        }
        return routeTool.reverseRoute(startPosition, segments);
    }

    private void undoReverseRoute(ReverseRouteResult result) {
        List<RouteSegment> original = result.getOriginalSegments();
        List<RouteSegment> newSegments = result.getNewSegments();
        if (original.size() == 0 || newSegments.size() == 0) return;
        segments.clear();
        segments.addAll(original);
        updateDistanceLengthAndAscent();
    }

    public List<RouteSegment> getBackTraceSegments() {
        int prevSegmentPosition = 0;
        if (segments.size() > 0) {
            prevSegmentPosition = segments.get(segments.size() - 1).getPosition();
            prevSegmentPosition++;
        }
        List<RouteSegment> backSegments = getReverseSegments(prevSegmentPosition);
        segments.addAll(backSegments);
        updateDistanceLengthAndAscent();
        actionStack.push(new AppendSegmentsOperation(backSegments));
        return backSegments;
    }

    private int countRoutePoints() {
        int count = 0;
        for (RouteSegment segment : segments) {
            count += segment.getRoutePoints().size();
        }
        return count;
    }

    public int getRoutePointsForAnalytics() {
        return routeTool.getRoutePointsForAnalytics(segments);
    }

    public boolean isRouteClosed() {
        int segmentsSize = segments.size();
        if (segmentsSize == 0 || segmentsSize == 1) return false;

        Point startPoint = segments.get(0).getStartPoint();
        Point endPoint = segments.get(segmentsSize - 1).getEndPoint();


        return startPoint.getLatitude() == endPoint.getLatitude() && startPoint.getLongitude() == endPoint.getLongitude();
    }

    public static class EmptyRouteException extends Exception {
        public EmptyRouteException() {
        }
    }

    public static class InvalidRouteNameException extends Exception {
        public InvalidRouteNameException() {
        }
    }

    public static class FetchingInProgressException extends Exception {
        public FetchingInProgressException() {
        }
    }
}
