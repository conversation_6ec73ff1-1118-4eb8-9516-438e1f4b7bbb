package com.stt.android.home.explore.toproutes.dots

import androidx.fragment.app.viewModels
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.home.explore.R
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TopRoutesDotsFragment :
    ViewStateListFragment2<TopRoutesDotsContainer, TopRoutesDotsViewModel>() {

    override val viewModel: TopRoutesDotsViewModel by viewModels()
    override val layoutId = R.layout.fragment_top_routes_dots

    fun show() {
        viewModel.show()
    }

    fun hide() {
        viewModel.hide()
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.home.explore.toproutes.dots.TopRoutesDotsFragment"
    }
}
