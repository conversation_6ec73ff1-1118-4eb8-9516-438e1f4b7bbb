package com.stt.android.home.explore.routes.planner.waypoints.details.type

import androidx.annotation.StringRes
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.planner.pois.POIType
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WaypointDetailsTypeViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : LoadingStateViewModel<WaypointDetailsTypeContainer>(ioThread, mainThread) {

    private val pointTypeData: PointTypeData? =
        savedStateHandle[WaypointDetailsTypeFragment.ARGS_POINT_TYPE_DATA]

    private val onTypeClicked = { typeListItem: PointTypeListItem ->
        onTypeSelected.postValue(typeListItem)
    }

    val onTypeSelected = SingleLiveEvent<PointTypeListItem>()

    override fun retryLoading() {
        // Not needed
    }

    fun loadData() {
        val items = when (pointTypeData) {
            is WayPointPointData -> WaypointType.entries.map { it.asTypeListItem(pointTypeData.current) }
            is POIPointData -> POIType.validValues().map { it.asTypeListItem(pointTypeData.current) }
            else -> {
                Timber.w("Null pointType - cannot show list")
                emptyList()
            }
        }

        notifyDataLoaded(WaypointDetailsTypeContainer(items, titleStringRes, onTypeClicked))
    }

    @StringRes
    private val titleStringRes: Int = if (pointTypeData is POIPointData) {
        R.string.poi_types_header
    } else {
        R.string.waypoint_types_header
    }

    private fun WaypointType.asTypeListItem(currentlySelected: WaypointType?) = PointTypeListItem(
        nameResId = nameResId,
        iconResId = iconResId,
        pinnedAtTop = this == WaypointType.WAYPOINT,
        typeId = typeId,
        isSelected = this == currentlySelected
    )

    private fun POIType.asTypeListItem(currentlySelected: POIType?) = PointTypeListItem(
        nameResId = nameResId,
        iconResId = listIconResId,
        pinnedAtTop = this == POIType.POI,
        typeId = typeId,
        isSelected = this == currentlySelected
    )
}
