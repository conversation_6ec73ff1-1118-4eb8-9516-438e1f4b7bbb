package com.stt.android.home.explore

import androidx.fragment.app.Fragment
import com.google.android.gms.maps.model.LatLng
import com.stt.android.ui.map.mapoptions.MapOption
import com.stt.android.ui.map.selection.MapSelectionDialogFragment

fun Fragment.showMapSelectionDialog(
    mapsProviderName: String,
    showHeatmaps: <PERSON>ole<PERSON>,
    showRoadSurface: <PERSON>olean,
    showMyTracks: <PERSON><PERSON><PERSON>,
    showMyPOIsGroup: <PERSON>olean,
    mapCenter: LatLng?,
    analyticsSource: String,
    focusedOption: MapOption = MapOption.MAP_STYLE,
    showPopularRoutes: Boolean = false,
) {
    if (!this.isAdded) return

    val fm = childFragmentManager

    if (fm.isStateSaved) return

    fm.findFragmentByTag(MapSelectionDialogFragment.FRAGMENT_TAG)?.let {
        (it as MapSelectionDialogFragment).dismissAllowingStateLoss()
    }

    MapSelectionDialogFragment.newInstance(
        mapsProviderName = mapsProviderName,
        showHeatmaps = showHeatmaps,
        showRoadSurface = showRoadSurface,
        showMyTracks = showMyTracks,
        showMyPOIsGroup = showMyPOIsGroup,
        mapCenter = mapCenter,
        analyticsSource = analyticsSource,
        focusedOption = focusedOption,
        showPopularRoutes = showPopularRoutes
    )
        .show(fm, MapSelectionDialogFragment.FRAGMENT_TAG)
}
