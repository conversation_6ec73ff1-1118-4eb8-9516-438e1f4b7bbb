package com.stt.android.datasource.explore.pois

import com.stt.android.data.source.local.pois.LocalPOI
import com.stt.android.data.source.local.pois.LocalPOISyncLogEvent
import com.stt.android.data.source.local.pois.LocalPOISyncState
import com.stt.android.data.source.local.pois.OverallPOISyncEvent
import java.time.ZonedDateTime

fun localPOI(
    creation: Long,
    watchEnabled: Boolean = false,
    syncState: LocalPOISyncState = LocalPOISyncState.IDLE,
    deleted: Boolean = false
) = LocalPOI(
    creation = creation,
    modified = creation,
    longitude = 0.0,
    latitude = 0.0,
    altitude = 0.0,
    name = "poi: $creation",
    type = null,
    activityId = null,
    country = null,
    locality = null,
    watchEnabled = watchEnabled,
    key = creation.toString(),
    syncState = syncState,
    deleted = deleted,
    remoteSyncErrorCode = null,
    watchSyncErrorCode = null
)

fun localPOISyncEvent(event: OverallPOISyncEvent): LocalPOISyncLogEvent {
    val now = ZonedDateTime.now()
    return LocalPOISyncLogEvent(
        timestampMillis = now.toInstant().toEpochMilli(),
        timeISO8601 = now,
        event = event,
        metadata = null,
        shown = null
    )
}
