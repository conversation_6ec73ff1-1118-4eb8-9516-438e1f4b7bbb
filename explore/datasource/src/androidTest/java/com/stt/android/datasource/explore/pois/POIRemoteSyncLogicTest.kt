package com.stt.android.datasource.explore.pois

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.source.local.AppDatabase
import com.stt.android.data.source.local.pois.LocalPOISyncState
import com.stt.android.data.source.local.pois.OverallPOISyncEvent
import com.stt.android.testutils.CoroutinesTestRule
import com.suunto.connectivity.poi.POISyncLogicResult
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.io.IOException
import java.util.concurrent.Executors

@RunWith(AndroidJUnit4::class)
class POIRemoteSyncLogicTest {

    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @Rule
    @JvmField
    val archTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var db: AppDatabase

    private lateinit var poiRemoteDataSource: POIRemoteDataSource

    private lateinit var poiRepository: POIRepositoryImpl

    private lateinit var poiRemoteSyncLogic: POIRemoteSyncLogic

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        db = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
            .setTransactionExecutor(Executors.newSingleThreadExecutor())
            .build()
        setUpDaoAndSyncLogic(supportsWatch = true)
    }

    private fun setUpDaoAndSyncLogic(supportsWatch: Boolean) {
        val poiDao = db.poiDao()
        val poiSyncLogEventDao = db.poiSyncLogEventDao()
        poiRemoteDataSource = POIRemoteDataSource(TestPOIRemoteAPI())
        poiRemoteSyncLogic = POIRemoteSyncLogic(poiDao, poiSyncLogEventDao, poiRemoteDataSource, supportsWatch)
        poiRepository = POIRepositoryImpl(poiDao)
    }

    @After
    @Throws(IOException::class)
    fun tearDown() {
        db.close()
    }

    @Test
    fun backendSyncShouldFailIfWatchSyncIsRunning() = runBlocking {
        db.poiSyncLogEventDao().insert(
            localPOISyncEvent(OverallPOISyncEvent.WATCH_SYNC_STARTED)
        )

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()
        val lastLog = db.poiSyncLogEventDao().fetchLastSyncEvent()

        assertThat(lastLog?.event).isEqualTo(OverallPOISyncEvent.WATCH_SYNC_STARTED)
        assertThat(result).isInstanceOf(POISyncLogicResult.Failure::class.java)
    }

    @Test
    fun backendSyncShouldFailIfBackendSyncIsRunning() = runBlocking {
        db.poiSyncLogEventDao().insert(
            localPOISyncEvent(OverallPOISyncEvent.BACKEND_SYNC_STARTED)
        )

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()
        val lastLog = db.poiSyncLogEventDao().fetchLastSyncEvent()

        assertThat(lastLog?.event).isEqualTo(OverallPOISyncEvent.BACKEND_SYNC_STARTED)
        assertThat(result).isInstanceOf(POISyncLogicResult.Failure::class.java)
    }

    @Test
    fun backendSyncShouldFailIfStartedTwiceWhileSyncIsRunning() = runBlocking {
        db.poiSyncLogEventDao().insert(
            localPOISyncEvent(OverallPOISyncEvent.WATCH_SYNC_STARTED)
        )
        val result1 = poiRemoteSyncLogic.syncPOIsWithBackend()
        val result2 = poiRemoteSyncLogic.syncPOIsWithBackend()
        val lastLog = db.poiSyncLogEventDao().fetchLastSyncEvent()

        assertThat(result1).isInstanceOf(POISyncLogicResult.Failure::class.java)
        assertThat(result2).isInstanceOf(POISyncLogicResult.Failure::class.java)
        assertThat(lastLog?.event).isEqualTo(OverallPOISyncEvent.WATCH_SYNC_STARTED)
    }

    @Test
    fun deletePOIsOnBackend() = runBlocking {
        val p1 = localPOI(creation = 1)
        val p2 = localPOI(
            creation = 2,
            syncState = LocalPOISyncState.PENDING_BACKEND,
            deleted = true
        )
        // to be synced to all
        val p3 = localPOI(
            creation = 3,
            syncState = LocalPOISyncState.PENDING_ALL,
            deleted = true
        )
        // p4 identical to p3 but not yet synced to backend
        val p4 = localPOI(
            creation = 4,
            syncState = LocalPOISyncState.PENDING_ALL,
            deleted = true
        ).copy(key = null)
        // already deleted in backend
        val p5 = localPOI(
            creation = 5,
            syncState = LocalPOISyncState.IDLE,
            deleted = true
        )
        val inputList = listOf(p1, p2, p3, p4, p5)
        db.poiDao().insert(inputList)

        // adding to remote and updating local keys
        val keys = poiRemoteDataSource.upsertAll(listOf(p1, p2, p3).map { it.toDomain() })
            .mapNotNull { it.poi }
            .map {
                db.poiDao().update(
                    db.poiDao().fetchById(it.creation)!!.copy(key = it.key)
                )
                it.key
            }

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()

        // expect p2 and p3 to be deleted
        assertThat(poiRemoteDataSource.fetchAll())
            .containsExactly(p1.toDomain().copy(key = keys[0]))

        // expect p2 and p5 gone, p3 and p4 pending watch sync
        assertThat(db.poiDao().fetchAll().reversed())
            .containsExactly(
                p1.copy(key = keys[0]),
                p3.copy(key = keys[2], syncState = LocalPOISyncState.PENDING_WATCH),
                p4.copy(syncState = LocalPOISyncState.PENDING_WATCH)
            )

        assertThat(result).isInstanceOf(POISyncLogicResult.Success::class.java)
    }

    @Test
    fun pushNewPOIsToBackend() = runBlocking {
        // exists in backend and synced
        val p1 = localPOI(creation = 1)
        // exists in backend, pending backend sync in mobile
        val p2 = localPOI(
            creation = 2,
            syncState = LocalPOISyncState.PENDING_BACKEND
        ).copy(key = null)
        // not existing in backend, pending sync in mobile to ALL
        val p3 = localPOI(
            creation = 3,
            syncState = LocalPOISyncState.PENDING_ALL
        ).copy(key = null)
        // not existing in backend, pending sync in mobile to backend
        val p4 = localPOI(
            creation = 4,
            syncState = LocalPOISyncState.PENDING_BACKEND
        ).copy(key = null)

        val inputList = listOf(p1, p2, p3, p4)
        db.poiDao().insert(inputList)

        // adding to remote and updating local keys
        poiRemoteDataSource.upsertAll(listOf(p1, p2).map { it.toDomain() })
            .mapNotNull { it.poi }
            .forEach {
                db.poiDao().update(
                    db.poiDao().fetchById(it.creation)!!.copy(key = it.key)
                )
            }

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()

        // expect p1-p4 in backend
        assertThat(poiRemoteDataSource.fetchAll().map { it.creation })
            .containsExactly(1L, 2L, 3L, 4L)

        // expect p3 and p4 update with key
        assertThat(db.poiDao().fetchById(3)?.key).isNotEmpty()
        assertThat(db.poiDao().fetchById(4)?.key).isNotEmpty()

        // check correct states of synced POIs
        assertThat(db.poiDao().fetchById(1)?.syncState).isEqualTo(LocalPOISyncState.IDLE)
        assertThat(db.poiDao().fetchById(2)?.syncState).isEqualTo(LocalPOISyncState.IDLE)
        assertThat(db.poiDao().fetchById(3)?.syncState).isEqualTo(LocalPOISyncState.PENDING_WATCH)
        assertThat(db.poiDao().fetchById(4)?.syncState).isEqualTo(LocalPOISyncState.IDLE)

        assertThat(result).isInstanceOf(POISyncLogicResult.Success::class.java)
    }

    @Test
    fun editExistingPOIsToBackend() = runBlocking {
        // exists in backend and synced
        val p1 = localPOI(creation = 1)
        // exists in backend, modified and pending sync in mobile
        val p2 = localPOI(
            creation = 2,
            syncState = LocalPOISyncState.PENDING_ALL
        )

        val inputList = listOf(p1, p2)
        db.poiDao().insert(inputList)

        // adding to remote and updating local keys
        poiRemoteDataSource.upsertAll(listOf(p1, p2).map { it.toDomain() })
            .mapNotNull { it.poi }
            .forEach {
                db.poiDao().update(
                    db.poiDao().fetchById(it.creation)!!.copy(key = it.key)
                )
            }

        val updatedPOI = poiRepository.editPOIName(2, "NameEdit")

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()

        // expect p1-p2 in backend with name changed
        assertThat(poiRemoteDataSource.fetchAll().map { it.point.name })
            .containsExactly(p1.name, updatedPOI!!.point.name)

        // check correct states of synced POIs
        assertThat(db.poiDao().fetchById(1)?.syncState).isEqualTo(LocalPOISyncState.IDLE)
        assertThat(db.poiDao().fetchById(2)?.syncState).isEqualTo(LocalPOISyncState.PENDING_WATCH)

        assertThat(result).isInstanceOf(POISyncLogicResult.Success::class.java)
    }

    @Test
    fun deletePOIsOnBackendInSportsTrackerApp() = runBlocking {
        setUpDaoAndSyncLogic(supportsWatch = false)

        val p1 = localPOI(
            creation = 1,
            syncState = LocalPOISyncState.PENDING_ALL,
            deleted = true,
            watchEnabled = false
        )

        db.poiDao().insert(p1)

        // adding to remote and updating local keys
        poiRemoteDataSource.upsertAll(listOf(p1.toDomain()))
            .mapNotNull { it.poi }
            .forEach {
                db.poiDao().update(
                    db.poiDao().fetchById(it.creation)!!.copy(key = it.key)
                )
            }

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()

        // expect p1 to be deleted from DB
        assertThat(db.poiDao().fetchAll()).isEmpty()

        assertThat(result).isInstanceOf(POISyncLogicResult.Success::class.java)
    }

    @Test
    fun newWatchEnabledPOIsFromBackendShouldHaveIdleSyncStateInSportsTrackerApp() = runBlocking {
        setUpDaoAndSyncLogic(supportsWatch = false)

        val remotePOI = localPOI(
            creation = 1,
            watchEnabled = true
        )
            .toDomain()

        // adding to remote
        poiRemoteDataSource.upsertAll(listOf(remotePOI))

        val result = poiRemoteSyncLogic.syncPOIsWithBackend()

        // expect local DB to have the POI with syncState=IDLE
        assertThat(db.poiDao().fetchById(1)?.syncState).isEqualTo(LocalPOISyncState.IDLE)

        assertThat(result).isInstanceOf(POISyncLogicResult.Success::class.java)
    }
}
