plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
}

android {
    namespace 'com.stt.android.explore.data'
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.datasourceBase)
    implementation project(Deps.timeline)
    implementation project(Deps.datasource)
    implementation project(Deps.exploreDomain)
    implementation project(Deps.exploreRemote)
    implementation project(Deps.persistence)
    androidTestImplementation libs.androidx.room.testing
}
