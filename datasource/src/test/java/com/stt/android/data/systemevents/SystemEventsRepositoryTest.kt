package com.stt.android.data.systemevents

import com.stt.android.data.source.local.systemevents.SystemEventsFileStorage
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.systemevents.SystemEventsRestApi
import com.suunto.connectivity.systemevents.SuuntoSystemEventsAnalysisEvent
import com.suunto.connectivity.systemevents.SuuntoSystemEventsHeader
import com.suunto.connectivity.systemevents.SuuntoSystemEventsResponse
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever
import java.io.File

@RunWith(MockitoJUnitRunner::class)
class SystemEventsRepositoryTest {
    @Mock
    private lateinit var systemEventsRestApi: SystemEventsRestApi

    @Mock
    private lateinit var systemEventsFileStorage: SystemEventsFileStorage

    @Mock
    private lateinit var generateOTPUseCase: GenerateOTPUseCase

    private lateinit var systemEventsDataRepository: SystemEventsRepository

    @Before
    fun setup() {
        systemEventsDataRepository = SystemEventsRepository(
            systemEventsFileStorage,
            systemEventsRestApi,
            generateOTPUseCase
        )
        whenever(generateOTPUseCase.generateTOTP(any())).thenReturn("123456")
    }

    @Test
    fun `fetch systemEvents should only access local datasource`() = runTest {
        // prepare
        whenever(systemEventsFileStorage.fetchSystemEvents(any()))
            .thenReturn(SuuntoSystemEventsResponse(listOf()))
        // verify
        val foo = File("foo")
        systemEventsDataRepository.fetchSystemEvents(foo)
        verify(systemEventsFileStorage).fetchSystemEvents(foo)
        verifyNoInteractions(systemEventsRestApi)
    }

    @Test
    fun `remove systemEvents should only access local datasource`() = runTest {
        // verify
        val foo = File("foo")
        systemEventsDataRepository.removeSyncedEvents(foo)
        verify(systemEventsFileStorage).removeSyncedEvents(foo)
        verifyNoInteractions(systemEventsRestApi)
    }

    private val fakeSuuntoSystemEventsAnalysisEvent = SuuntoSystemEventsAnalysisEvent(
        header = SuuntoSystemEventsHeader(
            appVersion = "",
            deviceId = "",
            deviceType = "",
            userId = ""
        ),
        events = listOf()
    )

    @Test
    fun `save systemEvents should only access remote datasource`() = runTest {
        // verify
        systemEventsDataRepository.saveSystemEvents(fakeSuuntoSystemEventsAnalysisEvent)
        verify(systemEventsRestApi).saveSystemEvents(any(), any())
        verifyNoInteractions(systemEventsFileStorage)
    }
}
