package com.stt.android.data.terms

import com.stt.android.domain.terms.TermsRepository
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertFalse

@RunWith(MockitoJUnitRunner::class)
class TermsRepositoryTest {
    @Mock
    private lateinit var termsRemoteDataSource: TermsRemoteDataSource

    private lateinit var termsDataRepository: TermsRepository

    @Before
    fun setup() {
        termsDataRepository = TermsRepositoryImpl(termsRemoteDataSource)
    }

    @Test
    fun `accept terms should only access remote datasource`() = runTest {
        // prepare
        whenever(termsRemoteDataSource.acceptTerms())
            .thenReturn(Unit)
        // verify
        termsDataRepository.acceptTerms()

        verify(termsRemoteDataSource).acceptTerms()
    }

    @Test
    fun `check terms should only access remote datasource`() = runTest {
        // prepare
        whenever(termsRemoteDataSource.needAcceptTerms())
            .thenReturn(false)
        // verify
        assertFalse(termsDataRepository.needAcceptTerms())

        verify(termsRemoteDataSource).needAcceptTerms()
    }
}
