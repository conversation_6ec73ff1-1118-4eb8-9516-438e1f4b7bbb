package com.stt.android.data.session

import com.stt.android.data.EntityMapper
import com.stt.android.domain.session.SessionStatus
import com.stt.android.remote.session.RemoteSessionStatus
import javax.inject.Inject

class SessionStatusRemoteMapper
@Inject constructor() : EntityMapper<RemoteSessionStatus, SessionStatus> {

    override fun toDomainEntity(): (RemoteSessionStatus) -> SessionStatus = {
        when (it) {
            RemoteSessionStatus.VALID -> SessionStatus.VALID
            RemoteSessionStatus.INVALID_NEED_LOGIN -> SessionStatus.INVALID_NEED_LOGIN
            RemoteSessionStatus.INVALID_PWD_RESET -> SessionStatus.INVALID_PWD_RESET
            RemoteSessionStatus.INVALID_ACCOUNT_INCOMPLETE -> SessionStatus.INVALID_ACCOUNT_INCOMPLETE
            RemoteSessionStatus.SMS_VERIFICATION_NEEDED -> SessionStatus.SMS_VERIFICATION_NEEDED
            RemoteSessionStatus.UNKNOWN -> SessionStatus.UNKNOWN
        }
    }

    override fun toDataEntity(): (SessionStatus) -> RemoteSessionStatus = {
        when (it) {
            SessionStatus.VALID -> RemoteSessionStatus.VALID
            SessionStatus.INVALID_NEED_LOGIN -> RemoteSessionStatus.INVALID_NEED_LOGIN
            SessionStatus.INVALID_PWD_RESET -> RemoteSessionStatus.INVALID_PWD_RESET
            SessionStatus.INVALID_ACCOUNT_INCOMPLETE -> RemoteSessionStatus.INVALID_ACCOUNT_INCOMPLETE
            SessionStatus.SMS_VERIFICATION_NEEDED -> RemoteSessionStatus.SMS_VERIFICATION_NEEDED
            SessionStatus.UNKNOWN -> RemoteSessionStatus.UNKNOWN
        }
    }
}
