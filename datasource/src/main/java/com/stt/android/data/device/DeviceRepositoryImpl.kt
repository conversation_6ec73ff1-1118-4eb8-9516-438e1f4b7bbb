package com.stt.android.data.device

import com.stt.android.data.Watch
import com.stt.android.domain.device.AboutInfo
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.DeviceRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.reactivex.Flowable
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class DeviceRepositoryModule {

    @Binds
    abstract fun bindDeviceRepository(
        repositoryImpl: DeviceRepositoryImpl
    ): DeviceRepository
}

class DeviceRepositoryImpl @Inject constructor(
    @Watch private val deviceWatchDataSource: DeviceDataSource,
) : DeviceRepository {
    override fun connectedWatchState(): Flowable<ConnectedWatchState> {
        return deviceWatchDataSource.connectedWatchState()
    }

    override suspend fun fetchAboutInfo(): AboutInfo = deviceWatchDataSource.fetchAboutInfo()
}
