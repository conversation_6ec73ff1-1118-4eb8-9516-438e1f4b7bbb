package com.stt.android.data.goaldefinition

import com.stt.android.data.source.local.goaldefinition.GoalDefinitionDao
import com.stt.android.domain.goaldefinition.GoalDefinition
import com.stt.android.domain.goaldefinition.GoalDefinitionRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.map
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class GoalDefinitionRepositoryModule {

    @Binds
    abstract fun bindGoalDefinitionRepositoryImpl(
        repositoryImpl: GoalDefinitionRepositoryImpl
    ): GoalDefinitionRepository
}

class GoalDefinitionRepositoryImpl
@Inject constructor(
    private val goalDefinitionDao: GoalDefinitionDao,
    private val mapper: GoalDefinitionMapper
) : GoalDefinitionRepository {
    override fun fetchByUsername(username: String): Flow<List<GoalDefinition>> {
        return goalDefinitionDao.findByUsername(username)
            .map { it.map(mapper.toDomainEntity()) }
    }

    override fun definitionsChangedFlow(): Flow<Unit> = goalDefinitionDao.fetchAll()
        .drop(1)
        .map { Unit }

    override suspend fun upsert(goalDefinition: GoalDefinition): Long {
        val localGoalDefinition = mapper.toDataEntity()(goalDefinition)
        return goalDefinitionDao.upsert(localGoalDefinition)
    }
}
