package com.stt.android.data.sleep

import com.stt.android.domain.sleep.SleepTrackingMode

interface SleepTrackingSettingsProvider {
    suspend fun fetchSleepTrackingMode(): SleepTrackingMode

    suspend fun updateSleepTrackingMode(sleepTrackingMode: SleepTrackingMode)

    suspend fun isSpO2NightlyEnabled(): Boolean

    suspend fun setSpO2NightlyEnabled(enabled: Boolean)

    suspend fun isHrvEnabled(): Boolean

    suspend fun setHrvEnabled(enabled: Boolean)
}
