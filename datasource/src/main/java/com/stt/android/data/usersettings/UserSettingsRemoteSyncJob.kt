package com.stt.android.data.usersettings

import android.content.Context
import androidx.annotation.VisibleForTesting
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.shouldRescheduleOrFailJob
import com.stt.android.domain.user.UserSettingsDataSource
import kotlinx.coroutines.CancellationException
import timber.log.Timber
import javax.inject.Inject

/**
 * Sync user settings, notifications settings and FCM token to the server
 */
class UserSettingsRemoteSyncJob(
    context: Context,
    private val params: WorkerParameters,
    private val userSettingsDataSource: UserSettingsDataSource,
    private val currentUser: CurrentUser,
    private val fcmTokenSynchronizer: FcmTokenSynchronizer,
    private val userSettingsSynchronizer: UserSettingsSynchronizer,
    private val userSettingsSyncMonitor: UserSettingsSyncMonitor,
) : CoroutineWorker(context, params) {

    private val forceSettingsFetch: Boolean
        get() = params.inputData.getBoolean(KEY_FORCE_SETTINGS_FETCH, false)

    override suspend fun doWork(): Result {
        Timber.d("Started user settings sync")
        userSettingsSyncMonitor.onSyncEvent(UserSettingsSyncMonitor.SyncEvent.Started)
        return try {
            if (!currentUser.isLoggedIn()) {
                // Since this job is deferred until we have network connection,
                // it might be that it is executed when the user is no longer logged in.
                // In this case, we abort by marking it as success.
                Timber.d("User is not logged in, aborting sync")
                Result.success()
            }

            val locallyChanged = userSettingsDataSource.isLocallyChanged()

            if (locallyChanged) {
                Timber.d("User settings changed locally, sync changes to server")
                userSettingsSynchronizer.syncUserSettings()
            }

            Timber.d("Registering FCM token")
            fcmTokenSynchronizer.saveFcmToken()

            if (locallyChanged || forceSettingsFetch) {
                Timber.d("Fetch settings")
                userSettingsSynchronizer.fetchAndStoreUserSettings()
            } else {
                Timber.d("Skip fetch settings")
            }

            userSettingsSyncMonitor.onSyncEvent(UserSettingsSyncMonitor.SyncEvent.Success)
            Result.success()
        } catch (e: Exception) {
            if (e !is CancellationException) {
                userSettingsSyncMonitor.onSyncEvent(UserSettingsSyncMonitor.SyncEvent.Failed(e))
            }
            shouldRescheduleOrFailJob(e)
        }
    }

    class Factory
    @Inject constructor(
        private val userSettingsDataSource: UserSettingsDataSource,
        private val currentUser: CurrentUser,
        private val fcmTokenSynchronizer: FcmTokenSynchronizer,
        private val userSettingsSynchronizer: UserSettingsSynchronizer,
        private val settingsSyncMonitor: UserSettingsSyncMonitor,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return UserSettingsRemoteSyncJob(
                context = context,
                params = params,
                currentUser = currentUser,
                fcmTokenSynchronizer = fcmTokenSynchronizer,
                userSettingsDataSource = userSettingsDataSource,
                userSettingsSynchronizer = userSettingsSynchronizer,
                userSettingsSyncMonitor = settingsSyncMonitor,
            )
        }
    }

    companion object {
        @VisibleForTesting
        internal const val KEY_FORCE_SETTINGS_FETCH = "KEY_FORCE_SETTINGS_FETCH"

        @JvmStatic
        fun enqueue(workManager: WorkManager, forceSettingsFetch: Boolean) {
            Timber.d("Enqueuing")
            workManager.enqueueUniqueWork(
                "UserSettingsRemoteSyncJob",
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequestBuilder<UserSettingsRemoteSyncJob>()
                    .setConstraints(
                        Constraints.Builder()
                            .setRequiredNetworkType(NetworkType.CONNECTED)
                            .build()
                    )
                    .setInputData(
                        Data.Builder()
                            .putBoolean(KEY_FORCE_SETTINGS_FETCH, forceSettingsFetch)
                            .build()
                    )
                    .build()
            )
        }
    }
}
