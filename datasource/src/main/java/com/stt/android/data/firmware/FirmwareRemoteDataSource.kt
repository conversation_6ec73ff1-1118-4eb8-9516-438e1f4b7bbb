package com.stt.android.data.firmware

import com.stt.android.domain.firmware.FirmwareDataSource
import com.stt.android.domain.firmware.FirmwareInfo
import com.stt.android.remote.firmware.FirmwareRestApi
import com.stt.android.remote.firmware.RemoteWatchFirmwareInfo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.reactivex.Single
import timber.log.Timber
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
internal abstract class FirmwareRemoteDataSourceModule {

    @Binds
    abstract fun bindFirmwareRemoteDataSource(
        firmwareRemoteDataSource: FirmwareRemoteDataSource
    ): FirmwareDataSource
}

internal class FirmwareRemoteDataSource
@Inject constructor(
    private val firmwareRestApi: FirmwareRestApi,
) : FirmwareDataSource {

    override fun fetchFirmware(
        variant: String,
        hwCompatibilityIdentifier: String,
        isOtaUpdateFirmware: Boolean,
    ): Single<FirmwareInfo> = firmwareRestApi.fetchFirmware(
        variant = variant,
        hwCompatibilityIdentifier = hwCompatibilityIdentifier,
        sofFirmware = isOtaUpdateFirmware.takeIf { it },
    ).doOnSuccess {
        Timber.d("Fetched firmware info with variant=$variant, hwId=$hwCompatibilityIdentifier, result=$it")
    }.flatMap { response ->
        val firmwareId = response.firmwareId ?: ""
        val firmwareInfo = response.toDomain()
        if (firmwareId.isEmpty()) {
            Single.just(firmwareInfo)
        } else {
            fetchFirmwareVersionLogById(firmwareId = firmwareId, isBes = false).flatMap { log ->
                Single.just(firmwareInfo.copy(versionLog = log))
            }
        }
    }

    override fun fetchFirmwareWithUpdateCheck(
        variant: String,
        hwCompatibilityIdentifier: String,
        currentVersion: String,
        productVersion: String?,
        isOtaUpdateFirmware: Boolean
    ): Single<FirmwareInfo> {
        return firmwareRestApi.fetchFirmwareWithUpdateCheck(
            variant = variant,
            hwCompatibilityIdentifier = hwCompatibilityIdentifier,
            currentVersion = currentVersion,
            productVersion = productVersion,
            sofFirmware = isOtaUpdateFirmware.takeIf { it },
        )
            .doOnSuccess { Timber.d("Fetched update check firmware info with variant=$variant, hwId=$hwCompatibilityIdentifier, result=$it") }
            .doOnError { Timber.w(it, "fetchFirmwareWithUpdateCheck error") }
            .onErrorReturn {
                RemoteWatchFirmwareInfo(
                    deviceName = variant,
                    latestFirmwareVersion = currentVersion
                )
            }
            .flatMap { response ->
                val firmwareId = response.firmwareId ?: ""
                val firmwareInfo = response.toDomain()
                if (firmwareId.isNotEmpty()) {
                    fetchFirmwareVersionLogById(
                        firmwareId = firmwareId,
                        productVersion = productVersion,
                        isBes = true
                    ).flatMap { log ->
                        Single.just(firmwareInfo.copy(versionLog = log))
                    }
                } else {
                    fetchBesFirmwareVersionLog(
                        variant,
                        hwCompatibilityIdentifier,
                        currentVersion,
                        productVersion
                    ).flatMap { log ->
                        Single.just(firmwareInfo.copy(versionLog = log))
                    }
                }
            }
    }

    private fun fetchFirmwareVersionLogById(
        firmwareId: String,
        productVersion: String? = null,
        isBes: Boolean
    ): Single<String> {
        return if (isBes) {
            firmwareRestApi.fetchBesFirmwareVersionLog(
                firmwareId = firmwareId,
                productVersion = productVersion
            )
        } else {
            firmwareRestApi.fetchFirmwareVersionLog(firmwareId = firmwareId)
        }
            .doOnSuccess { Timber.d("fetchFirmwareVersionLogById $it") }
            .doOnError { Timber.w(it, "fetchFirmwareVersionLogById error") }
            .onErrorReturn { "" }
    }

    private fun fetchBesFirmwareVersionLog(
        variant: String,
        hwCompatibilityIdentifier: String,
        currentVersion: String,
        productVersion: String?,
    ): Single<String> {
        return firmwareRestApi.fetchBesFirmwareVersionLog(
            variant = variant,
            hwCompatibilityIdentifier = hwCompatibilityIdentifier,
            firmwareVersion = currentVersion,
            productVersion = productVersion,
        )
            .doOnSuccess { Timber.d("fetchFirmwareVersionLog $it") }
            .doOnError { Timber.w(it, "fetchFirmwareVersionLog error") }
            .onErrorReturn { "" }
    }
}

private fun RemoteWatchFirmwareInfo.toDomain(): FirmwareInfo =
    FirmwareInfo(
        deviceName = deviceName ?: "",
        firmwareUploadDate = firmwareUploadDate ?: "",
        latestFirmwareURI = latestFirmwareURI ?: "",
        latestFirmwareVersion = latestFirmwareVersion ?: "",
        version = version ?: "",
        releaseType = releaseType,
        forceUpdate = forceUpdate ?: false
    )
