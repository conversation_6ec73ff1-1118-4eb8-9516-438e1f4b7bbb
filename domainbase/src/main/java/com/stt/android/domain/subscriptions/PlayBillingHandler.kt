package com.stt.android.domain.subscriptions

import android.app.Activity

interface PlayBillingHandler {
    interface Listener {
        fun onPurchasesUpdated(purchases: List<DomainPurchase>)
        fun onPurchaseError(responseCode: Int)
        fun onPurchaseCancelled()
    }

    fun addListener(listener: Listener)
    fun removeListener(listener: Listener)
    suspend fun querySubscriptionPrices(skus: List<String>): List<SubscriptionWithPrice>
    suspend fun launchBillingFlow(activity: Activity, username: String, sku: String)
    suspend fun getPendingPurchases(): List<DomainPurchase>
    suspend fun acknowledgePurchase(token: String)
}
