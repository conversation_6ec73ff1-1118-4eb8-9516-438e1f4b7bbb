<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="showLoadingSpinner"
            type="boolean" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:theme="@style/WhiteTheme">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/terms_appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="@dimen/elevation_toolbar"
            app:layout_constraintTop_toTopOf="parent">

            <com.stt.android.ui.components.CenteredToolbar
                android:id="@+id/terms_toolbar"
                style="@style/Toolbar.Native"
                app:logo="@drawable/app_logo_small"
                app:theme="@style/Toolbar.Native" />

        </com.google.android.material.appbar.AppBarLayout>

        <com.stt.android.ui.utils.WidthLimiterLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <WebView
                android:id="@+id/terms_webview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <ProgressBar
                android:id="@+id/terms_progressbar_login_spinner"
                style="@style/Widget.AppCompat.ProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:visible="@{showLoadingSpinner}"
                tools:visibility="visible" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/retry"
                android:visibility="gone" />
        </com.stt.android.ui.utils.WidthLimiterLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
