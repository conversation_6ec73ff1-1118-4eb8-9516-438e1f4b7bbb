package com.stt.android.domain.session.facebook

import com.stt.android.domain.session.DomainUserSession
import java.time.LocalDate

sealed class FacebookSignInResult {
    data class Success(
        val userSession: DomainUserSession
    ) : FacebookSignInResult()

    data class SignupNeeded(
        val newUserCredentials: NewUserCredentials
    ) : FacebookSignInResult()
}

data class NewUserCredentials(
    val fullName: String?,
    val password: String?,
    val email: String?,
    val sex: Sex?,
    val birthday: LocalDate?,
    val facebookAccessToken: String?,
    val phoneNumber: String?
)

enum class Sex {
    MALE,
    FEMALE
}
