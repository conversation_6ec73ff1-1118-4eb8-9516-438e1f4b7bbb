package com.stt.android.sportmode.trainingmode.reducer

import android.content.Context
import com.stt.android.sportmode.trainingmode.TrainingMode
import com.stt.android.sportmode.trainingmode.TrainingModeReducer
import com.stt.android.home.settings.wheel.wheelFragmentDataReduce

class UpdateTargetByWheelReducer(private val context: Context) : TrainingModeReducer {
    override suspend fun invoke(trainingMode: TrainingMode): TrainingMode {
        val data = trainingMode.modeTarget.getWheelFragmentData(context)
        val updated = wheelFragmentDataReduce(context, data)
        if (!updated.updateConfirmed) return trainingMode
        return trainingMode.copy(
            modeTarget = trainingMode.modeTarget.reduceByWheelFragmentData(updated)
        )
    }
}
