package com.stt.android.sportmode.modesetting.autolap

import android.content.Context
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.sportmode.modesetting.ModeSettingItemList
import com.stt.android.sportmode.modesetting.ModeSettingItemListMapper
import com.stt.android.sportmode.modesetting.ModeSettingReducer
import com.stt.android.sportmode.modesetting.autolap.reducer.AutolapSelectTypeReducer
import com.stt.android.sportmode.modesetting.autolap.reducer.AutolapSwitchReducer
import com.stt.android.sportmode.modesetting.list.RadioButtonTipsItem
import com.stt.android.sportmode.modesetting.list.RadioButtonWheelItem
import com.stt.android.sportmode.modesetting.list.SwitchItem
import com.stt.android.sportmode.modesetting.resString
import com.stt.android.sportmode.modesetting.sportStr
import javax.inject.Inject

interface AutolapItemListMapper : ModeSettingItemListMapper<Autolap>

class AutolapItemListMapperImpl @Inject constructor(
    private val context: Context,
    private val measurementUnit: MeasurementUnit,
) : AutolapItemListMapper {
    override var onReducer: (ModeSettingReducer<Autolap>) -> Unit = {}
    override suspend fun invoke(mode: Autolap): ModeSettingItemList {
        if (!mode.enable) {
            return ModeSettingItemList(
                title = sportStr.autolaps.resString(context),
                itemList = listOf(
                    SwitchItem(
                        title = sportStr.autolap_switch.resString(context),
                        description = sportStr.autolap_switch_tips.resString(context),
                        checked = false,
                        onCheck = {
                            onReducer(AutolapSwitchReducer())
                        }
                    )
                )
            )
        }
        return ModeSettingItemList(
            title = sportStr.autolaps.resString(context),
            itemList = listOf(
                SwitchItem(
                    title = sportStr.autolap_switch.resString(context),
                    description = sportStr.autolap_switch_tips.resString(context),
                    checked = true,
                    onCheck = {
                        onReducer(AutolapSwitchReducer())
                    }
                )
            ) + mode.autolapTypes.mapIndexed { index, autolapType ->
                when (autolapType) {
                    is AutolapType.Duration, is AutolapType.Distance -> {
                        RadioButtonWheelItem(
                            index = index,
                            title = autolapType.title.resString(context),
                            checked = mode.autolapType == autolapType,
                            wheelPickerData = if (autolapType is AutolapType.Duration) {
                                autolapType.generateWheelData(context, onReducer)
                            } else {
                                (autolapType as AutolapType.Distance).generateWheelData(
                                    context,
                                    measurementUnit,
                                    onReducer
                                )
                            },
                            onCheck = {
                                onReducer(AutolapSelectTypeReducer(it))
                            }
                        )
                    }

                    else -> {
                        RadioButtonTipsItem(
                            index = index,
                            title = autolapType.title.resString(context),
                            tips = sportStr.autolap_switch_location_tips.resString(context),
                            checked = mode.autolapType == autolapType,
                            onCheck = {
                                onReducer(AutolapSelectTypeReducer(it))
                            }
                        )
                    }
                }
            },
        )
    }
}


