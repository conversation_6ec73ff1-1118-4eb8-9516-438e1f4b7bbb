package com.stt.android.sportmode.modesetting.metronome.reducer

import android.content.Context
import com.stt.android.sportmode.modesetting.metronome.Metronome
import com.stt.android.sportmode.modesetting.metronome.MetronomeReducer
import com.stt.android.home.settings.wheel.wheelFragmentDataReduce

class MetronomeWheelReducer(private val context: Context) : MetronomeReducer {
    override suspend fun invoke(metronome: Metronome): Metronome {
        val tempo = updatedByWheelPicker(context, metronome)
        return metronome.copy(
            tempo = tempo
        )
    }

    private suspend fun updatedByWheelPicker(
        context: Context,
        metronome: Metronome
    ): Int {
        val data = metronome.generateWheelData(context)
        return wheelFragmentDataReduce(context, data).let { newData ->
            val indices = newData.columns.map { it.defaultIndex }
            indices[0] * metronome.step + metronome.tempoRange.first
        }
    }
}
