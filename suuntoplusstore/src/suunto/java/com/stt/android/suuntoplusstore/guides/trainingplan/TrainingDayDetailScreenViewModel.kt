package com.stt.android.suuntoplusstore.guides.trainingplan

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.soy.algorithms.planner.WorkoutPlanner
import com.squareup.moshi.Moshi
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTrigger
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTriggerImpl
import com.stt.android.device.suuntoplusguide.details.WorkoutDay
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalytics
import com.stt.android.device.domain.suuntoplusguide.FetchTrainingPlanUseCase
import com.stt.android.suuntoplusstore.itemdetail.PlanDayScreenViewState
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPluStoreLibraryOperationRunnerImpl
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPlusStoreLibraryOperationRunner
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import toWorkoutItem
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

@HiltViewModel
class TrainingDayDetailScreenViewModel @Inject constructor(
    coroutinesDispatchers: CoroutinesDispatchers = CoroutinesDispatcherProvider(),
    private val fetchTrainingPlanUseCase: FetchTrainingPlanUseCase,
    private val formatter: SimGuideMessagesFormatter,
    private val analytics: SuuntoPlusStoreAnalytics,
    remoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    savedStateHandle: SavedStateHandle,
    private val moshi: Moshi
) : CoroutineViewModel(coroutinesDispatchers),
    DelayedSuuntoPlusGuideRemoteSyncTrigger by DelayedSuuntoPlusGuideRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ),
    SuuntoPlusStoreLibraryOperationRunner by SuuntoPluStoreLibraryOperationRunnerImpl(analytics) {

    private val planId: String =
        checkNotNull(savedStateHandle[SuuntoPlusStoreDestinations.PLAN_DETAIL_ID_KEY])

    private val planWeekId: Int =
        checkNotNull(savedStateHandle[SuuntoPlusStoreDestinations.PLAN_WEEK_DETAIL_ID_KEY])

    private val planDayId: Int =
        checkNotNull(savedStateHandle[SuuntoPlusStoreDestinations.PLAN_DAY_DETAIL_ID_KEY])

    private val selectedTrainingDayJson: String =
        checkNotNull(savedStateHandle[SuuntoPlusStoreDestinations.PLAN_DAY_SELECTED_KEY])

    private val selectedTrainingDay: SelectedTrainingDay? by lazy {
        runCatching {
            moshi.adapter(SelectedTrainingDay::class.java).fromJson(selectedTrainingDayJson)
        }.getOrElse {
            Timber.e(it, "Failed to parse selectedTrainingDay")
            null
        }
    }
    private val _detailScreenViewState =
        MutableStateFlow<ViewState<PlanDayScreenViewState>>(loading())
    val detailScreenViewState: StateFlow<ViewState<PlanDayScreenViewState>>
        get() = _detailScreenViewState

    private val planner = WorkoutPlanner().apply {
        setFormatter(formatter)
    }

    init {
        fetchTrainingDay()
    }

    fun refresh() {
        fetchTrainingDay()
    }

    fun getSimGuideMessagesFormatter() :SimGuideMessagesFormatter {
        return formatter
    }

    private fun fetchTrainingDay() {
        if (selectedTrainingDay == null) {
            _detailScreenViewState.value = failure(
                errorEvent = ErrorEvent(
                    shouldHandle = true,
                    errorStringRes = BaseR.string.error_generic,
                    canRetry = false
                )
            )
            return
        }

        viewModelScope.launch {
            val existingData = _detailScreenViewState.value.data
            _detailScreenViewState.value = runSuspendCatching {
                val guideIdToJsonMap = fetchTrainingPlanUseCase.fetchTrainingDay(
                    planId = planId,
                    weekNumber = planWeekId,
                    dayNumber = planDayId
                )

                if (guideIdToJsonMap.isNotEmpty()) {
                    val workouts = guideIdToJsonMap.map { (guideId, json) ->
                        planner.toWorkoutItem(guideId, json, selectedTrainingDay?.guideIdToExtendMap)
                    }

                    loaded(
                        PlanDayScreenViewState(
                            WorkoutDay(
                                dayNumber = planDayId,
                                workouts = workouts
                            ),
                            selectedTrainingDay?.showShareAction,
                            selectedTrainingDay?.shareId
                        )
                    )
                } else {
                    failure(
                        errorEvent = ErrorEvent(
                            shouldHandle = true,
                            errorStringRes = BaseR.string.error_generic,
                            canRetry = false,
                        ),
                        data = existingData
                    )
                }
            }.getOrElse { e ->
                Timber.w(e, "Failed to fetch training day data for guide $planId")
                failure(ErrorEvent.get(e::class), existingData)
            }
        }
    }
}
