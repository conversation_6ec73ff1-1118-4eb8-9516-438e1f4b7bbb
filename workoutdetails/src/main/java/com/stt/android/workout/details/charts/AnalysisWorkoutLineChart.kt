package com.stt.android.workout.details.charts

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.utils.Utils
import com.stt.android.ThemeColors
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.FontUtils
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
open class AnalysisWorkoutLineChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : WorkoutLineChart(context, attrs, defStyle) {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    private val xAxisDurationFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = when {
            value == 0.0f -> "0"
            value.isSafeValue -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
            else -> ""
        }
    }

    init {
        labelColor = ThemeColors.primaryTextColor(context)
        lineColor = ThemeColors.primaryTextColor(context)
        fillColorStart = ContextCompat.getColor(context, BaseR.color.analysis_chart_trend_fill_color_start)
        fillColorEnd = ContextCompat.getColor(context, BaseR.color.analysis_chart_trend_fill_color_end)
        chartTypeface = FontUtils.getChartLabelsTypeface(context)
        setTouchEnabled(true)

        @ColorInt val gridColor = ThemeColors.resolveColor(context, BaseR.attr.analysisGridColor)

        with(xAxis) {

            position = XAxis.XAxisPosition.BOTTOM
            setDrawGridLines(false)
            setDrawLabels(true)
            textColor = labelColor
            setAvoidFirstLastClipping(true)
            typeface = chartTypeface
            textSize = 12f
            axisLineWidth = 1.5f
            axisLineColor = gridColor
            valueFormatter = xAxisDurationFormatter
        }

        with(axisRight) {
            isEnabled = true
            setDrawAxisLine(false)
            setDrawZeroLine(false)
            setDrawGridLines(true)
            gridLineWidth = 1f
            setDrawLabels(true)
            setDrawTopYLabelEntry(true)
            setLabelCount(5, false)
            textColor = labelColor
            textSize = 12f
            typeface = chartTypeface
            this.gridColor = gridColor
            setDrawAxisLine(false)
        }
    }

    override fun addAvgLine(avgValue: Float) {
        val avg = LimitLine(avgValue)
        avg.lineWidth = 0.75f
        val avgLineColor = ThemeColors.resolveColor(context, BaseR.attr.analysisAverageLineColor)
        avg.textColor = avgLineColor
        avg.lineColor = avgLineColor
        avg.enableDashedLine(5.0f, 10.0f, 0.0f)
        avg.label = context.getString(BaseR.string.avg)
        avg.textSize = 12.0f

        // Check how high in the chart the line will be, if there's not enough room for the label
        // to be above the line then put the label below the line, otherwise above.
        val textHeight = Utils.calcTextHeight(mAxisRendererRight.paintAxisLabels, avg.label)
        val lineYPosition = getPixelForValues(0f, avgValue, YAxis.AxisDependency.RIGHT).y - extraTopOffset - topPaddingOffset
        avg.labelPosition = if (lineYPosition > (textHeight + avg.yOffset)) {
            LimitLine.LimitLabelPosition.LEFT_TOP
        } else {
            LimitLine.LimitLabelPosition.LEFT_BOTTOM
        }

        axisRight.addLimitLine(avg)
    }

}
