package com.stt.android.workout.details.graphanalysis

import android.content.Context
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.annotation.ColorInt
import androidx.core.content.res.use
import androidx.core.graphics.ColorUtils
import androidx.core.view.doOnLayout
import com.amersports.formatter.Success
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.IFillFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.jobs.MoveViewJob
import com.github.mikephil.charting.listener.OnChartGestureListener
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.utils.Utils
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.infomodel.SummaryItem
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.intensityzone.ZoneRangeWithColor
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.ZoneLineChartRenderer
import com.stt.android.ui.utils.OnChartGestureListenerAdapter
import com.stt.android.utils.FontUtils
import com.stt.android.workout.details.R
import com.stt.android.workout.details.charts.WorkoutLineEntry
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisChartHighlight
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisChartTouchListener
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisHighlightMarker
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisLineChart
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisLineDataSet
import com.stt.android.workout.details.graphanalysis.charts.GraphAnalysisYAxisDependency
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

@AndroidEntryPoint
class GraphAnalysisChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : GraphAnalysisLineChart(context, attrs, defStyleAttr) {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    var highlightListener: HighlightListener? = null
    var visibleChartRangeChangedListener: VisibleChartRangeChangedListener? = null

    private var currentRequestedHighlightXValue: Float? = null
    private var currentMainChartData: GraphAnalysisChartData? = null
    private var currentComparisonChartData: GraphAnalysisChartData? = null
    private var currentBackgroundChartData: GraphAnalysisChartData? = null
    private var mainGraphZoneRangeWithColors: List<ZoneRangeWithColor>? = null

    private var timeRangeWaitingForDataToBeSet: Pair<Float?, Float?>? = null

    private lateinit var options: GraphAnalysisChartOptions

    private val graphAnalysisHighlightMarker: GraphAnalysisHighlightMarker
        get() = marker as GraphAnalysisHighlightMarker

    private val xAxisDurationFormatter = object : ValueFormatter() {
        private val Float.isSafeValue: Boolean
            get() = isFinite() && this != Float.MAX_VALUE && this != -Float.MAX_VALUE

        override fun getFormattedValue(value: Float): String =
            if (value.isSafeValue) {
                (infoModelFormatter.formatDurationHumane(value.toDouble()) as? Success)
                    ?.value
                    ?: infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
            } else {
                ""
            }
    }

    fun setIntensityZoneLimits(
        intensityZoneLimits: IntensityZoneLimits,
        isInverted: Boolean,
        graphType: GraphType
    ) {
        val zoneRangeWithColors = intensityZoneLimits.zoneLimits.map {
            ZoneRangeWithColor(
                zoneRange = it,
                color = it.intensityZone.getColor(context)
            )
        }
        setLeftRendererZoneColors(zoneRangeWithColors, isInverted, graphType)
        setLineChartZoneColors(intensityZoneLimits.asFloats, zoneRangeWithColors)
        setInfoViewZoneColors(zoneRangeWithColors)
    }

    private fun setLeftRendererZoneColors(
        zoneRangesAndColors: List<ZoneRangeWithColor>,
        isInverted: Boolean,
        graphType: GraphType
    ) {
        graphAnalysisYAxisRendererLeft.setZones(isInverted, zoneRangesAndColors, graphType)
    }

    private fun setLineChartZoneColors(zoneLimits : List<Float>, zoneRangesAndColors: List<ZoneRangeWithColor>){
        val renderer = mRenderer
        if (renderer is ZoneLineChartRenderer) {
            val colors = zoneRangesAndColors.map { it.color }.reversed()
            // Drop the zone max limit which is unnecessary to color the zones.
            // Reverse the limits as drawing starts from the top of the chart
            val adjustedZoneLimits = zoneLimits.reversed().drop(1)
            val zones = adjustedZoneLimits.mapIndexed { index, limit ->
                val color = if (index <= colors.lastIndex) colors[index] else colors.last()
                ZoneLineChartRenderer.Zone(limit, color)
            }
            renderer.setZones(zones)
        }
    }

    private fun setInfoViewZoneColors(zoneRangesAndColors: List<ZoneRangeWithColor>){
        mainGraphZoneRangeWithColors = zoneRangesAndColors
    }

    fun resetZones() {
        (mRenderer as? ZoneLineChartRenderer)?.resetZoneLimits() // Line chart
        graphAnalysisYAxisRendererLeft.reset() // Left Y-axis
        mainGraphZoneRangeWithColors = null // Info view colors
    }

    private val internalChartValueSelectedListener = object : OnChartValueSelectedListener {
        // Using @JvmOverloads doesn't work with local declarations or overwrites, so just define
        // the overload manually
        override fun onValueSelected(entry: Entry, highlight: Highlight) {
            onValueSelected(entry, highlight, false)
        }

        fun onValueSelected(entry: Entry, highlight: Highlight, programmaticSelection: Boolean) {
            currentRequestedHighlightXValue = entry.x
            highlightListener?.let { listener ->
                val allHighlights = latestHighlightBuffer
                val highlightData = GraphAnalysisChartHighlightData(
                    programmaticallyHighlighted = programmaticSelection,
                    closestEntry = entry,
                    mainChartData = currentMainChartData,
                    mainEntry = allHighlights
                        .filter { it.graphAnalysisAxis == GraphAnalysisYAxisDependency.LEFT }
                        .minByOrNull { abs(highlight.xPx - it.xPx) }
                        ?.entry,
                    mainGraphZoneRanges = mainGraphZoneRangeWithColors,
                    comparisonChartData = currentComparisonChartData,
                    comparisonEntry = allHighlights
                        .filter { it.graphAnalysisAxis == GraphAnalysisYAxisDependency.RIGHT_FIRST }
                        .minByOrNull { abs(highlight.xPx - it.xPx) }
                        ?.entry,
                    backgroundChartData = currentBackgroundChartData,
                    backgroundEntry = allHighlights
                        .filter { it.graphAnalysisAxis == GraphAnalysisYAxisDependency.RIGHT_SECOND }
                        .minByOrNull { abs(highlight.xPx - it.xPx) }?.entry,
                )

                listener.onHighlight(highlightData)
            }
        }

        override fun onNothingSelected() {
            currentRequestedHighlightXValue = null
            highlightListener?.onHighlightCleared()
        }
    }

    private val internalChartGestureListener = object : OnChartGestureListenerAdapter() {
        // Listener is informed after next draw to make sure lowestVisibleX and highestVisibleX
        // return what is on screen after the chart manipulation. Otherwise there could be feedback
        // loops where dragging to right while smallest value is visible causes the chart to zoom
        private val informListenerRunnable = Runnable {
            visibleChartRangeChangedListener?.onVisibleRangeChanged(
                lowestVisibleX,
                highestVisibleX,
                viewPortHandler.isFullyZoomedOutX
            )
        }

        override fun onChartScale(me: MotionEvent, scaleX: Float, scaleY: Float) {
            informListener()
        }

        override fun onChartTranslate(me: MotionEvent, dX: Float, dY: Float) {
            informListener()
        }

        private fun informListener() {
            removeCallbacks()
            post(informListenerRunnable)
        }

        fun removeCallbacks() {
            removeCallbacks(informListenerRunnable)
        }
    }

    @ColorInt
    private val mainChartColor: Int = context.getColor(R.color.graph_analysis_main)

    @ColorInt
    private val comparisonChartColor: Int = context.getColor(R.color.graph_analysis_comparison)

    @ColorInt
    private val backgroundChartTextColor: Int = context.getColor(R.color.graph_analysis_background)

    /**
     * Getting both the line and fill look identical using alpha is is hard,
     * so simulate the intended look of 25% opacity by premixing the color with white
     */
    @ColorInt
    private val backgroundChartColor: Int = ColorUtils.blendARGB(Color.WHITE, backgroundChartTextColor, 0.25f)

    private val chartTypeface: Typeface = FontUtils.getChartLabelsTypeface(context)

    init {
        @ColorInt val gridColor = context.getColor(CR.color.quite_dark_gray)

        with(xAxis) {
            position = XAxis.XAxisPosition.BOTTOM
            setDrawLabels(true)
            textColor = gridColor
            typeface = chartTypeface
            textSize = 12f
            axisLineWidth = 1.5f
            axisLineColor = gridColor
            valueFormatter = xAxisDurationFormatter
            labelCount = 7
        }
        with(axisLeft) {
            textColor = mainChartColor
            typeface = chartTypeface
            isInverted = false
        }
        with(axisRight) {
            textColor = comparisonChartColor
            typeface = chartTypeface
            isInverted = false
        }
        with(axisSecondRight) {
            textColor = backgroundChartTextColor
            typeface = chartTypeface
            isInverted = false
        }

        setDrawBorders(false)
        setDrawGridBackground(false)
        description.text = ""
        setNoDataText("")
        legend.isEnabled = false
        isHighlightPerDragEnabled = true
        isHighlightPerTapEnabled = true
        isDoubleTapToZoomEnabled = false
        setPinchZoom(false)
        setTouchEnabled(true)
        setHardwareAccelerationEnabled(true)
        isScaleYEnabled = false
        isScaleXEnabled = true

        marker = GraphAnalysisHighlightMarker(context, xAxisDurationFormatter)

        super.setOnChartValueSelectedListener(internalChartValueSelectedListener)
        super.setOnChartGestureListener(internalChartGestureListener)

        with(xAxis) {
            setAvoidFirstLastClipping(false)
            setDrawGridLinesBehindData(false)
            axisMinimum = 0f
        }

        with(axisLeft) {
            setLabelCount(5, true)
            setDrawGridLinesBehindData(false)
        }

        with(axisRight) {
            setLabelCount(5, true)
            setDrawGridLinesBehindData(false)
        }

        with(axisSecondRight) {
            setLabelCount(5, true)
            setDrawGridLinesBehindData(false)
        }

        setOptions(GraphAnalysisChartOptions.fromAttributeSet(context, attrs))

        onTouchListener = GraphAnalysisChartTouchListener(this, mViewPortHandler.matrixTouch, 3f)
    }

    override fun onDetachedFromWindow() {
        // Fix MPAndroidChart memory leak https://github.com/PhilJay/MPAndroidChart/issues/2238
        // Steps to reproduce: Open a graph (e.g. speed, altitude, etc) to fullscreen and navigate back
        MoveViewJob.getInstance(null, 0f, 0f, null, null)
        super.onDetachedFromWindow()
    }

    fun setOptions(options: GraphAnalysisChartOptions) {
        this.options = options

        xAxis.isEnabled = options.showLabels
        axisLeft.isEnabled = options.showLabels
        axisRight.isEnabled = options.showLabels
        axisSecondRight.isEnabled = options.showLabels

        graphAnalysisXAxisRenderer.numInBetweenGridLines = options.xAxisMinorLineCount
        graphAnalysisXAxisRenderer.drawOnlyTickMarks = options.xAxisShowOnlyTickMarks

        graphAnalysisYAxisRendererLeft.numInBetweenGridLines = options.yAxisMinorLineCount
        graphAnalysisYAxisRendererLeft.drawOnlyTickMarks = options.yAxisShowOnlyTickMarks

        graphAnalysisYAxisRendererRight.numInBetweenGridLines = options.yAxisMinorLineCount
        graphAnalysisYAxisRendererRight.drawOnlyTickMarks = options.yAxisShowOnlyTickMarks

        graphAnalysisYAxisRendererSecondRight.numInBetweenGridLines = options.yAxisMinorLineCount
        graphAnalysisYAxisRendererSecondRight.drawOnlyTickMarks = options.yAxisShowOnlyTickMarks

        if (options.showLabels) {
            extraBottomOffset = 4f // Fixes bottoms of X axis labels clipping
        } else {
            minOffset = 0.0f // Remove all space reserved for labels
        }

        if (options.showDurationInHighlight) {
            graphAnalysisHighlightMarker.showTimestampText(true)
            extraTopOffset = 32f
            setHighlightLineTopOffset(30f)
        } else {
            graphAnalysisHighlightMarker.showTimestampText(false)
            extraTopOffset = 6f
            setHighlightLineTopOffset(30f)
        }

        invalidate()
        notifyDataSetChanged()
    }

    fun setWorkoutDurationSeconds(seconds: Float) {
        xAxis.axisMaximum = seconds
    }

    fun setChartData(
        main: GraphAnalysisChartData?,
        comparison: GraphAnalysisChartData?,
        background: GraphAnalysisChartData?,
    ) {
        prepareXAxis(
            main,
            comparison,
            background
        )

        prepareYAxis(axisLeft, main)
        prepareYAxis(axisRight, comparison)
        prepareYAxis(axisSecondRight, background)

        val mainDataSets = main?.data?.map {
            createLineDataSet(
                it.entries,
                color = mainChartColor,
                axisDependency = GraphAnalysisYAxisDependency.LEFT,
                isFilled = false,
                isInverted = main.isGraphInverted
            )
        } ?: emptyList()

        val comparisonDataSets = comparison?.data?.map {
            createLineDataSet(
                it.entries,
                color = comparisonChartColor,
                axisDependency = GraphAnalysisYAxisDependency.RIGHT_FIRST,
                isFilled = false,
                isInverted = comparison.isGraphInverted
            )
        } ?: emptyList()

        val backgroundDataSets = background?.data?.map {
            createLineDataSet(
                it.entries,
                color = backgroundChartColor,
                axisDependency = GraphAnalysisYAxisDependency.RIGHT_SECOND,
                isFilled = true,
                isInverted = background.isGraphInverted
            )
        } ?: emptyList()

        data = LineData(backgroundDataSets + comparisonDataSets + mainDataSets)
        invalidate()

        currentMainChartData = main
        currentComparisonChartData = comparison
        currentBackgroundChartData = background

        // Refresh highlight if needed so listeners get the updated entry & data
        currentRequestedHighlightXValue?.let { highlightAtXValue(it, true) }

        timeRangeWaitingForDataToBeSet?.let {
            showTimeRange(it.first, it.second)
            timeRangeWaitingForDataToBeSet = null
        }
    }

    fun highlightAtXValue(xValue: Float, forceRefresh: Boolean = false) {
        if (mXAxis.axisMaximum < mXAxis.axisMinimum) {
            // Axis is empty, nothing to highlight
            return
        }

        // Laps can sometimes have times bit past axis maximum
        val clampedXValue = xValue.coerceIn(xAxis.mAxisMinimum, xAxis.axisMaximum)

        val oldRequest = currentRequestedHighlightXValue
        if (!forceRefresh && oldRequest != null && abs(oldRequest - clampedXValue) < PROGRAMMATIC_HIGHLIGHT_UPDATE_EPSILON) {
            return
        }

        // TODO - should we add a filter that rejects the closest highlight if its x value
        // isn't close enough to the requested one?
        currentRequestedHighlightXValue = clampedXValue
        val highlightList = graphAnalysisChartHighlighter.getHighlightsAtXValue(clampedXValue)
        // always prefer highlight right to clampedXValue to make sure highlight cursor visible on UI
        val closestHighlight = highlightList
            .filter { it.x - clampedXValue >= 0F }
            .ifEmpty { highlightList }
            .minByOrNull { abs(it.x - clampedXValue) } as GraphAnalysisChartHighlight?

        if (closestHighlight != null) {
            highlightValue(closestHighlight, false)
            internalChartValueSelectedListener.onValueSelected(
                closestHighlight.entry,
                closestHighlight,
                true
            )
        }
    }

    fun showTimeRange(startSecondsInWorkout: Float?, endSecondsInWorkout: Float?) {
        if (
            currentMainChartData == null &&
            currentComparisonChartData == null &&
            currentBackgroundChartData == null
        ) {
            timeRangeWaitingForDataToBeSet = startSecondsInWorkout to endSecondsInWorkout
            return
        }

        // If chart is still sliding from previous pan gesture, it cancels out moveViewToX
        // if we don't tell it to stop first and remove queued up callback from our internal listener
        (mChartTouchListener as? GraphAnalysisChartTouchListener)?.stopDeceleration()
        internalChartGestureListener.removeCallbacks()

        // Set visible range by changing the visible range of X values so that it forces
        // the zoom to be lap length (adjusted to possibly data missing at start / end),
        // then move the left side of chart to start of lap, and revert the visible range
        // limitations to allow zooming freely.
        if (startSecondsInWorkout != null && endSecondsInWorkout != null) {
            val minimumVisibleValue = startSecondsInWorkout.coerceAtLeast(0f)
            val maximumVisibleValue = endSecondsInWorkout.coerceAtMost(xAxis.axisMaximum)
            val lapRange = maximumVisibleValue - minimumVisibleValue
            setVisibleXRange(lapRange, lapRange)
            moveViewToX(minimumVisibleValue)
            setVisibleXRangeMinimum(1f)
            setVisibleXRangeMaximum(xAxis.axisMaximum)
        } else {
            setVisibleXRangeMinimum(xAxis.axisMaximum)
            moveViewToX(0f)
            setVisibleXRangeMinimum(1f)
        }
    }

    private fun prepareXAxis(
        main: GraphAnalysisChartData?,
        comparison: GraphAnalysisChartData?,
        background: GraphAnalysisChartData?
    ) {
        // for typeface other than monospace, the width of the characters can vary a lot,
        // use "0" for all digits
        fun String.maskDigitsWithZero() = replace(Regex("\\d"), "0")

        doOnLayout { v ->
            // Target a number of X-axis labels that fits the screen comfortably, MPAndroidChart
            // can shift it a bit to either direction to get nice rounded values in the labels.
            val paint = Paint(mXAxisRenderer.paintAxisLabels).apply {
                typeface = xAxis.typeface
                textSize = xAxis.textSize
            }
            // hour format
            val maxXValue = maxOf(
                main?.data?.maxOf { it.entries.lastOrNull()?.x ?: 0f } ?: 0f,
                comparison?.data?.maxOf { it.entries.lastOrNull()?.x ?: 0f } ?: 0f,
                background?.data?.maxOf { it.entries.lastOrNull()?.x ?: 0f } ?: 0f,
                TimeUnit.HOURS.toSeconds(1).toFloat()
            )
            val maxValueLabel =
                xAxisDurationFormatter.getFormattedValue(maxXValue).maskDigitsWithZero()
            val maxXValueLabelWidth = Utils.calcTextWidth(paint, maxValueLabel)
            // minute format
            val minuteValue = TimeUnit.MINUTES.toSeconds(10).toFloat()
            val minuteValueLabel =
                xAxisDurationFormatter.getFormattedValue(minuteValue).maskDigitsWithZero()
            val minuteValueLabelWidth = Utils.calcTextWidth(paint, minuteValueLabel)

            val maxLabelWidth = max(maxXValueLabelWidth, minuteValueLabelWidth)
            // Target having the max label width of space between labels
            val targetLabelCount = (v.width / (maxLabelWidth * 2f)).roundToInt()
            xAxis.setLabelCount(targetLabelCount, false)
            xAxis.granularity = 1.0f
        }
    }

    private fun prepareYAxis(axis: YAxis, chartData: GraphAnalysisChartData?) {
        with(axis) {
            if (chartData != null) {
                isEnabled = options.showLabels
                isInverted = chartData.isGraphInverted

                this.valueFormatter = object : ValueFormatter() {
                    override fun getFormattedValue(value: Float): String {
                        return chartData.yValueFormatter(value)
                    }
                }

                if (chartData.graphMinValueStrict != null) {
                    axisMinimum = chartData.graphMinValueStrict
                } else {
                    resetAxisMinimum()
                    if (!chartData.isGraphInverted || chartData.graphMinAllowedValue != null) {
                        calculate(chartData.dataMinYValue, chartData.dataMaxYValue)
                        val computedMinRoundedTo10Below = if (axisMinimum > 0.0) {
                            axisMinimum - (axisMinimum % 10f)
                        } else {
                            axisMinimum - (10.0f + axisMinimum % 10f)
                        }
                        axisMinimum = if (chartData.graphMinAllowedValue != null) {
                            max(computedMinRoundedTo10Below, chartData.graphMinAllowedValue)
                        } else {
                            computedMinRoundedTo10Below
                        }
                    }
                }

                axisMaximum = if (chartData.graphMaxValueStrict != null) {
                    chartData.graphMaxValueStrict
                } else if (chartData.graphMinRange == null) {
                    if (chartData.isGraphInverted) {
                        chartData.dataMaxYValue
                    } else {
                        (10 * ((chartData.dataMaxYValue / 10.0f).toInt() + 1)).toFloat()
                    }
                } else {
                    // axisMinimum has either our custom rounded minimum or graphMinValue if not null
                    getMaxFromMinRange(
                        if (chartData.isGraphInverted) chartData.graphMinValueStrict else axisMinimum,
                        chartData.dataMinYValue,
                        chartData.dataMaxYValue,
                        chartData.isGraphInverted,
                        chartData.graphMaxValueStrict,
                        chartData.graphMinRange
                    )
                }
            } else {
                isEnabled = false
            }
        }
    }

    private fun createLineDataSet(
        entries: List<Entry>,
        color: Int,
        axisDependency: GraphAnalysisYAxisDependency,
        isFilled: Boolean = false,
        isInverted: Boolean = false,
        label: String = "",
    ): GraphAnalysisLineDataSet {
        return GraphAnalysisLineDataSet(entries, label).apply {
            setColor(color)
            setDrawValues(false)
            setDrawCircles(false)
            setDrawFilled(isFilled)
            fillColor = color
            fillAlpha = 255
            fillFormatter = IFillFormatter { _, dataProvider ->
                if (isInverted) {
                    val secondRightYAxisMax =
                        (dataProvider as? GraphAnalysisLineChart)?.axisSecondRight?.mAxisMaximum
                            ?: dataProvider.yChartMax
                    minOf(dataProvider.yChartMax, secondRightYAxisMax)
                } else {
                    val secondRightYAxisMin =
                        (dataProvider as? GraphAnalysisLineChart)?.axisSecondRight?.mAxisMinimum
                            ?: dataProvider.yChartMin
                    minOf(dataProvider.yChartMin, secondRightYAxisMin)
                }
            }
            mode = LineDataSet.Mode.LINEAR
            setDrawHighlightIndicators(true)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.5f
            enableDashedHighlightLine(4.5f, 4.5f, 0f)
            setDrawHorizontalHighlightIndicator(false)
            graphAnalysisYAxisDependency = axisDependency
            lineWidth = 1.5f
        }
    }

    private fun getMaxFromMinRange(
        minValue: Float?,
        datasetYMin: Float,
        datasetYMax: Float,
        inverted: Boolean,
        maxValue: Float?,
        minRange: Float
    ): Float {
        val min = minValue ?: datasetYMin
        val max = if (inverted) {
            maxValue ?: datasetYMax
        } else {
            (10 * (((maxValue ?: datasetYMax) / 10.0f).toInt() + 1)).toFloat()
        }
        return if (max - min < minRange) min + minRange else max
    }

    override fun computeScroll() {
        // Base class passes the computeScroll to default BarLineChartTouchListener,
        // do the same for our custom TouchListener
        if (mChartTouchListener is GraphAnalysisChartTouchListener) {
            (mChartTouchListener as GraphAnalysisChartTouchListener).computeScroll()
        } else {
            super.computeScroll()
        }
    }

    override fun setOnChartValueSelectedListener(listener: OnChartValueSelectedListener?) {
        Timber.w("Use HighlightListener with GraphAnalysisChart instead of default OnChartValueSelectedListener to get results for all graphs")
        super.setOnChartValueSelectedListener(listener)
    }

    override fun setOnChartGestureListener(listener: OnChartGestureListener?) {
        Timber.w("OnChartGestureListener isn't supported with GraphAnalysisChart")
    }

    interface HighlightListener {
        fun onHighlight(data: GraphAnalysisChartHighlightData)
        fun onHighlightCleared()
    }

    interface VisibleChartRangeChangedListener {
        fun onVisibleRangeChanged(
            startSeconds: Float,
            endSeconds: Float,
            isFullyZoomedOut: Boolean
        )
    }

    companion object {
        private const val PROGRAMMATIC_HIGHLIGHT_UPDATE_EPSILON = 1.0
    }
}

data class GraphAnalysisChartData(
    val graphType: GraphType,
    val data: List<WorkoutLineEntry>,
    val dataMinXValue: Float,
    val dataMaxXValue: Float,
    val dataMinYValue: Float,
    val dataMaxYValue: Float,
    val graphMinValueStrict: Float? = null,
    val graphMinAllowedValue: Float? = null,
    val graphMaxValueStrict: Float? = null,
    val graphMinRange: Float? = null,
    val isGraphInverted: Boolean = false,
    val yValueFormatter: (Float) -> String,
    val highlightedInfoValueFormatter: (Float) -> String = yValueFormatter,
    val measurementUnit: MeasurementUnit
) {
    val yDataRange: Float
        get() = dataMaxYValue - dataMinYValue
}

data class GraphAnalysisChartHighlightData(
    /**
     * False if the selection was made by a call to GraphAnalysisChart's selection methods or
     * when restoring selection after datasets were updated
     */
    val programmaticallyHighlighted: Boolean,
    /**
     * Entry closest to the point we wanted to select, the one closest to the touch point with
     * non-programmatic selections and the one closest to the requested xValue with
     * GraphAnalysisChart's selection methods
     */
    val closestEntry: Entry,
    val mainChartData: GraphAnalysisChartData?,
    val mainEntry: Entry?,
    val mainGraphZoneRanges: List<ZoneRangeWithColor>?,
    val comparisonChartData: GraphAnalysisChartData?,
    val comparisonEntry: Entry?,
    val backgroundChartData: GraphAnalysisChartData?,
    val backgroundEntry: Entry?
)

data class GraphAnalysisChartOptions(
    val showLabels: Boolean,
    val showDurationInHighlight: Boolean,
    val showGrid: Boolean,
    val xAxisMinorLineCount: Int,
    val yAxisMinorLineCount: Int,
    val xAxisShowOnlyTickMarks: Boolean,
    val yAxisShowOnlyTickMarks: Boolean,
) {
    companion object {
        private val DEFAULTS =
            GraphAnalysisChartOptions(
                showLabels = true,
                showDurationInHighlight = false,
                showGrid = true,
                xAxisMinorLineCount = 4,
                yAxisMinorLineCount = 4,
                xAxisShowOnlyTickMarks = false,
                yAxisShowOnlyTickMarks = false
            )

        fun fromAttributeSet(context: Context, attrs: AttributeSet?): GraphAnalysisChartOptions {
            if (attrs == null) {
                return DEFAULTS
            }

            val options = context.resources.obtainAttributes(attrs, R.styleable.GraphAnalysisChartAttrs).use {
                GraphAnalysisChartOptions(
                    showLabels = it.getBoolean(R.styleable.GraphAnalysisChartAttrs_showAxisLabels, DEFAULTS.showLabels),
                    showDurationInHighlight = it.getBoolean(R.styleable.GraphAnalysisChartAttrs_showDurationInHighlight, DEFAULTS.showDurationInHighlight),
                    showGrid = it.getBoolean(R.styleable.GraphAnalysisChartAttrs_showGrid, DEFAULTS.showGrid),
                    xAxisMinorLineCount = it.getInt(R.styleable.GraphAnalysisChartAttrs_xAxisMinorLineCount, DEFAULTS.xAxisMinorLineCount),
                    yAxisMinorLineCount = it.getInt(R.styleable.GraphAnalysisChartAttrs_yAxisMinorLineCount, DEFAULTS.yAxisMinorLineCount),
                    xAxisShowOnlyTickMarks = it.getBoolean(R.styleable.GraphAnalysisChartAttrs_xAxisShowOnlyTickMarks, DEFAULTS.xAxisShowOnlyTickMarks),
                    yAxisShowOnlyTickMarks = it.getBoolean(R.styleable.GraphAnalysisChartAttrs_yAxisShowOnlyTickMarks, DEFAULTS.yAxisShowOnlyTickMarks),
                )
            }

            return options
        }
    }
}
