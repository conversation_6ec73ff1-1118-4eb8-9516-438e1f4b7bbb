package com.stt.android.workout.details.advancedlaps

import android.view.View
import android.widget.TextView
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.FragmentManager
import androidx.viewpager.widget.ViewPager
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.google.android.material.tabs.TabLayout
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.theme.AppTheme
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.SmarterViewPager
import com.stt.android.workout.details.LapPageChangeListener
import com.stt.android.workout.details.R
import com.stt.android.workout.details.laps.advanced.AdvancedLapsViewPagerAdapter
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableItems
import com.stt.android.workout.details.laps.advanced.table.OnHighlightVariancesToggled
import com.stt.android.workout.details.laps.advanced.table.OnSelectColumnRequested
import com.stt.android.workout.details.laps.advanced.table.composables.LapCellColorInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@EpoxyModelClass
abstract class AdvancedLapsModel : EpoxyModelWithHolder<AdvancedLapsViewHolder>() {

    @EpoxyAttribute
    var stId: Int? = null

    @EpoxyAttribute
    var longScreenshotLayout: Boolean = false

    @EpoxyAttribute
    lateinit var lapTables: List<AdvancedLapsTableItems>

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var currentLapTable: Int = 0

    @EpoxyAttribute
    var lapSelectionEnabled: Boolean = false

    @EpoxyAttribute
    var showLapsTableColouringToggle: Boolean = true

    @EpoxyAttribute
    var lapsTableColouringEnabled: Boolean = true

    @EpoxyAttribute
    var showLapCellColorInfoUi: Boolean = false

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onSelectColumnRequested: OnSelectColumnRequested? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onHighlightVariancesToggled: OnHighlightVariancesToggled? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var fragmentManager: FragmentManager

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var pageChangeListener: LapPageChangeListener? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var lifecycleScope: CoroutineScope? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var selectLapsTableType: LapsTableType? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var selectLapsTableRow: LapsTableRow? = null

    override fun getDefaultLayout() = R.layout.model_advanced_laps

    override fun bind(holder: AdvancedLapsViewHolder) {
        stId?.let { stId ->
            var adapter = holder.viewPager.adapter as? AdvancedLapsViewPagerAdapter
            if (adapter == null || adapter.stId != stId) {
                adapter = AdvancedLapsViewPagerAdapter(
                    holder.viewPager.context,
                    infoModelFormatter,
                    stId,
                    lapTables,
                    onSelectColumnRequested,
                    lapSelectionEnabled,
                    lapsTableColouringEnabled,
                )
                holder.viewPager.adapter = adapter
            } else {
                adapter.tableData = lapTables
                adapter.isLapsTableColouringEnabled = lapsTableColouringEnabled
                adapter.notifyDataSetChanged()
            }

            holder.tabLayout.setupWithViewPager(holder.viewPager)
            // longScreenshot can't click tabLayout and scroll viewpager
            for (i in 0..<holder.tabLayout.tabCount) {
                val tab = holder.tabLayout.getTabAt(i)
                val item = adapter.tableData[i]
                tab?.apply {
                    view.isEnabled = !longScreenshotLayout
                    setCustomView(R.layout.tab_item)
                    customView?.let {
                        it.findViewById<TextView>(R.id.tabName)?.text =
                            adapter.getPageTitle(i)
                        it.findViewById<View>(R.id.tabSelectIcon)?.visibility =
                            if (item.lapsTableType == selectLapsTableType) View.VISIBLE else View.GONE
                    }
                }
            }
            holder.viewPager.scrollingEnabled = !longScreenshotLayout
            holder.viewPager.setCurrentItem(currentLapTable, false)
            if (showLapCellColorInfoUi) {
                updateLapCellColorInfoComposable(holder, currentLapTable)
            }

            val listener = object : ViewPager.SimpleOnPageChangeListener() {
                override fun onPageSelected(position: Int) {
                    val activityName = ActivityType.valueOf(stId).simpleName
                    lifecycleScope?.launch {
                        val viewPagerAdapter =
                            (holder.viewPager.adapter as? AdvancedLapsViewPagerAdapter)
                        val interval = viewPagerAdapter?.getLapsTableType(position)
                        interval?.let { pageChangeListener?.onPageChanged(it, activityName) }
                        if (showLapCellColorInfoUi) {
                            // Introduce a slight delay to ensure the ViewPager animation has completed
                            delay(200)
                            updateLapCellColorInfoComposable(holder, position)
                        }
                    }
                }
            }
            // Remove existing listener from viewpager before adding a new one
            holder.onPageChangeListener?.let {
                holder.viewPager.removeOnPageChangeListener(it)
            }
            // Store the new listener for later removal
            holder.onPageChangeListener = listener
            holder.viewPager.addOnPageChangeListener(listener)
        }
    }

    /**
     * Updates the LapCellColorInfo Composable based on the current position.
     * This approach directly modifies the Composable without involving the ViewModel,
     * resulting in improved performance by avoiding unnecessary ViewModel updates
     * and Epoxy model rebinding.
     */
    private fun updateLapCellColorInfoComposable(holder: AdvancedLapsViewHolder, position: Int) {
        if (!::lapTables.isInitialized)
            return

        val tableSize = lapTables.getOrNull(position)?.lapsTable?.lapsTableRows?.size
        val showNotEnoughRowsText = tableSize != null && tableSize < MINIMUM_ROWS_FOR_INFO_DISPLAY

        holder.lapCellColorInfo.setContent {
            AppTheme {
                LapCellColorInfo(
                    showLapsTableColouringToggle = showLapsTableColouringToggle,
                    isLapsTableColouringEnabled = lapsTableColouringEnabled,
                    showNotEnoughRowsText = showNotEnoughRowsText,
                    onCheckedChange = {
                        onHighlightVariancesToggled?.invoke(it)
                    })
            }
        }
    }

    override fun unbind(holder: AdvancedLapsViewHolder) {
        super.unbind(holder)
        holder.viewPager.clearOnPageChangeListeners()
        holder.onPageChangeListener = null
    }

    companion object {
        private const val MINIMUM_ROWS_FOR_INFO_DISPLAY = 5
    }
}

class AdvancedLapsViewHolder : KotlinEpoxyHolder() {
    var onPageChangeListener: ViewPager.SimpleOnPageChangeListener? = null
    val viewPager by bind<SmarterViewPager>(R.id.laps_view_pager)
    val tabLayout by bind<TabLayout>(R.id.laps_tab_layout)
    val lapCellColorInfo by bind<ComposeView>(R.id.lap_cell_color_info)
}
