package com.stt.android.divecustomization.customization

import com.soy.algorithms.divemodecustomization.entities.Mode
import com.stt.android.divecustomization.customization.logic.displays.DiveDisplayEditFixedFields
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

@RunWith(MockitoJUnitRunner::class)
class DiveDisplayEditFixedFieldsTest : DiveCustomizationTestBase() {

    private lateinit var diveDisplayEditFixedFields: DiveDisplayEditFixedFields

    @Before
    override fun setup() = runTest {
        super.setup()
        diveDisplayEditFixedFields = viewModel

        // set the view and field to be the first one
        viewModel.setCurrentlyEditedViewAndFieldType(0, "C1.2")
    }

    @Test
    fun `check if dive fixed field values flow returns correct values`() = runTest {
        val diveFieldsFlow = diveDisplayEditFixedFields.getDiveFixedFieldsValuesFlow(0, "C1.2").first()

        // config has 1 field with 28 types with Empty selected
        assertThat(diveFieldsFlow.data?.optionsList?.size).isEqualTo(28)
        assertThat(diveFieldsFlow.data?.optionsList?.get(0)?.selected).isTrue
        assertThat(diveFieldsFlow.data?.optionsList?.get(0)?.optionType?.value).isEqualTo("Empty")
    }

    @Test
    fun `checking a fixed field should call validator with correct change`() = runTest {
        diveDisplayEditFixedFields.setDiveDisplayFixedField(
            value = "BatteryTime",
            viewIndex = 0,
            fieldType = "C1.2"
        )

        val argumentCaptor = argumentCaptor<Mode>()
        verify(diveDeviceModeUseCase, times(1)).validateCustomizationMode(
            any(),
            argumentCaptor.capture(),
            any()
        )

        val editedField = argumentCaptor.firstValue.views.view[0].fields?.field?.get(0)

        // value of the fixed field should have changed to BatteryTime
        assertThat(editedField?.type?.value).isEqualTo("BatteryTime")
    }
}
