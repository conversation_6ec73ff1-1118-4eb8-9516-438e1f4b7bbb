{"availableOptions": {"modeAlarms": {"diveTimeEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "diveTimeTime": {"conditions": [{"conditionType": "range", "max": 59940, "min": 60}], "editor": {"default": 30.0, "max": 999.0, "min": 1.0, "step": 1.0, "type": "range", "unit": "min"}, "unit": "s", "values": []}, "gasTimeEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "gasTimeTime": {"conditions": [{"conditionType": "range", "max": 1800, "min": 60}], "editor": {"default": 10.0, "max": 30.0, "min": 1.0, "step": 1.0, "type": "range", "unit": "min"}, "unit": "s", "values": []}, "maxDepthDepth": {"conditions": [{"conditionType": "range", "max": 302.0, "min": 3.0}], "editor": {"default": 30.0, "min": 3.0, "steps": [{"step": 0.5, "to": 100.0}, {"step": 1.0, "to": 300.0}], "type": "piecewiseRange", "unit": "m"}, "unit": "m", "values": []}, "maxDepthEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "tankPressureEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "tankPressurePressure": {"conditions": [{"conditionType": "range", "max": 36000000.0, "min": 600000.0}], "editor": {"default": 75.0, "max": 360.0, "min": 10.0, "step": 10.0, "type": "range", "unit": "bar"}, "unit": "Pa", "values": []}}, "modeDivingAlgorithm": {"conditions": [], "values": [{"label": "TXT_APP_ALGORITHM_SUUNTO_FUSED2_RGBM", "value": "Suunto Fused2 RGBM"}]}, "modeDivingAltitude": {"conditions": [], "values": [{"label": "TXT_APP_ALTITUDE_SEA_LEVEL", "value": 0}, {"label": "TXT_APP_ALTITUDE_300_1500m", "value": 1}, {"label": "TXT_APP_ALTITUDE_1500_3000m", "value": 2}]}, "modeDivingAscentStepsEnabled": {"conditions": [], "values": [{"label": "TXT_APP_DECO_PROFILE_STEPPED", "value": true}, {"label": "TXT_APP_DECO_PROFILE_CONTINUOUS", "value": false}]}, "modeDivingConservatism": {"conditions": [], "values": [{"label": "TXT_APP_MORE_AGGRESSIVE", "value": -2}, {"label": "TXT_APP_AGGRESSIVE", "value": -1}, {"label": "TXT_APP_DEFAULT", "value": 0}, {"label": "TXT_APP_CONSERVATIVE", "value": 1}, {"label": "TXT_APP_MORE_CONSERVATIVE", "value": 2}]}, "modeDivingDeepStopEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingDiveMode": {"conditions": [], "default": {"value": "OC"}, "values": [{"label": "TXT_APP_DIVE_MODE_GAUGE", "value": "Gauge"}, {"label": "TXT_APP_DIVE_MODE_OC", "value": "OC"}]}, "modeDivingDiveModeFlagsGasEdit": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingDiveModeFlagsHelium": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingDiveModeFlagsMultiGas": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingDiveModeFlagsType": {"conditions": [], "values": [{"label": "TXT_APP_DIVEMODE_FLAG_TYPE_OC", "value": "OC"}]}, "modeDivingDiveStyle": {"conditions": [], "values": [{"label": "TXT_APP_DIVE_STYLE_FREE", "value": "Free"}, {"label": "TXT_APP_DIVE_STYLE_OFF", "value": "Off"}, {"label": "TXT_APP_DIVE_STYLE_SCUBA", "value": "Scuba"}]}, "modeDivingFixedPO2Enabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingFixedPO2Value": {"editor": {"type": "range", "unit": "bar", "min": 1.2, "max": 1.6, "step": 0.1, "steps": null, "default": 1.4, "_comment": null}}, "modeDivingHighSetPoint": {"conditions": [{"conditionType": "range", "max": 150000, "min": 100000}], "editor": {"default": 1.3, "max": 1.5, "min": 1.0, "step": 0.1, "type": "range", "unit": "ata"}, "unit": "Pa", "values": []}, "modeDivingLastDecoStopDepth": {"conditions": [], "values": [{"label": "TXT_APP_LAST_STOP_DEPTH_3M", "value": 3.0}, {"label": "TXT_APP_LAST_STOP_DEPTH_6M", "value": 6.0}]}, "modeDivingLowSetPoint": {"conditions": [{"conditionType": "range", "max": 90000, "min": 40000}], "editor": {"default": 0.7, "max": 0.9, "min": 0.4, "step": 0.1, "type": "range", "unit": "ata"}, "unit": "Pa", "values": []}, "modeDivingMaxEND": {"conditions": [{"conditionType": "range", "max": 50.0, "min": 15.0, "step": 1.0}], "values": []}, "modeDivingMaxGF": {"conditions": [{"conditionType": "range", "max": 1.0, "min": 0.5, "step": 0.01}, {"conditionType": "moreOrEqual", "value": 0.3}], "values": []}, "modeDivingMinGF": {"conditions": [{"conditionType": "range", "max": 1.0, "min": 0.1, "step": 0.01}, {"conditionType": "lessOrEqual", "value": 0.7}], "values": []}, "modeDivingSafetyStopTime": {"conditions": [{"conditionType": "range", "max": 300, "min": 180}], "editor": {"default": 3.0, "max": 5.0, "min": 3.0, "step": 1.0, "type": "range", "unit": "min"}, "unit": "s", "values": []}, "modeDivingSwitchHighSetPointDepth": {"conditions": [{"conditionType": "range", "max": 61.0, "min": 15.0}], "editor": {"default": 21.0, "max": 60.0, "min": 15.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "modeDivingSwitchHighSetPointEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeDivingSwitchLowSetPointDepth": {"conditions": [{"conditionType": "range", "max": 15.25, "min": 3.0}], "editor": {"default": 4.5, "max": 15.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "modeDivingSwitchLowSetPointEnabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "modeGases": {"gas": [{"helium": {"conditions": [{"conditionType": "range", "max": 0.95, "min": 0.0, "step": 0.01}, {"conditionType": "combinedSum", "max": 1.0}], "values": []}, "oxygen": {"conditions": [{"conditionType": "range", "max": 0.99, "min": 0.05, "step": 0.01}, {"conditionType": "combinedSum", "max": 1.0}], "editor": {"type": "range", "unit": "%", "min": 5, "max": 99, "step": 1, "default": 21}, "values": []}, "po2": {"conditions": [{"conditionType": "range", "max": 160000.0, "min": 50000.0}], "editor": {"default": 1.4, "max": 1.6, "min": 0.5, "step": 0.1, "type": "range", "unit": "bar"}, "unit": "Pa", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_OFF", "value": "Off"}, {"label": "TXT_APP_PRIMARY", "value": "Primary"}]}, "tankFillPressure": {"conditions": [{"conditionType": "range", "max": 36000000.0, "min": 0.0, "step": 100000.0}], "values": []}, "tankSize": {"conditions": [{"conditionType": "range", "max": 0.1, "min": 0.0}, {"conditionType": "absent"}], "editor": {"default": 12.0, "max": 50.0, "min": 3.0, "step": 1.0, "type": "range", "unit": "l"}, "unit": "m^3", "values": []}, "transmitterID": {"conditions": [], "values": []}, "transmitterName": {"conditions": [], "values": []}}], "gasesListAvailableOptions": {"conditions": [{"conditionType": "groupSizes", "groupSizes": {"Primary": {"max": 20, "min": 1}, "Diluent": {"max": 0, "min": 0}, "Oxygen": {"max": 0, "min": 0}}, "groupingSubKey": "State"}], "values": []}}, "modeName": {"conditions": [{"conditionType": "regex", "regex": "^[!%&'()*+,\\-./0-9₂:?A-Z^`a-z|~¡¨°´¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÑÒÓÔÕÖØÙÚÛÜÝßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýÿŒœŠšŸ–’><\\s]{1,15}$"}], "values": []}, "modeNotifications": {"depth": {"depth": [{"depth": {"conditions": [{"conditionType": "range", "max": 101.0, "min": 3.0}], "editor": {"max": 100.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_Off", "value": "Off"}, {"label": "TXT_APP_Sound", "value": "Sound"}, {"label": "TXT_APP_Vibra", "value": "<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_SoundAndVibra", "value": "Sound+Vibra"}]}}, {"depth": {"conditions": [{"conditionType": "range", "max": 101.0, "min": 3.0}], "editor": {"max": 100.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_Off", "value": "Off"}, {"label": "TXT_APP_Sound", "value": "Sound"}, {"label": "TXT_APP_Vibra", "value": "<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_SoundAndVibra", "value": "Sound+Vibra"}]}}, {"depth": {"conditions": [{"conditionType": "range", "max": 101.0, "min": 3.0}], "editor": {"max": 100.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_Off", "value": "Off"}, {"label": "TXT_APP_Sound", "value": "Sound"}, {"label": "TXT_APP_Vibra", "value": "<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_SoundAndVibra", "value": "Sound+Vibra"}]}}, {"depth": {"conditions": [{"conditionType": "range", "max": 101.0, "min": 3.0}], "editor": {"max": 100.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_Off", "value": "Off"}, {"label": "TXT_APP_Sound", "value": "Sound"}, {"label": "TXT_APP_Vibra", "value": "<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_SoundAndVibra", "value": "Sound+Vibra"}]}}, {"depth": {"conditions": [{"conditionType": "range", "max": 101.0, "min": 3.0}], "editor": {"max": 100.0, "min": 3.0, "step": 0.5, "type": "range", "unit": "m"}, "unit": "m", "values": []}, "state": {"conditions": [], "values": [{"label": "TXT_APP_Off", "value": "Off"}, {"label": "TXT_APP_Sound", "value": "Sound"}, {"label": "TXT_APP_Vibra", "value": "<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_SoundAndVibra", "value": "Sound+Vibra"}]}}]}, "depthsListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 5, "min": 0}], "values": []}, "surfaceTime": {"enabled": {"conditions": [], "values": [{"value": true}, {"value": false}]}, "time": {"conditions": [{"conditionType": "range", "max": 59940, "min": 10}], "editor": {"default": 300.0, "max": 59940.0, "min": 10.0, "step": 1.0, "type": "range", "unit": "s"}, "unit": "s", "values": []}}}, "modeViews": {"view": [{"fields": {"field": [{"name": {"conditions": [], "values": [{"label": "", "value": "None"}, {"label": "", "value": "A1"}, {"label": "", "value": "A2"}, {"label": "", "value": "A2.1"}, {"label": "", "value": "A2.2"}, {"label": "", "value": "C1.1"}, {"label": "", "value": "C1.2"}, {"label": "", "value": "C2.1"}, {"label": "", "value": "C2.2"}, {"label": "", "value": "G1"}, {"label": "", "value": "A3"}]}, "type": {"conditions": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_Depth", "value": "De<PERSON><PERSON>"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_NoDecTime", "value": "Surface"}, {"label": "TXT_APP_Ceiling", "value": "Ceiling"}, {"label": "TXT_APP_Gas", "value": "Gas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_BatteryTime", "value": "BatteryTime"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Time", "value": "Time"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_DiveTimer", "value": "DiveTimer"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_Ventilation", "value": "Ventilation"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}, {"label": "TXT_APP_StopTime", "value": "StopTime"}, {"label": "TXT_APP_CCRO2TankPressure", "value": "CCRO2TankPressure"}, {"label": "TXT_APP_Heading", "value": "Heading"}, {"label": "TXT_APP_MaxAscentSpeed", "value": "MaxAscentSpeed"}, {"label": "TXT_APP_MaxAscentSpeed", "value": "MaxDescentSpeed"}, {"label": "TXT_APP_SurfaceInterval", "value": "SurfaceInterval"}, {"label": "TXT_APP_DualTime", "value": "DualTime"}, {"label": "TXT_APP_SStopTime", "value": "SStopTime"}, {"label": "TXT_APP_DStopTime", "value": "DStopTime"}]}}], "fieldsLength": {"conditions": [{"conditionType": "length", "max": 0, "min": 0}], "values": []}}, "layout": {"conditions": [{"conditionType": "indexRange", "max": "0", "min": "0"}], "values": [{"label": "TXT_APP_RoundGraphicalDaily", "value": "RoundGraphicalDaily"}]}, "selectionFields": {"field": [{"name": {"conditions": [], "default": {"value": "Y1"}, "values": [{"label": "", "value": "Y1"}]}, "types": {"type": [{"conditions": [], "requiredValues": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_Consumption", "value": "Consumption"}, {"label": "TXT_APP_CurrentGas", "value": "CurrentGas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_SetPoint", "value": "SetPoint"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_Timer", "value": "Timer"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}]}], "typesListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 15, "min": 1}, {"conditionType": "uniqueElementsBy"}], "values": []}}}, {"name": {"conditions": [], "default": {"value": "Y1"}, "values": [{"label": "", "value": "Y1"}]}, "types": {"type": [{"conditions": [], "requiredValues": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_Consumption", "value": "Consumption"}, {"label": "TXT_APP_CurrentGas", "value": "CurrentGas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_SetPoint", "value": "SetPoint"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_Timer", "value": "Timer"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}]}], "typesListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 15, "min": 1}, {"conditionType": "uniqueElementsBy"}], "values": []}}}], "fieldsListAvailableOptions": {"conditions": [{"conditionType": "uniqueElementsBy"}], "values": []}}}, {"fields": {"field": [{"name": {"conditions": [], "values": [{"label": "", "value": "None"}, {"label": "", "value": "A1"}, {"label": "", "value": "A2"}, {"label": "", "value": "A2.1"}, {"label": "", "value": "A2.2"}, {"label": "", "value": "C1.1"}, {"label": "", "value": "C1.2"}, {"label": "", "value": "C2.1"}, {"label": "", "value": "C2.2"}, {"label": "", "value": "G1"}, {"label": "", "value": "A3"}]}, "type": {"conditions": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_Depth", "value": "De<PERSON><PERSON>"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_NoDecTime", "value": "Surface"}, {"label": "TXT_APP_Ceiling", "value": "Ceiling"}, {"label": "TXT_APP_Gas", "value": "Gas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_BatteryTime", "value": "BatteryTime"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Time", "value": "Time"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_DiveTimer", "value": "DiveTimer"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_Ventilation", "value": "Ventilation"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}, {"label": "TXT_APP_StopTime", "value": "StopTime"}, {"label": "TXT_APP_CCRO2TankPressure", "value": "CCRO2TankPressure"}, {"label": "TXT_APP_Heading", "value": "Heading"}, {"label": "TXT_APP_MaxAscentSpeed", "value": "MaxAscentSpeed"}, {"label": "TXT_APP_MaxAscentSpeed", "value": "MaxDescentSpeed"}, {"label": "TXT_APP_SurfaceInterval", "value": "SurfaceInterval"}, {"label": "TXT_APP_DualTime", "value": "DualTime"}, {"label": "TXT_APP_SStopTime", "value": "SStopTime"}, {"label": "TXT_APP_DStopTime", "value": "DStopTime"}]}}], "fieldsLength": {"conditions": [{"conditionType": "length", "max": 0, "min": 0}], "values": []}}, "layout": {"conditions": [{"conditionType": "indexRange", "max": "0", "min": "0"}], "values": [{"label": "TXT_APP_RoundGraphicalDaily", "value": "RoundGraphicalDaily"}]}, "selectionFields": {"field": [{"name": {"conditions": [], "default": {"value": "Y1"}, "values": [{"label": "", "value": "Y1"}]}, "types": {"type": [{"conditions": [], "requiredValues": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_Consumption", "value": "Consumption"}, {"label": "TXT_APP_CurrentGas", "value": "CurrentGas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_SetPoint", "value": "SetPoint"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_Timer", "value": "Timer"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}]}], "typesListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 15, "min": 1}, {"conditionType": "uniqueElementsBy"}], "values": []}}}, {"name": {"conditions": [], "default": {"value": "Y1"}, "values": [{"label": "", "value": "Y1"}]}, "types": {"type": [{"conditions": [], "requiredValues": [], "values": [{"label": "TXT_APP_Empty", "value": "Empty"}, {"label": "TXT_APP_AvgDepth", "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_CNS", "value": "CNS"}, {"label": "TXT_APP_Consumption", "value": "Consumption"}, {"label": "TXT_APP_CurrentGas", "value": "CurrentGas"}, {"label": "TXT_APP_DiveTime", "value": "DiveTime"}, {"label": "TXT_APP_GasTime", "value": "GasTime"}, {"label": "TXT_APP_MaxDepth", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "TXT_APP_OTU", "value": "OTU"}, {"label": "TXT_APP_PO2", "value": "PO2"}, {"label": "TXT_APP_SetPoint", "value": "SetPoint"}, {"label": "TXT_APP_TankPressure", "value": "TankPressure"}, {"label": "TXT_APP_Temperature", "value": "Temperature"}, {"label": "TXT_APP_Timer", "value": "Timer"}, {"label": "TXT_APP_DiveType", "value": "DiveType"}]}], "typesListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 15, "min": 1}, {"conditionType": "uniqueElementsBy"}], "values": []}}}], "fieldsListAvailableOptions": {"conditions": [{"conditionType": "uniqueElementsBy"}], "values": []}}}], "viewsListAvailableOptions": {"conditions": [{"conditionType": "length", "max": 4, "min": 1}], "values": []}}}, "validModeWithAvailability": {"alarms": {"diveTime": {"enabled": {"available": true, "value": false}, "time": {"available": true, "value": 2700}}, "gasTime": {"enabled": {"available": true, "value": false}, "time": {"available": true, "value": 600}}, "maxDepth": {"depth": {"available": true, "value": 30.0}, "enabled": {"available": true, "value": false}}, "tankPressure": {"enabled": {"available": true, "value": true}, "pressure": {"available": true, "value": 7500000.0}}}, "diving": {"algorithm": {"available": true, "value": "Suunto Fused2 RGBM"}, "altitude": {"available": true, "value": 0}, "ascentStepsEnabled": {"available": true, "value": false}, "conservatism": {"available": true, "value": 0}, "deepStopEnabled": {"available": true, "value": true}, "diveMode": {"available": true, "value": "OC"}, "diveModeFlags": {"gasEdit": {"available": true, "value": false}, "helium": {"available": true, "value": false}, "multiGas": {"available": true, "value": false}, "type": {"available": true, "value": "OC"}}, "diveStyle": {"available": true, "value": "Scuba"}, "fixedPO2": {"enabled": {"available": true, "value": true}, "value": {"available": true, "value": 140000.0}}, "gases": {"gas": [{"id": "123456789", "helium": {"available": false, "value": 0.0}, "oxygen": {"available": true, "value": 0.21}, "po2": {"available": true, "value": 140000.0}, "state": {"available": true, "value": "Primary"}, "tankFillPressure": {"available": false, "value": 20000000.0}, "tankSize": {"available": true, "value": 0.012}, "transmitterID": {"available": true, "value": "456712340987"}, "transmitterName": {"available": true, "value": ""}}]}, "highSetPoint": {"available": false, "value": 130000}, "lastDecoStopDepth": {"available": true, "value": 3.0}, "lowSetPoint": {"available": true, "value": 70000}, "maxEND": {"available": true, "value": 30.0}, "maxGF": {"available": true, "value": 0.7}, "minGF": {"available": true, "value": 0.3}, "safetyStopTime": {"available": true, "value": 180}, "switchHighSetPoint": {"depth": {"available": false, "value": 21.0}, "enabled": {"available": false, "value": false}}, "switchLowSetPoint": {"depth": {"available": true, "value": 4.5}, "enabled": {"available": true, "value": false}}}, "name": {"available": true, "value": "Scuba/OC"}, "notifications": {"available": true, "value": {"depths": {"depth": [{"depth": {"available": false, "value": 3.0}, "state": {"available": false, "value": "Off"}}, {"depth": {"available": false, "value": 5.0}, "state": {"available": false, "value": "Sound"}}, {"depth": {"available": false, "value": 10.0}, "state": {"available": false, "value": "<PERSON><PERSON><PERSON>"}}, {"depth": {"available": false, "value": 15.0}, "state": {"available": false, "value": "Sound+Vibra"}}, {"depth": {"available": false, "value": 20.0}, "state": {"available": false, "value": "Off"}}]}, "surfaceTime": {"enabled": {"available": true, "value": false}, "time": {"available": true, "value": 300}}}}, "views": {"view": [{"fields": {"field": [{"name": {"available": false, "value": "C1.2"}, "type": {"available": false, "value": "Empty"}}]}, "layout": {"available": true, "value": "GraphicDive"}, "selectionFields": {"field": [{"name": {"available": true, "value": "Y1"}, "types": {"type": [{"available": true, "value": "Empty"}]}}, {"name": {"available": true, "value": "B1"}, "types": {"type": [{"available": true, "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"available": true, "value": "Consumption"}]}}]}}, {"fields": {"field": [{"name": {"available": false, "value": "C1.2"}, "type": {"available": false, "value": "Empty"}}]}, "layout": {"available": true, "value": "GraphicCompass"}, "selectionFields": {"field": [{"name": {"available": true, "value": "Y1"}, "types": {"type": [{"available": true, "value": "Empty"}]}}, {"name": {"available": true, "value": "B1"}, "types": {"type": [{"available": true, "value": "A<PERSON>g<PERSON><PERSON><PERSON>"}, {"available": true, "value": "Consumption"}]}}]}}]}}}