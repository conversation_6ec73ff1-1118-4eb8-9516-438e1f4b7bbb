package com.stt.android.divecustomization.customization.ui.displays.main

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun DiveDisplaysFieldDataItem(
    fieldNumber: Int,
    fieldColor: Color,
    text: String,
    isRequiredValue: Boolean,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    showFieldNumbers: Boolean = true
) {
    var localModifier = modifier.height(56.dp)
    onClick?.run { localModifier = modifier.clickable(onClick = this) }

    Row(
        modifier = localModifier
            .padding(start = dimensionResource(CR.dimen.padding))
            .fillMaxWidth(),
        verticalAlignment = CenterVertically
    ) {
        if (showFieldNumbers) {
            Text(
                modifier = Modifier
                    .size(dimensionResource(BaseR.dimen.size_spacing_xlarge))
                    .background(
                        color = fieldColor
                    )
                    .wrapContentSize(Alignment.Center),
                text = fieldNumber.toString(),
                fontSize = 20.sp,
                color = colorResource(CR.color.white)
            )
        }

        // Add the padding only when there's field number to the left, otherwise there's no need
        val fieldTextModifier = if (showFieldNumbers) {
            Modifier.padding(start = dimensionResource(BaseR.dimen.size_spacing_medium))
        } else {
            Modifier
        }

        Text(
            modifier = fieldTextModifier,
            text = text,
            fontSize = 16.sp,
            color = if (isRequiredValue) {
                colorResource(BaseR.color.medium_grey)
            } else {
                colorResource(CR.color.black)
            }
        )
    }
}
