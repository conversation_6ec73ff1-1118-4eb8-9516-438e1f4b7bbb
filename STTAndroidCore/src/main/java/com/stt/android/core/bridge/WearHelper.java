package com.stt.android.core.bridge;

import android.content.Context;
import android.os.SystemClock;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.PendingResult;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.wearable.DataApi;
import com.google.android.gms.wearable.DataItem;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.android.gms.wearable.Wearable;

public class WearHelper {
    public static PendingResult<DataApi.DataItemResult> sendDataItem(
            GoogleApiClient googleApiClient, PutDataMapRequest putDataMapRequest) {
        putDataMapRequest.getDataMap().putLong(WearConstants.NONCE, SystemClock.elapsedRealtimeNanos());
        return Wearable.DataApi.putDataItem(googleApiClient, putDataMapRequest.asPutDataRequest().setUrgent());
    }

    public static Task<DataItem> sendDataItem(Context context, PutDataMapRequest putDataMapRequest) {
        putDataMapRequest.getDataMap().putLong(WearConstants.NONCE, SystemClock.elapsedRealtimeNanos());
        return Wearable.getDataClient(context).putDataItem(putDataMapRequest.asPutDataRequest().setUrgent());
    }
}
