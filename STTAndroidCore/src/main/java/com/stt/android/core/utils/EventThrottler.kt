package com.stt.android.core.utils

/**
 * Helper for discarding events that happen within intervalMillis of previous accepted event.
 * Most notable use case is throttling click events, see ThrottlingOnClickListener.
 */
class EventThrottler @JvmOverloads constructor(
    private val throttleMillis: Long = DEFAULT_THROTTLE_MILLIS,
    private val timeProvider: TimeProvider = AndroidTimeProvider()
) {
    private var previousAcceptedEventTime: Long? = null

    /**
     * @return true if the event was accepted and throttling timer started
     */
    fun checkAcceptEvent(): Boolean {
        val now = timeProvider.elapsedRealtime()
        val previous = previousAcceptedEventTime
        return if (previous == null || (now - previous > throttleMillis)) {
            previousAcceptedEventTime = now
            true
        } else {
            false
        }
    }

    /**
     * @return true if the event was processed and throttling timer started
     */
    fun processEvent(event: () -> Unit): Boolean {
        return if (checkAcceptEvent()) {
            event.invoke()
            true
        } else {
            false
        }
    }

    companion object {
        const val DEFAULT_THROTTLE_MILLIS = 400L
    }
}

/**
 * Syntax sugar.
 *
 * Note: with Modifier.clickable use it as
 *   Modifier.clickable(onClick = eventThrottler.onClick { onSelectNetwork(networkInfo) })
 */
fun EventThrottler.onClick(onClick: () -> Unit): () -> Unit {
    return { this.processEvent { onClick() } }
}
