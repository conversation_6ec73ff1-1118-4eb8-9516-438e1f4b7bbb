package com.suunto.connectivity.suuntoconnectivity.ble.event;

import android.bluetooth.BluetoothGattService;

public class BleGattServerServiceAddedEvent extends BleGattServerEvent {

    private final BluetoothGattService service;

    public BleGattServerServiceAddedEvent(int status, BluetoothGattService service) {
        super(null, status); // No device information for this event.

        this.service = service;
    }

    public BluetoothGattService getService() {
        return service;
    }
}
