package com.suunto.connectivity.trenddata

import com.movesense.mds.MdsResponse
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource
import com.stt.android.utils.toV1
import com.stt.android.utils.toV2
import com.stt.android.utils.traceCompletableV1
import com.suunto.connectivity.STATUS_CONTINUE
import com.suunto.connectivity.STATUS_NEXT_PAGE
import com.suunto.connectivity.STATUS_NO_CONTENT
import com.suunto.connectivity.STATUS_OK
import com.suunto.connectivity.repository.SyncResult
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.sync.WatchSynchronizeException
import com.suunto.connectivity.watch.SpartanBt
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.SpartanSynchronizer
import com.suunto.connectivity.watch.SyncStepResult
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.rx2.rxSingle
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong

/**
 * Resource to query trend data, i.e.: steps and energy.
 * This logic in this resource for fetching the data is described in this
 * sequence diagram: https://goo.gl/tq3ExX
 */
class TrendDataResource(
    private val spartan: SpartanBt,
    private val synchronizerStorage: SynchronizerStorage,
    private val moshi: Moshi,
    private val timelineResourceLocalDataSource: TimelineResourceLocalDataSource,
    private val weChatTimelineResourceLocalDataSource: WeChatTimelineResourceLocalDataSource
) {

    private val macAddress = spartan.suuntoBtDevice.macAddress
    private var lastEntryTimestamp = synchronizerStorage.getLastTrendDataTimestamp(macAddress)

    fun sync(serial: String, builder: SpartanSyncResult.Builder): rx.Completable {
        val syncStartTimestamp = AtomicLong()
        return fetchTrendData(lastEntryTimestamp)
            .doOnSubscribe {
                syncStartTimestamp.set(System.currentTimeMillis())
            }
            .doOnSuccess { (_, didStoreAny) ->
                val syncDuration = System.currentTimeMillis() - syncStartTimestamp.get()
                if (didStoreAny) {
                    builder.trendDataResult(
                        SyncStepResult(
                            syncResult = SyncResult.success(),
                            syncDuration = syncDuration
                        )
                    )
                } else {
                    builder.trendDataResult(
                        SyncStepResult(
                            syncResult = SyncResult.alreadySynced(),
                            syncDuration = syncDuration
                        )
                    )
                }
            }
            .ignoreElement()
            .doOnError { throwable ->
                Timber.w(throwable, "Trend data sync failed")
                builder.trendDataResult(
                    SyncStepResult(
                        syncResult = SyncResult.failed(throwable),
                        syncDuration = System.currentTimeMillis() - syncStartTimestamp.get()
                    )
                )
            }.toV1()
            .traceCompletableV1("SyncTrendData")
    }

    private fun fetchTrendData(
        timestampInSeconds: Long,
        hasStoredAny: Boolean = false
    ): Single<Pair<MdsResponse, Boolean>> {
        Timber.d("Fetching trend data, NewerThan: $timestampInSeconds")
        return spartan.fetchTrendData(timestampInSeconds).toV2()
            .observeOn(Schedulers.io())
            .flatMap { mdsResponse ->
                if (mdsResponse.body.isNullOrEmpty()) {
                    Single.just(false)
                } else {
                    val entries = moshi.adapter<List<SuuntoTrendDataEntry>>(
                        Types.newParameterizedType(
                            List::class.java,
                            SuuntoTrendDataEntry::class.java
                        )
                    ).fromJson(mdsResponse.body)?.map {
                        val variantName =
                            spartan.suuntoBtDevice?.deviceType?.let { deviceType ->
                                SuuntoDeviceType.getVariantName(
                                    deviceType
                                )
                            }
                        it.copy(
                            serial = spartan.serial,
                            variantName = variantName ?: "",
                            timestamp = it.timestamp * 1000 // Returned timestamps are in seconds
                        )
                    } ?: throw WatchSynchronizeException(SpartanSynchronizer.MDS_RESPONSE_ERROR)
                    Timber.d("Fetching trend data returned ${entries.size} entries")
                    updateLastEntryTimestamp(entries)
                    rxSingle {
                        weChatTimelineResourceLocalDataSource.storeTrendDataEntries(entries)
                        timelineResourceLocalDataSource.storeTrendDataEntries(entries)
                    }
                }.map { didStore -> mdsResponse to (didStore || hasStoredAny) }
            }.flatMap { (mdsResponse, trendDataStored) ->
                when (mdsResponse.statusCode) {
                    STATUS_NO_CONTENT -> {
                        Timber.d("No trend data content")
                        Single.just(mdsResponse to trendDataStored)
                    }

                    STATUS_OK -> {
                        Timber.d("Got all trend data pages")
                        Single.just(mdsResponse to trendDataStored)
                    }
                    // It seems SDS will not return neither of these two ever. Keeping them for now
                    // just to be safe.
                    STATUS_CONTINUE,
                    STATUS_NEXT_PAGE -> {
                        Timber.d("Getting next trend data page")
                        fetchTrendData(lastEntryTimestamp, trendDataStored)
                    }

                    else -> {
                        Timber.w("WatchSynchronizeException with SDS statusCode:${mdsResponse.statusCode}")
                        throw WatchSynchronizeException(SpartanSynchronizer.MDS_RESPONSE_ERROR)
                    }
                }
            }.subscribeOn(Schedulers.io())
    }

    private fun updateLastEntryTimestamp(entries: List<SuuntoTrendDataEntry>) {
        if (entries.isEmpty()) return

        Timber.d("Updating Trend Data last entry timestamp")
        // The following only applies if SDS ever return 100 or 202 status codes:
        // ESW does not guarantee chronological data. Timestamps might be inconsistent if
        // battery has run out or time is otherwise set backwards. Therefore the data should
        // be always queried with the timestamp of the last entry without sorting.
        lastEntryTimestamp = entries.last().timestamp / 1000 // Convert to seconds
        synchronizerStorage.storeLastTrendDataTimestamp(macAddress, lastEntryTimestamp)
    }

    fun getSyncState(): SyncState = SyncState(SyncState.SYNCING_TREND_DATA)
}
