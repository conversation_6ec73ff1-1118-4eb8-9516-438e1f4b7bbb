package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import com.suunto.connectivity.repository.stateMachines.base.Transition;

/**
 * Temporary unpairing state which will immediately exit.
 */
public class UnpairingState extends ConnectionStateBase {
    UnpairingState() {
        super(States.Unpairing.name());
    }

    @Override
    protected void onEntry(Transition transition) {
        super.onEntry(transition);
        stateMachine().fireNext(Triggers.Unpaired);
    }
}
