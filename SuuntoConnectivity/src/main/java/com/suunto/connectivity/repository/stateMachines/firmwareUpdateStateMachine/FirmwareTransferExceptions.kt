package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import com.movesense.mds.MdsException

class FirmwareTransferStartException(
    message: String,
    val error: ErrorCode
) : Exception("ErrorCode:$error Message:$message") {

    enum class ErrorCode(val errorCode: Int) {
        IMAGE_FILE_NOT_SOF(400),
        DEVICE_NOT_CONNECTED(404),
        REQUEST_URL_INVALID(405),
        DEVICE_IS_BUSY(409),
        INTERNAL_ERROR(500),
        FAILED_TO_COMMUNICATE_WITH_DEVICE(503);

        companion object {
            fun getByValue(errorCode: Int) = values().firstOrNull {
                it.errorCode == errorCode
            }
        }
    }

    companion object {
        fun createIfKnownException(throwable: Throwable): FirmwareTransferStartException? {
            return if (throwable is MdsException) {
                throwable.statusCode?.let { statusCode ->
                    ErrorCode.getByValue(statusCode)?.let { error ->
                        FirmwareTransferStartException(throwable.message.orEmpty(), error)
                    }
                }
            } else {
                null
            }
        }
    }
}

class FirmwareTransferException(message: String, val error: ErrorCode) :
    Exception("ErrorCode:$error Message:$message") {

    enum class ErrorCode(val errorCode: Int) {
        BAD_REQUEST(400),
        SERVICE_UNAVAILABLE(503),
        INSUFFICIENT_STORAGE(507),
        INTERNAL_SERVER_ERROR(500);

        companion object {
            fun getByValue(errorCode: Int) = values().firstOrNull {
                it.errorCode == errorCode
            }
        }
    }

    companion object {
        fun createIfKnownException(message: String?, errorCode: Int): FirmwareTransferException? {
            return ErrorCode.getByValue(errorCode)?.let { error ->
                FirmwareTransferException(message.orEmpty(), error)
            }
        }
    }
}
