package com.suunto.connectivity.widget

import android.os.Bundle
import com.stt.android.utils.toV1
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.ArgumentKeys
import kotlinx.coroutines.rx2.rxSingle
import rx.Observable

class SetWidgetsResponseProducer(
    private val widgetsWatchApi: WidgetsWatchApi
) : SuuntoResponseProducer<SetWidgetsResponse> {
    override fun isRelated(messageType: Int): Boolean =
        messageType == SuuntoRepositoryService.MSG_SET_WIDGETS

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out SetWidgetsResponse> {
        val query = bundle.getParcelable<SetWidgetsQuery>(ArgumentKeys.ARG_DATA)
            ?: return Observable.just(SetWidgetsResponse(isSuccessful = false))

        return rxSingle {
            widgetsWatchApi.putWatchWidgets(
                query.serial,
                query.widgets,
                query.widgetVersion
            )
        }.toV1().toObservable()
            .map {
                SetWidgetsResponse(isSuccessful = true)
            }
            .onErrorReturn {
                SetWidgetsResponse(isSuccessful = false, errorMessage = it.message)
            }
    }
}
