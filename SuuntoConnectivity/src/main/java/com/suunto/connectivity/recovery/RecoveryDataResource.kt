package com.suunto.connectivity.recovery

import com.movesense.mds.MdsResponse
import com.squareup.moshi.Moshi
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.utils.toV1
import com.stt.android.utils.toV2
import com.stt.android.utils.traceCompletableV1
import com.suunto.connectivity.STATUS_CONTINUE
import com.suunto.connectivity.STATUS_OK
import com.suunto.connectivity.repository.SyncResult
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.sync.WatchSynchronizeException
import com.suunto.connectivity.watch.SpartanBt
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.SpartanSynchronizer
import com.suunto.connectivity.watch.SyncStepResult
import com.suunto.connectivity.watch.WatchSynchronizerBase.MDS_RESPONSE_INVALID_TIMESTAMP
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.rx2.rxSingle
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong

/**
 * Get stress & recovery data. Shown as "Resources" in watch UI and called "moments" on the watch
 * side implementation.
 */
class RecoveryDataResource(
    private val spartan: SpartanBt,
    private val synchronizerStorage: SynchronizerStorage,
    private val moshi: Moshi,
    private val timelineResourceLocalDataSource: TimelineResourceLocalDataSource
) {

    private val macAddress = spartan.suuntoBtDevice.macAddress

    // Milliseconds since epoch UTC
    private var lastEntryTimestamp = synchronizerStorage.getLastRecoveryDataTimestamp(macAddress)

    fun sync(serial: String, builder: SpartanSyncResult.Builder): rx.Completable {
        val syncStartTimestamp = AtomicLong()
        return fetchRecoveryData(lastEntryTimestamp)
            .doOnSubscribe { syncStartTimestamp.set(System.currentTimeMillis()) }
            .doOnSuccess { (_, didStoreAny) ->
                val syncDuration = System.currentTimeMillis() - syncStartTimestamp.get()
                if (didStoreAny) {
                    builder.recoveryDataResult(
                        SyncStepResult(
                            syncResult = SyncResult.success(),
                            syncDuration = syncDuration
                        )
                    )
                } else {
                    builder.recoveryDataResult(
                        SyncStepResult(
                            SyncResult.alreadySynced(),
                            syncDuration = syncDuration
                        )
                    )
                }
            }
            .ignoreElement()
            .doOnError { throwable ->
                Timber.w(throwable, "Recovery data sync failed")
                builder.recoveryDataResult(
                    SyncStepResult(
                        syncResult = SyncResult.failed(throwable),
                        syncDuration = System.currentTimeMillis() - syncStartTimestamp.get()
                    )
                )
            }.toV1()
            .traceCompletableV1("SyncRecoveryData")
    }

    private fun fetchRecoveryData(timestampMillis: Long, hasStoredAny: Boolean = false): Single<Pair<MdsResponse, Boolean>> {
        Timber.w("fetchRecoveryData timestampMillis=$timestampMillis")
        // Moments API uses seconds based timestamps, but lastEntryTimestamp is in milliseconds
        // to be consistent with sleep and trend data.
        val timestampSeconds = TimeUnit.MILLISECONDS.toSeconds(timestampMillis)
        return spartan.fetchRecoveryData(timestampSeconds).toV2()
            .observeOn(Schedulers.io())
            .flatMap { mdsResponse ->
                val content = moshi.adapter(SuuntoRecoveryDataResponse::class.java)
                    .fromJson(mdsResponse.body)?.content
                val entries = content?.entries?.map { it.copy(serial = spartan.serial) }
                if (content == null) {
                    Timber.w("Syncing with old ESW version, no recovery data")
                    Single.just(mdsResponse to hasStoredAny)
                } else {
                    if (entries != null) updateLastEntryTimestamp(entries)
                    rxSingle {
                        val didStore = if (entries != null) {
                            timelineResourceLocalDataSource.storeRecoveryEntries(entries)
                        } else {
                            false
                        }
                        hasStoredAny || didStore
                    }.flatMap { recoveryDataStored ->
                        when (mdsResponse.statusCode) {
                            STATUS_OK -> {
                                Timber.d("Got all Recovery pages")
                                Single.just(mdsResponse to recoveryDataStored)
                            }
                            STATUS_CONTINUE -> {
                                val continueTimestamp = content.continueTimestamp
                                if (continueTimestamp != null) {
                                    handleContinueResponse(timestampSeconds, continueTimestamp, recoveryDataStored)
                                } else {
                                    Timber.w("Expecting continueTimestamp but was null instead")
                                    Single.error(WatchSynchronizeException(SpartanSynchronizer.MDS_RESPONSE_ERROR))
                                }
                            }
                            else -> Single.error(WatchSynchronizeException(SpartanSynchronizer.MDS_RESPONSE_ERROR))
                        }
                    }
                }
            }.subscribeOn(Schedulers.io())
    }

    private fun handleContinueResponse(
        previousTimestampSeconds: Long,
        continueTimestampSeconds: Long,
        hasStoredAny: Boolean
    ): Single<Pair<MdsResponse, Boolean>> {
        Timber.d("Got 100 CONTINUE response, content.continueTimestamp=$continueTimestampSeconds")
        val timestampValid = continueTimestampSeconds > previousTimestampSeconds &&
            continueTimestampSeconds in VALID_CONTINUE_TIMESTAMP_RANGE_EPOCH_SECONDS

        return if (timestampValid) {
            // Continue fetching more data
            fetchRecoveryData(
                TimeUnit.SECONDS.toMillis(continueTimestampSeconds),
                hasStoredAny
            )
        } else {
            // Invalid timestamp. Stop recursion to be safe.
            Single.error(
                WatchSynchronizeException(
                    String.format(
                        Locale.US,
                        MDS_RESPONSE_INVALID_TIMESTAMP,
                        continueTimestampSeconds
                    )
                )
            )
        }
    }

    private fun updateLastEntryTimestamp(entries: List<SuuntoRecoveryDataEntry>) {
        Timber.d("Updating Recovery data last entry timestamp")
        if (entries.isNotEmpty()) {
            lastEntryTimestamp = TimeUnit.SECONDS.toMillis(entries.last().timestampSeconds)
            synchronizerStorage.storeLastRecoveryDataTimestamp(macAddress, lastEntryTimestamp)
        } else {
            Timber.d("updateLastEntryTimestamp: entries is empty")
        }
    }

    fun getSyncState(): SyncState = SyncState(SyncState.SYNCING_RECOVERY_DATA)

    companion object {
        // 2010-01-01 to 2200-01-01 seconds since epoch UTC
        private val VALID_CONTINUE_TIMESTAMP_RANGE_EPOCH_SECONDS = 1262304000L..7258118400L
    }
}
