package com.suunto.connectivity.logbook.json

import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.suunto.connectivity.logbook.Logbook
import java.lang.reflect.Type

class LogbookEntrySerializer : JsonSerializer<Logbook.Entry> {
    override fun serialize(
        src: Logbook.Entry?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        val jsonObject = JsonObject()
        src?.let {
            jsonObject.addProperty("Id", src.id)
            jsonObject.addProperty("ModificationTimestamp", src.modificationTimestamp)
            jsonObject.addProperty("Size", src.size)
        }
        return jsonObject
    }
}
