package com.suunto.connectivity.logbook.json;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.suunto.connectivity.logbook.Logbook;

import java.lang.reflect.Type;

/**
 * Gson deserializer for Logbook.Entry
 * <p>
 * This implementation will use LogbookEntryJson concrete class for deserializing entries
 */
public class LogbookEntryDeserializer implements JsonDeserializer<Logbook.Entry> {
    @Override
    public Logbook.Entry deserialize(JsonElement json, Type typeOfT,
        JsonDeserializationContext context) throws JsonParseException {
        return context.deserialize(json, LogbookEntryJson.class);
    }
}
