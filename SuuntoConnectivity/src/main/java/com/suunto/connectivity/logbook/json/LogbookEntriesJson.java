package com.suunto.connectivity.logbook.json;

import com.google.gson.annotations.SerializedName;
import com.suunto.connectivity.logbook.Logbook;

import java.util.List;

/**
 * POJO for receiving a list of Logbook.Entries
 */
@SuppressWarnings({ "UnusedDeclaration" })
public class LogbookEntriesJson {
    @SerializedName("elements")
    private List<Logbook.Entry> elements;

    public List<Logbook.Entry> getEntries() {
        return elements;
    }

    public LogbookEntriesJson(List<Logbook.Entry> elements) {
        this.elements = elements;
    }
}
