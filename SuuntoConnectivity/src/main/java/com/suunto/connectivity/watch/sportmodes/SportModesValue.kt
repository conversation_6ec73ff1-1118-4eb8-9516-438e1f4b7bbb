package com.suunto.connectivity.watch.sportmodes

import rx.Single

/**
 * Single sport modes type value that can be read from the device
 *
 * @param <T> Type of the value that is stored
</T> */
interface SportModesValue<T> {

    /**
     * Starts the asynchronous operation to get the sport modes value from the device
     *
     *
     * Returned Single will not emit null values. Instead it will throw any non existing
     * value as SdsMissingValue exception
     *
     * @return Single that will emit the sport modes value
     */
    val value: Single<T>

    /**
     * Instantly returns last fetched value for this sport modes type. GetValue() or
     * will update internally cached value if operation
     * succeeds.
     *
     * @return Last fetched value or null, if no cached value exists
     */
    val cachedValue: T?

    /**
     * Clears cached value to allow GC
     */
    fun clearCachedValue()

    /**
     * Starts the asynchronous operation to get the sport modes value from the device
     *
     *
     * Returned Single will not emit null values. Instead it will throw any non existing
     * value as SdsMissingValue exception
     *
     * @return Single that will emit the sport mode value as raw json
     */
    val rawJsonString: Single<String>

    /**
     * Starts the asynchronous operation to get the sport modes value from the device
     *
     *
     * Returned Single will not emit null values. Instead it will throw any non existing
     * value as SdsMissingValue exception
     *
     * @return Single that will emit the sport mode displays value as raw json
     */
    fun rawValueForId(id: Int): Single<String>

    /**
     * Starts the asynchronous operation to put the sport modes value into the device
     *
     * @return Single with result
     */
    fun putRawValueForId(id: Int, json: String): Single<String>

    /**
     * Starts the asynchronous operation to put the sport modes value into the device
     *
     * @return Single with result
     */
    fun postRawValueForId(id: Int, json: String): Single<String>

    /**
     * Starts the asynchronous operation to delete the sport modes value from the device
     *
     * @return Single with result
     */
    fun deleteValueForId(id: Int): Single<String>
}
