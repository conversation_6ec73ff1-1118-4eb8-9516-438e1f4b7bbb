package com.suunto.connectivity.runsportmodes

import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.runsportmodes.entities.Challenge
import com.suunto.connectivity.runsportmodes.entities.SportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.TrainingModeHeaderEntity
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryException
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.Response
import rx.Single

class RunSportModesConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {

    private val relatedClass = listOf(
        GetRecentSportsResponse::class.java,
        GetAllSportsResponse::class.java,
        GetTrainingModeHeaderListResponse::class.java,
        GetTrainingModeListResponse::class.java,
        TrainingModeResponse::class.java,
        SportModesSimpleResponse::class.java,
        GetDataScreenResponse::class.java,
    )

    override fun isResponseRelated(response: Response): Boolean {
        return response::class.java in relatedClass
    }

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*>? {
        return ResponseMessage(messageId, response)
    }

    fun getRecentSportIds(macAddress: String): Single<List<Int>> {
        return sendQuery<GetRecentSportsResponse>(GetRecentSportsQuery(macAddress))
            .map { response ->
                response.arrayData.filter { it.lastUseTime > 0 }
                    .sortedByDescending { it.lastUseTime }.map { it.sportId }
            }
    }

    fun getAllSportHeaders(macAddress: String): Single<List<SportHeaderEntity>> {
        return sendQuery<GetAllSportsResponse>(GetAllSportsQuery(macAddress)).map { it.arrayData }
    }

    fun getDefaultTrainingModeList(
        macAddress: String, sportId: Int
    ): Single<List<TrainingModeHeaderEntity>> {
        return sendQuery<GetTrainingModeHeaderListResponse>(
            GetTrainingModeTemplateListQuery(
                macAddress, SportItemContract(sportId)
            )
        ).map { it.arrayData }
    }

    fun getTrainingModeList(
        macAddress: String, sportId: Int, lastModeId: Int?
    ): Single<List<TrainingModeHeaderEntity>> {
        return sendQuery<GetTrainingModeListResponse>(
            GetTrainingModeListQuery(
                macAddress, SportItemContract(sportId, lastModeId)
            )
        ).map { it.arrayData }
    }

    fun getDefaultTrainingMode(
        macAddress: String, sportId: Int, trainingModeId: Int
    ): Single<TrainingModeResponse> {
        return sendQuery<TrainingModeResponse>(
            GetTrainingModeTemplateQuery(
                macAddress, TrainingModeItemContract(sportId, trainingModeId)
            )
        )
    }

    fun getTrainingMode(
        macAddress: String, sportId: Int, trainingModeId: Int
    ): Single<TrainingModeResponse> {
        return sendQuery<TrainingModeResponse>(
            GetTrainingModeQuery(macAddress, TrainingModeItemContract(sportId, trainingModeId))
        )
    }

    fun postTrainingMode(
        macAddress: String, sportId: Int, trainingMode: TrainingModeResponse
    ): Single<Boolean> {
        return sendSimpleQuery(
            PostTrainingModeQuery(
                macAddress, SaveTrainingModeContract(sportId, trainingMode)
            )
        )
    }

    fun putTrainingMode(
        macAddress: String, sportId: Int, trainingMode: TrainingModeResponse
    ): Single<Boolean> {
        return sendSimpleQuery(
            PutTrainingModeQuery(
                macAddress, SaveTrainingModeContract(sportId, trainingMode)
            )
        )
    }

    fun deleteTrainingMode(macAddress: String, sportId: Int, modeId: Int): Single<Boolean> {
        return sendSimpleQuery(
            DelTrainingModeQuery(
                macAddress, TrainingModeItemContract(sportId, modeId)
            )
        )
    }

    fun getDefaultDataScreenList(
        macAddress: String, sportId: Int, modeBaseId: Int
    ): Single<GetDataScreenResponse> {
        return sendQuery<GetDataScreenResponse>(
            GetDataScreenTemplateListQuery(
                macAddress, TrainingModeBaseContract(sportId, modeBaseId)
            )
        )
    }

    fun getDataScreenList(
        macAddress: String, sportId: Int, modeId: Int
    ): Single<GetDataScreenResponse> {
        return sendQuery<GetDataScreenResponse>(
            GetDataScreenListQuery(
                macAddress, TrainingModeItemContract(sportId, modeId)
            )
        )
    }

    fun postDataScreen(
        macAddress: String, sportId: Int, modeId: Int, dataScreen: GetDataScreenResponse
    ): Single<Boolean> {
        return sendSimpleQuery(
            PostDataScreenQuery(
                macAddress, DataScreenContract(sportId, modeId, dataScreen)
            )
        )
    }

    fun putDataScreen(
        macAddress: String, sportId: Int, modeId: Int, dataScreen: GetDataScreenResponse
    ): Single<Boolean> {
        return sendSimpleQuery(
            PutDataScreenQuery(
                macAddress, DataScreenContract(sportId, modeId, dataScreen)
            )
        )
    }

    fun deleteDataScreen(
        macAddress: String, sportId: Int, modeId: Int, dataScreen: GetDataScreenResponse
    ): Single<Boolean> {
        return sendSimpleQuery(
            DelDataScreenQuery(
                macAddress, DataScreenContract(sportId, modeId, dataScreen)
            )
        )
    }

    private inline fun <reified T : Response> sendQuery(
        query: DeviceSpecificQuery
    ): Single<T> {
        return suuntoRepositoryClient.waitForServiceReady()
            .andThen(suuntoRepositoryClient.sendQuery(query).first().toSingle().map { response ->
                if (response is T) {
                    return@map response
                }
                (response as? ErrorResponse)?.cause?.let { error -> throw error }
                throw SuuntoRepositoryException("Invalid response [$response]")
            })
    }

    fun putCompetitionTarget(macAddress: String, challenge: Challenge): Single<Boolean> {
        return sendSimpleQuery(
            PutCompetitionTargetQuery(
                macAddress,
                CompetitionTargetContract(
                    sportId = challenge.sportId,
                    modeId = challenge.modeId,
                    workoutKey = challenge.workoutKey,
                    totalDistance = challenge.totalDistance,
                    avgSpeed = challenge.avgSpeed,
                    maxSpeed = challenge.maxSpeed,
                    maxSpeedTime = challenge.maxSpeedTime,
                    minSpeed = challenge.minSpeed,
                    minSpeedTime = challenge.minSpeedTime,
                    samplesCount = challenge.samplesCount,
                    totalDuration = challenge.totalDuration,
                    userName = challenge.userName,
                    workoutOwner = challenge.workoutOwner,
                )
            )
        )
    }

    fun putCompetitionSamplesTarget(macAddress: String, samples: List<Int>): Single<Boolean> {
        return sendSimpleQuery(
            PutCompetitionSamplesTargetQuery(
                macAddress,
                samples
            )
        )
    }

    private fun sendSimpleQuery(
        query: DeviceSpecificQuery
    ): Single<Boolean> {
        return sendQuery<SportModesSimpleResponse>(query).map { it.isSuccess }
    }
}
