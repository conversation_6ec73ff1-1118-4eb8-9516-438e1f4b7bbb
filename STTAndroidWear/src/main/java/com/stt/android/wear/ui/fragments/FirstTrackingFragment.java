package com.stt.android.wear.ui.fragments;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.google.android.gms.wearable.DataMap;
import com.stt.android.core.R;
import com.stt.android.core.bridge.Encoder;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.core.domain.MeasurementUnit;
import com.stt.android.core.ui.utils.TextFormatter;
import com.stt.android.databinding.FragmentFirstTrackingBinding;
import com.stt.android.wear.ui.utils.AmbientModeHelper;

public class FirstTrackingFragment extends BaseOngoingWorkoutStateFragment {
    private long currentDuration;
    private int normalColor;
    private int autoPausedColor;

    private FragmentFirstTrackingBinding binding;

    public static FirstTrackingFragment newInstance() {
        return new FirstTrackingFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Resources resources = getResources();
        normalColor = resources.getColor(R.color.value);
        autoPausedColor = resources.getColor(R.color.auto_paused);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = FragmentFirstTrackingBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onWorkoutStateChanged(DataMap workoutState, boolean ambientModeOn) {
        if (binding == null) {
            return;
        }

        AmbientModeHelper.updateTextViewPaint(binding.duration, ambientModeOn);
        AmbientModeHelper.updateTextViewPaint(binding.distance, ambientModeOn);
        AmbientModeHelper.updateTextViewPaint(binding.speedPace, ambientModeOn);

        boolean autoPaused = false;
        double distance = 0.0;
        String distanceUnit = "";
        byte speedPaceState = WearConstants.SPEED_PACE_STATE_SPEED;
        double speedPace = 0.0;
        String speedPaceUnit = "";
        if (workoutState != null) {
            byte[] snapshot = workoutState.getByteArray(WearConstants.SNAPSHOT);
            if (snapshot != null) {
                autoPaused = Encoder.decodeAutoPausedFromSnapshot(snapshot);
                currentDuration = Encoder.decodeDurationFromSnapshot(snapshot);
                distance = Encoder.decodeDistanceFromSnapshot(snapshot);
                speedPaceState = Encoder.decodeSpeedPaceStateFromSnapshot(snapshot);
                speedPace = Encoder.decodeSpeedFromSnapshot(snapshot);

                MeasurementUnit measurementUnit =
                    Encoder.decodeMeasurementUnitFromSnapshot(snapshot)
                        == WearConstants.MEASUREMENT_UNIT_METRIC ? MeasurementUnit.METRIC
                        : MeasurementUnit.IMPERIAL;
                distanceUnit = getString(measurementUnit.distanceUnitResId);
                if (speedPaceState == WearConstants.SPEED_PACE_STATE_SPEED) {
                    speedPaceUnit = getString(measurementUnit.speedUnitResId);
                } else {
                    speedPaceUnit = getString(measurementUnit.paceUnitResId);
                }
            }
        }

        int textColor = autoPaused && !ambientModeOn ? autoPausedColor : normalColor;

        binding.duration.setText(
            ambientModeOn ? TextFormatter.formatElapsedTime(currentDuration, true, false)
                : TextFormatter.formatElapsedTime(currentDuration));
        binding.duration.setTextColor(textColor);
        binding.distance.setText(TextFormatter.formatDistance(distance));
        binding.distance.setTextColor(textColor);
        binding.distanceUnit.setText(distanceUnit);

        int unitColor = ambientModeOn ? getResources().getColor(R.color.white)
            : getResources().getColor(R.color.unit);
        binding.distanceUnit.setTextColor(unitColor);

        if (speedPaceState == WearConstants.SPEED_PACE_STATE_SPEED) {
            binding.speedPace.setText(TextFormatter.formatSpeed(speedPace));
        } else {
            binding.speedPace.setText(TextFormatter.formatPace(speedPace));
        }
        binding.speedPace.setTextColor(textColor);
        binding.speedPaceUnit.setText(speedPaceUnit);
        binding.speedPaceUnit.setTextColor(unitColor);
    }
}
