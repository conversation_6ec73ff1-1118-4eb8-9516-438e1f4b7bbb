package com.stt.android.questionnaire.screens

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.questionnaire.QuestionnaireViewModel
import com.stt.android.questionnaire.R
import com.stt.android.questionnaire.widgets.SurveyTag
import com.stt.android.questionnaire.widgets.TagCloudPreviewHelper
import com.stt.android.questionnaire.widgets.TagCloudSurveyScreen
import com.stt.android.questionnaire.widgets.TagCloudSurveyState
import com.stt.android.ui.utils.WindowInfo
import com.stt.android.ui.utils.WindowSizeClass

@Composable
fun MotivationQuestionnaireScreen(
    state: TagCloudSurveyState,
    windowInfo: WindowInfo,
    onTagClick: (SurveyTag) -> Unit,
    onShowAllClick: (Boolean) -> Unit,
    onDone: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    TagCloudSurveyScreen(
        modifier = modifier,
        header = stringResource(id = R.string.motivation_questionnaire_header),
        subHeader = stringResource(id = R.string.questionnaire_sub_header),
        state = state,
        windowInfo = windowInfo,
        onTagClick = onTagClick,
        onShowAllClick = onShowAllClick,
        onDone = onDone,
        onDismiss = onDismiss
    )
}

@Composable
@Preview(device = Devices.DEFAULT, widthDp = 720, heightDp = 320)
private fun MotivationQuestionnaireLandscapePreview() {
    val previewHelper = TagCloudPreviewHelper(QuestionnaireViewModel.getDefaultMotivationState())

    AppTheme {
        MotivationQuestionnaireScreen(
            state = previewHelper.state.value,
            windowInfo = WindowInfo(
                windowWidthSizeClass = WindowSizeClass.Medium,
                windowSize = DpSize(width = 720.dp, 320.dp)
            ),
            onTagClick = previewHelper.onTagClick,
            onShowAllClick = {},
            onDone = {},
            onDismiss = {}
        )
    }
}

@Composable
@Preview
@Preview(device = Devices.DEFAULT, widthDp = 1280, heightDp = 720)
private fun MotivationQuestionnairePreview() {
    val previewHelper = TagCloudPreviewHelper(QuestionnaireViewModel.getDefaultMotivationState())

    AppTheme {
        MotivationQuestionnaireScreen(
            state = previewHelper.state.value,
            windowInfo = WindowInfo(
                windowWidthSizeClass = WindowSizeClass.Medium,
                windowSize = DpSize(width = 1280.dp, 720.dp)
            ),
            onTagClick = previewHelper.onTagClick,
            onShowAllClick = {},
            onDone = {},
            onDismiss = {}
        )
    }
}
