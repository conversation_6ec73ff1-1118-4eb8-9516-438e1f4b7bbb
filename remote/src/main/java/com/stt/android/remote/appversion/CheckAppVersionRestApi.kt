package com.stt.android.remote.appversion

import com.stt.android.remote.response.ChinaResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface CheckAppVersionRestApi {
    @GET("appstore/app/version/v1/update/check")
    suspend fun checkAppVersion(
        @Query("appVersionCode") appVersionCode: String,
        @Query("innerAppVersionCode") innerAppVersionCode: Int,
        @Query("systemVersionCode") systemVersionCode: String,
        @Query("mobilePhoneBrand") mobilePhoneBrand: String,
        @Query("packageName") packageName: String?,
        @Query("brand") appBrand: String
    ): ChinaResponse<AppUpgradeInfo?>

    @GET("/appstore/app/version/v1/list?systemType=ANDROID")
    suspend fun getVersionList(
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
        @Query("brandType") brandType: String,
    ): ChinaResponse<List<AppVersionInfo>>

    @GET("/appstore/app/version/v1/getVersionNotes?systemType=ANDROID")
    suspend fun getVersionNotes(
        @Query("innerAppVersionCode") innerAppVersionCode: Int,
        @Query("brandType") brandType: String,
    ): ChinaResponse<String>
}
