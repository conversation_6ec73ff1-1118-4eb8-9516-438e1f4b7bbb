package com.stt.android.remote.report

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class ReportedWorkout(
    @Json(name = "id") val workoutKey: String,
    @<PERSON><PERSON>(name = "type") val type: String,
    @<PERSON><PERSON>(name = "reason") val reason: String
)

@JsonClass(generateAdapter = true)
data class ReportedUser(
    @Json(name = "blockedusername") val username: String,
    @<PERSON><PERSON>(name = "type") val type: String,
    @<PERSON><PERSON>(name = "reason") val reason: String
)
