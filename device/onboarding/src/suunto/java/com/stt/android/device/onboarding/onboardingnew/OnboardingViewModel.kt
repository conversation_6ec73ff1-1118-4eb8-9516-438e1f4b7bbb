package com.stt.android.device.onboarding.onboardingnew

import android.content.SharedPreferences
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.onboarding.BaseOnboardingActivity
import com.stt.android.device.onboarding.DeviceOnboardingAnalytics
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.sleep.SleepTrackingMode
import com.stt.android.domain.sleep.SleepTrackingSettingsRepository
import com.stt.android.domain.trenddata.TrendDataSettingsRepository
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.widget.WidgetVersion
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

// Interface helps with Compose Previews
interface IOnboardingViewModel {
    fun onPageChanged(newPage: Int, oldPage: Int)
    fun onCloseOnboardingClicked(pageName: String?, delay: Boolean)
    fun onStartClicked()
    fun onQuestionnaireOpened()
    fun onWatchWidgetCustomizationOpened()
    fun onVideoEndReached(pageName: String)
    fun setOngoingPageNavigationMethod(method: String)
    fun onWifiSetupOpened()
    fun onSleepTrackingEnabled(enabled: Boolean)
    fun on247HrEnabled()
}

@HiltViewModel
class OnboardingViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val suuntoWatchModel: SuuntoWatchModel,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
    private val onboardingAnalytics: DeviceOnboardingAnalytics,
    private val sleepTrackingSettingsRepository: SleepTrackingSettingsRepository,
    private val trendDataRepository: TrendDataSettingsRepository,
    private val checkFirstOpenOnboardingUseCase: ShouldAutoDisable247HrUseCase
) : ViewModel(), IOnboardingViewModel {
    private val suuntoDeviceType: SuuntoDeviceType =
        savedStateHandle[BaseOnboardingActivity.KEY_EXTRA_SUUNTO_DEVICE_TYPE]
            ?: SuuntoDeviceType.Unrecognized

    private var ongoingNavigationMethod: String? = null

    private var wifiSetupOpened = false

    private var enable247HrStarted = false

    val watchConnected = suuntoWatchModel
        .stateChangeObservable
        .toV2Flowable()
        .asFlow()
        .map { it.isConnected }
        .catch { emit(false) }
        .distinctUntilChanged()
        .stateIn(viewModelScope, SharingStarted.Lazily, false)

    private var update247HrJob: Job? = null
    private var updateSleepJob: Job? = null

    private val _uiState: MutableStateFlow<OnboardingUIState> =
        MutableStateFlow(OnboardingUIState())
    val uiState: StateFlow<OnboardingUIState> = _uiState.asStateFlow()

    private val widgetSupportOverride = featureTogglePreferences.getBoolean(
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY,
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT
    )

    val diluOnboardingWatchFacesEnabled = featureTogglePreferences.getBoolean(
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES,
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
    )

    val supportsWatchWidgets: Flow<Boolean> = suuntoWatchModel
        .supportsWidgets(waitForConnect = true)
        .toFlowable()
        .asFlow()
        .catch { emit(false) }
        .map { supportsWidgets ->
            supportsWidgets || widgetSupportOverride
        }
        .onEach {
            var attempt = 0L
            var shallRetry: Boolean
            var version: WidgetVersion
            do {
                shallRetry = false
                version = try {
                    suuntoWatchModel.getWidgetVersion(false)
                } catch (e: Exception) {
                    shallRetry = true
                    attempt++
                    WidgetVersion.fromValue(null)
                }
            } while (attempt < 3 && shallRetry)

            widgetVersion = version
        }
        .distinctUntilChanged()
        .shareIn(scope = viewModelScope, started = SharingStarted.Lazily, replay = 1)

    val supportsSettingSportlist: Flow<Boolean> = flowOf(
        featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS_DEFAULT
        )
    )

    init {
        autoDisable247HrIfNeeded()
        collectSleepTrackingEnabled()
    }

    private fun autoDisable247HrIfNeeded() {
        if (suuntoDeviceType.isSuuntoRaceS) {
            viewModelScope.launch {
                if (shouldDisable247Hr()) {
                    watchConnected
                        .filter { isConnected -> isConnected }
                        .take(1)
                        .collect {
                            if (!enable247HrStarted) {
                                update247HrState(enabled = false)
                            }
                        }
                }
            }
        }
    }

    private fun collectSleepTrackingEnabled() {
        if (suuntoDeviceType.isSuuntoRaceS || suuntoDeviceType.isSuuntoRun) {
            viewModelScope.launch {
                sleepTrackingSettingsRepository.fetchSleepTrackingMode()
                    .map { it != SleepTrackingMode.OFF }
                    .distinctUntilChanged()
                    .catch { e -> Timber.w(e, "observe sleep tracking state error") }
                    .collect {
                        _uiState.update { currentState ->
                            currentState.copy(
                                sleepTrackEnabled = it
                            )
                        }
                    }
            }
        }
    }

    private suspend fun shouldDisable247Hr(): Boolean {
        val result = runSuspendCatching {
            withContext(IO) {
                val currentWatch = suuntoWatchModel.currentWatch.await()
                val shouldDisable247HrSetting =
                    checkFirstOpenOnboardingUseCase.shouldAutoDisable247Hr(currentWatch.serial)
                shouldDisable247HrSetting
            }
        }
        return result.getOrElse { error ->
            Timber.w(error, "shouldDisable247Hr failed")
            false
        }
    }

    private fun updateSleepTrackingState(enabled: Boolean) {
        if ((suuntoDeviceType.isSuuntoRaceS || suuntoDeviceType.isSuuntoRun) && watchConnected.value) {
            _uiState.update { currentState ->
                currentState.copy(
                    show247Page = !enabled,
                    sleepTrackEnabled = enabled
                )
            }
            // 24/7 HR, HRV, SpO2, sleep tracking
            updateSleepJob?.cancel()
            updateSleepJob = viewModelScope.launch(IO) {
                runSuspendCatching {
                    trendDataRepository.set247HrEnabled(enabled)
                }.onFailure {
                    Timber.w(it, "set247HrEnabled failed")
                }
                runSuspendCatching {
                    sleepTrackingSettingsRepository.setHrvEnabled(enabled)
                }.onFailure {
                    Timber.w(it, "setHrvEnabled failed")
                }
                runSuspendCatching {
                    sleepTrackingSettingsRepository.setSpO2NightEnabled(enabled)
                }.onFailure {
                    Timber.w(it, "setSpO2NightEnabled failed")
                }
                runSuspendCatching {
                    val sleepTrackingMode =
                        if (enabled) SleepTrackingMode.AUTO else SleepTrackingMode.OFF
                    sleepTrackingSettingsRepository.updateSleepTrackingMode(sleepTrackingMode)
                }.onFailure {
                    Timber.w(it, "updateSleepTrackingState failed")
                }
            }
        }
    }

    private fun update247HrState(enabled: Boolean) {
        if (suuntoDeviceType.isSuuntoRaceS && watchConnected.value) {
            update247HrJob?.cancel()
            update247HrJob = viewModelScope.launch(IO) {
                runSuspendCatching {
                    trendDataRepository.set247HrEnabled(enabled)
                }.onFailure {
                    Timber.w(it, "update247HrState failed")
                }
            }
        }
    }

    /**
     * The boolean tells if the closing should have slight delay for animations to finish or not
     */
    val closeOnboardingEvent = SingleLiveEvent<Boolean>()

    var widgetVersion by mutableStateOf(WidgetVersion.fromValue(null))
        private set

    override fun onPageChanged(newPage: Int, oldPage: Int) {
        // Buttons should have reported that they've been pressed by calling
        // [setOngoingPageNavigationMethod]. If the value is null at this point,
        // assume that navigation happened by swiping
        val navigationMethod = ongoingNavigationMethod
            ?: AnalyticsPropertyValue.NavigationMethodProperty.SWIPE

        onboardingAnalytics.trackPageChanged(
            suuntoDeviceType,
            newPage,
            oldPage,
            navigationMethod
        )

        ongoingNavigationMethod = null
    }

    override fun onCloseOnboardingClicked(pageName: String?, delay: Boolean) {
        trackOpenWifiSetup()
        pageName?.let {
            onboardingAnalytics.trackOnboardingFinished(
                suuntoDeviceType,
                it
            )
        }
        closeOnboardingEvent.postValue(delay)
    }

    private fun trackOpenWifiSetup() {
        if (suuntoDeviceType.isSuuntoVertical) {
            onboardingAnalytics.trackOpenWifiSetup(suuntoDeviceType, !wifiSetupOpened)
        }
    }

    override fun onStartClicked() {
        onboardingAnalytics.trackStartClick(suuntoDeviceType)
    }

    override fun onQuestionnaireOpened() {
        onboardingAnalytics.trackOpenQuestionnaire(suuntoDeviceType)
    }

    override fun onWatchWidgetCustomizationOpened() {
        onboardingAnalytics.trackOpenWatchWidgetCustomization(suuntoDeviceType)
    }

    override fun onVideoEndReached(pageName: String) {
        onboardingAnalytics.trackVideoCompleted(suuntoDeviceType, pageName)
    }

    override fun setOngoingPageNavigationMethod(method: String) {
        ongoingNavigationMethod = method
    }

    override fun onWifiSetupOpened() {
        wifiSetupOpened = true
    }

    override fun onSleepTrackingEnabled(enabled: Boolean) {
        updateSleepTrackingState(enabled = enabled)
    }

    override fun on247HrEnabled() {
        enable247HrStarted = true
        update247HrState(enabled = true)
    }
}

data class OnboardingUIState(
    val show247Page: Boolean = true,
    val sleepTrackEnabled: Boolean = false,
)
