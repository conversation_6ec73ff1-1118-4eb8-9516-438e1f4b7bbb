package com.stt.android.device.suuntoplusfeature.settings

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.moshi.IntegerPreservingAdapterFactory
import kotlin.math.roundToInt

/**
 * A 'Settings JSON' is regular JSON document with the following limitations:
 * - Root is always an object
 * - Keys under root have either string or object values
 * - Any nested objects can have arbitrary values
 *
 * This class supports parsing such Settings JSON files and querying and modifying individual values
 * in the content. Modifying is implemented with the [withNewValueForPath] method that returns a new
 * [SettingsJson] instance since the instance itself is immutable.
 */
data class SettingsJson(
    private val map: Map<String, Any?>
) {
    fun asMap(): Map<String, Any?> = map

    fun isEmpty() = map.isEmpty()

    /**
     * Replace a single value in the JSON document with the given [value]. The [keyPath] parameter
     * the defines the location of the value in the document as a dot separate chain of object names.
     * For example, the key 'workout.averageSpeed.goal' refers to the goal value in a JSON document
     * like this '{"workout": {"averageSpeed": {"goal": 3.4}}}'.
     *
     * It is possible to change the type of a value. The old value and type are simply ignored.
     *
     * New objects are implicitly created when [keyPath] refers to an object that does not already
     * exist. It is not supported to refer to an array index when modifying content.
     */
    fun withNewValueForPath(keyPath: String, value: Any?): SettingsJson {
        var index: Int? = null
        val keyPathWithoutIndex = if (keyPath.endsWith(']')) {
            index = keyPath.takeLastWhile { it != '[' }.dropLast(1).toInt()
            keyPath.dropLastWhile { it != '[' }.dropLast(1)
        } else {
            keyPath
        }
        return if (keyPathWithoutIndex.isRootPath) {
            SettingsJson(
                map.withNewValueForPath(listOf(keyPathWithoutIndex), value.toString(), index)
            )
        } else {
            SettingsJson(
                map.withNewValueForPath(keyPathWithoutIndex.split("."), value, index)
            )
        }
    }

    /**
     * Get the size of an array in the JSON document located at [keyPath]. Returns null if [keyPath]
     * refers to an non-existing location or the type of the referred location is not an array.
     */
    fun arraySizeForPath(keyPath: String): Int? = (valueForPath(keyPath) as? List<*>)?.size

    /**
     * Get the integer value at the given [keyPath] or null if the value does not exist or is not
     * an integer.
     *
     * If the key ends with an index in square brackets ("option.values[3]") then it refers to a
     * 0 based index in an array. Array indexing is supported only for the last element of the key
     * path.
     */
    fun integerForPath(keyPath: String) = if (keyPath.isRootPath) {
        stringForPath(keyPath)?.toIntOrNull() ?: stringForPath(keyPath)?.toDoubleOrNull()?.roundToInt()
    } else {
        (valueForPath(keyPath) as? Int) ?: (valueForPath(keyPath) as? Double)?.roundToInt()
    }

    /**
     * Get the floating point value at the given [keyPath] or null if the value does not exist or is
     * not a floating point value.
     *
     * If the key ends with an index in square brackets ("option.values[3]") then it refers to a
     * 0 based index in an array. Array indexing is supported only for the last element of the key
     * path.
     */
    fun doubleForPath(keyPath: String) = if (keyPath.isRootPath) {
        stringForPath(keyPath)?.toDoubleOrNull()
    } else {
        (valueForPath(keyPath) as? Double) ?: (valueForPath(keyPath) as? Int)?.toDouble()
    }

    /**
     * Get the boolean value at the given [keyPath] or null if the value does not exist or is
     * not a boolean value.
     *
     * If the key ends with an index in square brackets ("option.values[3]") then it refers to a
     * 0 based index in an array. Array indexing is supported only for the last element of the key
     * path.
     */
    fun booleanForPath(keyPath: String) = if (keyPath.isRootPath) {
        stringForPath(keyPath)?.toBooleanStrictOrNull()
    } else {
        valueForPath(keyPath) as? Boolean
    }

    /**
     * Get the string value at the given [keyPath] or null if the value does not exist or is not
     * a string.
     *
     * If the key ends with an index in square brackets ("option.values[3]") then it refers to a
     * 0 based index in an array. Array indexing is supported only for the last element of the key
     * path.
     */
    fun stringForPath(keyPath: String) = valueForPath(keyPath) as? String

    private fun valueForPath(keyPath: String): Any? {
        var index: Int? = null
        val keyWithoutIndex = if (keyPath.endsWith(']')) {
            index = keyPath.takeLastWhile { it != '[' }.dropLast(1).toInt()
            keyPath.dropLastWhile { it != '[' }.dropLast(1)
        } else {
            keyPath
        }

        var current: Any? = map
        for (key in keyWithoutIndex.split(".")) {
            current = (current as? Map<*, *>)?.get(key)
        }

        if (index != null) {
            current = (current as? List<*>)?.get(index)
        }

        return current
    }

    fun asJson(): String {
        val moshi = Moshi.Builder().build()
        val adapter: JsonAdapter<Map<String, Any?>> = moshi.adapter(
            Types.newParameterizedType(
                Map::class.java,
                String::class.java,
                Any::class.java
            )
        )
        return adapter.serializeNulls().indent("  ").toJson(map)
    }

    companion object {
        fun parse(json: String): SettingsJson {
            val moshi = Moshi.Builder()
                .add(IntegerPreservingAdapterFactory())
                .build()
            val adapter: JsonAdapter<Map<String, Any?>> = moshi.adapter(
                Types.newParameterizedType(
                    Map::class.java,
                    String::class.java,
                    Any::class.java
                )
            )

            return adapter.fromJson(json)?.let { SettingsJson(it) }
                ?: throw NullPointerException("Parsing settings JSON returned null! Input: $json")
        }

        fun empty() = parse("{}")
    }

    private val String.isRootPath: Boolean
        get() = !contains('.')
}

private fun Map<String, Any?>.withNewValueForPath(
    keyPath: List<String>,
    newValue: Any?,
    index: Int? = null
): Map<String, Any?> = when {
    // Empty path: no changes to be made
    keyPath.isEmpty() -> this

    // Recursion has reached the level where the key path points and the value can be set directly
    keyPath.size == 1 ->
        toMutableMap().apply {
            val currentValue = this[keyPath.first()]
            if (currentValue is List<*> && index != null) {
                val updatedList = currentValue.toMutableList()
                updatedList[index] = newValue
                this[keyPath.first()] = updatedList
            } else {
                this[keyPath.first()] = newValue
            }
        }

    // Recurse one level deeper in order to find where key path is pointing to
    else ->
        if (!containsKey(keyPath.first())) {
            // Path refers to a non-existing object. Create an empty one and continue recursion
            toMutableMap().apply {
                this[keyPath.first()] = emptyMap<String, Any?>().withNewValueForPath(
                    keyPath = keyPath.drop(1),
                    newValue = if (index == null) {
                        newValue
                    } else {
                        val newList = MutableList<Any?>(index + 1) { "" }
                        newList[index] = newValue
                        newList
                    }
                )
            }
        } else {
            // Pass other values as is, but recurse into any existing nested objects
            mapValues { (key, value) ->
                if (key == keyPath.first() && value is Map<*, *>) {
                    @Suppress("UNCHECKED_CAST")
                    (value as Map<String, Any?>).withNewValueForPath(
                        keyPath = keyPath.drop(1),
                        newValue = newValue,
                        index = index
                    )
                } else {
                    value as Any
                }
            }
        }
}
