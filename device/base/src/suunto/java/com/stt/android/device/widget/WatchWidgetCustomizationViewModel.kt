@file:OptIn(FlowPreview::class)

package com.stt.android.device.widget

import android.content.SharedPreferences
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.widget.GetCurrentWatchWidgetsUseCase
import com.stt.android.device.domain.widget.SetWatchWidgetsUseCase
import com.stt.android.device.domain.widget.entities.WatchWidget
import com.stt.android.device.domain.widget.entities.WatchWidgetHelp
import com.stt.android.device.domain.widget.entities.WatchWidgetHelpProvider
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.utils.STTConstants
import com.stt.android.utils.indexOfFirstOrNull
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.widget.WidgetVersion
import com.suunto.connectivity.widget.getEnableWidgetMaxCount
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class WatchWidgetCustomizationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val getCurrentWatchWidgetsUseCase: GetCurrentWatchWidgetsUseCase,
    private val setWatchWidgetsUseCase: SetWatchWidgetsUseCase,
    private val watchWidgetAnalytics: WatchWidgetAnalytics,
    dispatchers: CoroutinesDispatchers,
    @SuuntoSharedPrefs sharedPreferences: SharedPreferences,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : LoadingStateViewModel<ImmutableList<WatchWidget>>(
    ioThread,
    mainThread,
    dispatchers
) {
    enum class SyncStatus {
        IDLE,
        PENDING,
        SYNCING,
        ERROR
    }

    var showSyncingWidgets by mutableStateOf(false)
    var showErrorDialog by mutableStateOf(false)
    var showWidgetLimitDialog by mutableStateOf(false)

    val isWatchConnected: Flow<Boolean> = suuntoWatchModel
        .isConnectedFlow
        .catch { emit(false) }
        .distinctUntilChanged()
        .onEach { isWatchConnected ->
            if (!isWatchConnected) {
                _syncStatus.tryEmit(SyncStatus.ERROR)
            }
        }

    private val _watchWidgetHelpByDevice = MutableLiveData<WatchWidgetHelp?>()
    val watchWidgetHelpByDevice: LiveData<WatchWidgetHelp?>
        get() = _watchWidgetHelpByDevice

    var showWidgetHelpAction by mutableStateOf(false)

    private var setWidgetsJob: Job? = null
    private var hasPendingChanges = false

    private val _syncStatus = MutableStateFlow(SyncStatus.IDLE)
    val syncStatus: StateFlow<SyncStatus> = _syncStatus

    private val _widgetsChanged = MutableSharedFlow<Unit>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    private val _widgetVersion = MutableLiveData<WidgetVersion>()
    val widgetVersion: LiveData<WidgetVersion>
        get() = _widgetVersion

    private var enableWidgetMaxCount: Int = Int.MAX_VALUE

    val fromOnboarding by lazy {
        savedStateHandle.get<Boolean>(ARG_FROM_ONBOARDING) == true
    }

    init {
        val analyticsSource = savedStateHandle.get<String?>(ARG_ANALYTICS_SOURCE)
        // The analytics event is sent only when source is non-null, remove from
        // handle to make sure the event isn't re-send in any situation
        savedStateHandle.remove<String?>(ARG_ANALYTICS_SOURCE)

        load(analyticsSource)
        listenToWidgetChanges()

        sharedPreferences.edit {
            putBoolean(
                STTConstants.SuuntoPreferences.KEY_HAS_OPENED_WATCH_WIDGET_CUSTOMIZATION,
                true
            )
        }

        launch {
            val widgetVersion = suuntoWatchModel.getWidgetVersion(true)
            _widgetVersion.value = widgetVersion
            val watchWidgetHelp = provideWatchWidgetHelp()
            showWidgetHelpAction = when (widgetVersion) {
                WidgetVersion.V2,
                WidgetVersion.V3 -> true
                else -> false
            }
            _watchWidgetHelpByDevice.postValue(watchWidgetHelp)
            val isWidgetCountUnlimited = suuntoWatchModel.isWidgetCountUnlimited(true).await()
            enableWidgetMaxCount = widgetVersion.getEnableWidgetMaxCount(isWidgetCountUnlimited)
        }
    }

    private suspend fun provideWatchWidgetHelp(): WatchWidgetHelp? = runSuspendCatching {
        val deviceType = suuntoWatchModel.currentWatch.await().suuntoBtDevice.deviceType
        WatchWidgetHelpProvider(deviceType).watchWidgetHelp
    }.getOrElse { e ->
        if (e !is MissingCurrentWatchException) {
            Timber.w(e, "Error while fetching current watch")
        }
        null
    }

    /**
     * @param analyticsSource - if non-null sends event of the watch widget customization screen
     *   being opened after current widgets from the watch
     */
    private fun load(analyticsSource: String? = null) {
        val isInitialLoad = viewState.value == null
        notifyLoading(viewState.value?.data)
        launch {
            runSuspendCatching {
                val showProgressIndicatorIfLoadIsSlowJob = if (isInitialLoad) {
                    launch {
                        delay(INITIAL_LOADING_INDICATOR_DELAY)
                        showSyncingWidgets = true
                    }
                } else {
                    null
                }

                val widgets = withContext(io) {
                    getCurrentWatchWidgetsUseCase.getCurrentWatchWidgets()
                        .sortedByDescending { it.enabled }
                }

                showProgressIndicatorIfLoadIsSlowJob?.let {
                    it.cancel()
                    showSyncingWidgets = false
                }

                notifyDataLoaded(widgets.toImmutableList())

                if (analyticsSource != null) {
                    watchWidgetAnalytics.trackWatchEditWidgetsScreen(analyticsSource, widgets)
                }
            }.onFailure { e ->
                notifyError(e, viewState.value?.data)
            }
        }
    }

    fun saveWhenWatchIsConnected() {
        _widgetsChanged.tryEmit(Unit)
    }

    private fun save() {
        val widgets = viewState.value?.data ?: return

        _syncStatus.tryEmit(SyncStatus.SYNCING)
        setWidgetsJob?.cancel()
        setWidgetsJob = launch {
            runSuspendCatching {
                setWatchWidgetsUseCase.setWatchWidgets(widgets)
                hasPendingChanges = false
                if (_syncStatus.value == SyncStatus.SYNCING) {
                    _syncStatus.tryEmit(SyncStatus.IDLE)
                }
            }.onFailure { e ->
                Timber.w(e, "Storing widgets to watch failed.")
                _syncStatus.tryEmit(SyncStatus.ERROR)
            }
        }
    }

    fun waitUntilSaveComplete(
        setShowSyncingWidgetsIfNeeded: Boolean = false,
        onComplete: () -> Unit
    ) {
        if (!hasPendingChanges) {
            onComplete()
            return
        }

        if (setShowSyncingWidgetsIfNeeded) {
            showSyncingWidgets = true
        }

        launch {
            fun onTimeoutOrError() {
                _syncStatus.tryEmit(SyncStatus.ERROR)
                showErrorDialog = true
            }

            try {
                withTimeout(1.minutes) {
                    syncStatus.collect {
                        Timber.d("Widget sync status changed to: $it")
                        if (it == SyncStatus.IDLE) {
                            onComplete()
                        } else if (it == SyncStatus.ERROR) {
                            onTimeoutOrError()
                        }
                    }
                }
            } catch (e: TimeoutCancellationException) {
                onTimeoutOrError()
            }
        }
    }

    private fun listenToWidgetChanges() {
        launch {
            val widgetsChanged = _widgetsChanged
                .onEach { _syncStatus.emit(SyncStatus.PENDING) }
                .debounce(2.seconds) // Wait in case the user makes more changes
                .catch { _syncStatus.tryEmit(SyncStatus.ERROR) }

            combine(
                widgetsChanged,
                isWatchConnected,
                ::Pair
            )
                .filter { it.second }
                .onEach { save() }
                .catch {
                    Timber.w(it, "Storing widgets to watch failed.")
                    notifyError(it, viewState.value?.data)
                }
                .collect()
        }
    }

    @MainThread
    fun setWidgetEnabled(id: String, enabled: Boolean) {
        // when the widget enabled is true, it will be add the last of enabled widgets list.
        this.setWidgetEnableForV3(id, enabled)
    }

    @MainThread
    fun setWidgetEnableForV3(id: String, enabled: Boolean) {
        val currentWidgets = viewState.value?.data?.sortedByDescending { it.enabled } ?: return
        // for widget_v3, the number of open widgets is limited to 7
        if (enabled) {
            val enableWidgetCount = currentWidgets.count { it.enabled }
            showWidgetLimitDialog = enableWidgetCount >= enableWidgetMaxCount
            if (showWidgetLimitDialog) return
        }

        val updateWidgetIndex = currentWidgets.indexOfFirstOrNull { it.id == id } ?: return
        val updatedWidget = currentWidgets[updateWidgetIndex].copy(enabled = enabled)
        val updatedList = currentWidgets.toMutableList().apply {
            val toIndex = if (enabled) {
                this.indexOfLast { it.enabled } + 1
            } else {
                (this.indexOfFirstOrNull { !it.enabled } ?: this.lastIndex) - 1
            }
            add(toIndex, removeAt(updateWidgetIndex))
            set(toIndex, updatedWidget)
        }.toImmutableList()
        notifyDataLoadedImmediately(updatedList)
        hasPendingChanges = true
        _widgetsChanged.tryEmit(Unit)
    }

    @MainThread
    fun moveWidget(fromIndex: Int, toIndex: Int) {
        val currentWidgets = viewState.value?.data ?: return
        if (fromIndex !in 0..currentWidgets.size || toIndex !in 0..currentWidgets.size) return

        val updatedList = currentWidgets.toMutableList().apply {
            add(toIndex, removeAt(fromIndex))
        }.toImmutableList()

        notifyDataLoadedImmediately(updatedList)
        hasPendingChanges = true
        _widgetsChanged.tryEmit(Unit)
    }

    @MainThread
    fun widgetMoveEnded(fromIndex: Int, toIndex: Int) {
        val currentWidgets = viewState.value?.data ?: return
        if (fromIndex == toIndex ||
            fromIndex !in 0..currentWidgets.size ||
            toIndex !in 0..currentWidgets.size
        ) {
            return
        }

        val movedWidget = currentWidgets[toIndex]
        watchWidgetAnalytics.trackWatchWidgetsOrderChanged(movedWidget, toIndex)
    }

    override fun retryLoading() = load()

    companion object {
        const val ARG_ANALYTICS_SOURCE = "analyticsSource"
        const val ARG_FROM_ONBOARDING = "fromOnboarding"

        private const val INITIAL_LOADING_INDICATOR_DELAY = 200L
    }
}
