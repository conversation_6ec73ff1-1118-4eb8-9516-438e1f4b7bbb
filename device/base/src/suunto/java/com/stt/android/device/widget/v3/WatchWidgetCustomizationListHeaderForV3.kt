package com.stt.android.device.widget.v3

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R

@Composable
fun WatchWidgetCustomizationListHeaderForV3(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(all = MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.watch_widget_customization_list_selected),
            style = MaterialTheme.typography.h6
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

        Text(
            text = stringResource(R.string.watch_widget_customization_list_header),
            style = MaterialTheme.typography.bodySmall,
        )
    }
}

@Preview
@Composable
private fun WatchWidgetCustomizationListHeaderForV3Preview() {
    AppTheme {
        WatchWidgetCustomizationListHeaderForV3()
    }
}
