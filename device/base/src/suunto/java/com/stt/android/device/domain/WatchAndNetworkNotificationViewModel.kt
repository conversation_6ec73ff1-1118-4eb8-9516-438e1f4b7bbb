package com.stt.android.device.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.watch.SuuntoPlusWatchStateListener
import com.stt.android.device.watch.SuuntoPlusWatchStateListenerImpl
import com.stt.android.device.watch.WatchBusyState
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.utils.IsInternetAvailableUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject

@HiltViewModel
class WatchAndNetworkNotificationViewModel @Inject constructor(
    isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    isWatchBusyUseCase: IsWatchBusyUseCase,
    isWatchConnectedUseCase: IsWatchConnectedUseCase,
    currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    isInternetAvailableUseCase: IsInternetAvailableUseCase,
) : ViewModel(),
    SuuntoPlusWatchStateListener by SuuntoPlusWatchStateListenerImpl(
        isSyncOngoingUseCase,
        isWatchBusyUseCase
    ) {

    private data class CustomToastData(
        val message: String,
        val actionText: String?,
        val isError: Boolean,
    )

    private val customToastData = MutableStateFlow<CustomToastData?>(null)
    private val customToastMutex = Mutex()

    val watchAndNetworkNotificationState: StateFlow<WatchAndNetworkNotificationState> =
        combine(
            currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow(),
            isWatchConnectedUseCase.isWatchConnected(),
            watchBusyState,
            isInternetAvailableUseCase.isInternetAvailable(),
            customToastData
        ) { capabilitiesResult, watchConnected, busyState, internetAvailable, customToast ->
            WatchAndNetworkNotificationState(
                internetAvailable = internetAvailable,
                watchPaired = capabilitiesResult.serial != null,
                watchBusy = busyState == WatchBusyState.BUSY,
                watchSyncing = busyState == WatchBusyState.SYNC_ONGOING,
                watchDisconnected = !watchConnected,
                areSuuntoPlusGuidesSupported = capabilitiesResult.capabilities?.areSuuntoPlusGuidesSupported,
                customToastMessage = customToast?.message.takeIf { customToast?.isError == false },
                customToastActionText = customToast?.actionText,
                customErrorMessage = customToast?.message.takeIf { customToast?.isError == true }
            )
        }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(),
                initialValue = WatchAndNetworkNotificationState.DEFAULT
            )

    suspend fun showToastMessage(
        message: String,
        actionText: String? = null,
        isError: Boolean = false,
        durationMillis: Long = 4000L
    ) {
        // Use a mutex to make sure only one toast is shown at a time, similar to how
        // SnackbarHostState works
        customToastMutex.withLock {
            try {
                customToastData.value = CustomToastData(
                    message = message,
                    actionText = actionText,
                    isError = isError
                )

                delay(durationMillis)
            } finally {
                customToastData.value = null
            }
        }
    }
}
