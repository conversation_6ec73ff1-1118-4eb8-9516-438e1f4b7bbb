package com.stt.android.device.suuntoplusdetails

import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.domain.workout.ActivityType
import com.stt.android.suuntoplus.ui.ExpandableActivityTypeGrid
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import com.stt.android.R as BaseR

@Composable
fun SuuntoPlusItemDetailsActivityTypes(
    activityTypes: ImmutableList<ActivityType>,
    modifier: Modifier = Modifier,
) {
    ExpandableActivityTypeGrid(
        activityTypes = activityTypes,
        expandAllText = stringResource(
            id = BaseR.string.suunto_plus_activity_types_show_all,
            activityTypes.size
        ),
        collapseText = stringResource(id = BaseR.string.suunto_plus_activity_types_hide),
        modifier = modifier
    )
}

@Preview
@Composable
private fun SuuntoPlusItemDetailsSingleActivityTypePreview() {
    AppTheme {
        Surface {
            SuuntoPlusItemDetailsActivityTypes(persistentListOf(ActivityType.RUNNING))
        }
    }
}

@Preview
@Composable
private fun SuuntoPlusItemDetailsMultipleActivityTypesPreview() {
    AppTheme {
        Surface {
            SuuntoPlusItemDetailsActivityTypes(persistentListOf(*ActivityType.values()))
        }
    }
}
