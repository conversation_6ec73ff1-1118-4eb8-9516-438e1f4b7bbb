package com.stt.android.device.datasource.suuntoplusguide

import androidx.annotation.WorkerThread
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginType
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities

interface GuideZAPPFileStorage {
    @WorkerThread
    fun existsInCache(
        pluginId: String,
        fileModificationTimeMillis: Long,
        capabilities: SuuntoWatchCapabilities
    ): Boolean

    @WorkerThread
    fun store(
        pluginId: String,
        fileModificationTimeMillis: Long,
        capabilities: SuuntoWatchCapabilities,
        data: ByteArray
    )

    @WorkerThread
    fun getAbsolutePath(
        pluginId: String,
        fileModificationTimeMillis: Long,
        capabilities: SuuntoWatchCapabilities,
        type: SuuntoPlusPluginType
    ): String

    @WorkerThread
    fun getFileSize(
        pluginId: String,
        fileModificationTimeMillis: Long,
        capabilities: SuuntoWatchCapabilities
    ): Int

    @WorkerThread
    fun deleteUnusedFiles(
        usedPluginIdsToTimestamp: Map<String, Long>,
        allCapabilities: List<SuuntoWatchCapabilities>
    )
}
