package com.stt.android.device.suuntoplusguide.listitems

import androidx.compose.foundation.layout.Column
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.R as BaseR

@Composable
fun RevealSwipeDeleteAction(
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        modifier = modifier,
        onClick = onDeleteClick
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                painter = painterResource(BaseR.drawable.ic_delete_outline),
                contentDescription = stringResource(BaseR.string.delete)
            )

            Text(
                text = stringResource(BaseR.string.delete),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Preview
@Composable
private fun SwipeRevealDeleteActionPreview() {
    AppTheme {
        RevealSwipeDeleteAction(
            onDeleteClick = {}
        )
    }
}
