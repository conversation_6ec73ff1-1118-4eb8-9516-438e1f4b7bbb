package com.stt.android.device.widget.v3

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.device.domain.widget.entities.WatchWidget
import com.stt.android.device.domain.widget.entities.WatchWidgetType

@Composable
fun WatchWidgetCustomizationListItem(
    widget: WatchWidget,
    isWidgetEnabled: Boolean,
    isShowDragDropIcon: Boolean,
    isShowEndIcon: Boolean,
    onEnabledChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    elevation: Dp = 0.dp
) {
    Surface(
        modifier = modifier,
        elevation = elevation
    ) {
        Row(
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.large,
                )
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (isShowDragDropIcon) {
                Icon(
                    modifier = Modifier.alpha(1.0f),
                    painter = painterResource(id = R.drawable.drag_and_drop_black),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }

            Image(
                modifier = Modifier.width(90.dp),
                painter = painterResource(widget.imageRes),
                contentDescription = null
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.smaller)
                    .weight(1f)
            ) {
                Text(
                    text = stringResource(widget.nameRes),
                    style = MaterialTheme.typography.h6
                )

                Text(
                    text = stringResource(widget.descriptionRes),
                    style = MaterialTheme.typography.body2
                )
            }

            if (isShowEndIcon) {
                val widgetControlImgRes = if (widget.enabled) {
                    R.drawable.watch_widget_remove
                } else {
                    R.drawable.watch_widget_add
                }
                Image(
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clickable(enabled = isWidgetEnabled) {
                            onEnabledChange.invoke(!widget.enabled)
                        },
                    painter = painterResource(widgetControlImgRes),
                    contentDescription = null
                )
            }
        }
    }
}

@Preview
@Composable
private fun WatchWidgetCustomizationListItemPreview() {
    AppTheme {
        WatchWidgetCustomizationListItem(
            widget = WatchWidget(
                type = WatchWidgetType.WIDGET_HR,
                nameRes = R.string.watch_widget_heart_rate_name,
                descriptionRes = R.string.watch_widget_heart_rate_description_v1,
                imageRes = R.drawable.widg_hr_v3,
                enabled = false,
                isPinned = false,
                id = WatchWidgetType.WIDGET_HR.widgetId
            ),
            isWidgetEnabled = true,
            isShowDragDropIcon = true,
            isShowEndIcon = true,
            onEnabledChange = {}
        )
    }
}
