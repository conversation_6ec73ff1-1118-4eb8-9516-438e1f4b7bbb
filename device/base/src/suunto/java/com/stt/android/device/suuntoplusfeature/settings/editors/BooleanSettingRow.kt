package com.stt.android.device.suuntoplusfeature.settings.editors

import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Switch
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.switchColors

@Composable
internal fun BooleanSettingRow(
    name: String,
    value: Boolean,
    onSettingChange: (newValue: Boolean) -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    SettingRow(
        name = name,
        onClick = {},
        enabled = enabled,
        modifier = modifier
    ) {
        Switch(
            checked = value,
            onCheckedChange = onSettingChange,
            enabled = enabled,
            colors = MaterialTheme.colors.switchColors
        )
    }
}

@Preview
@Composable
private fun BooleanSettingRowPreview() {
    AppTheme {
        Surface {
            var value by remember { mutableStateOf(false) }

            BooleanSettingRow(
                name = "Boolean setting",
                value = value,
                onSettingChange = { value = !value },
                enabled = true,
            )
        }
    }
}

@Preview
@Composable
private fun DisabledBooleanSettingRowPreview() {
    AppTheme {
        Surface {
            var value by remember { mutableStateOf(false) }

            BooleanSettingRow(
                name = "Disabled boolean setting",
                value = value,
                onSettingChange = { value = !value },
                enabled = false
            )
        }
    }
}
