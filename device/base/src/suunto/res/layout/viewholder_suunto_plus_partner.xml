<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="iconUrl"
            type="String" />

        <variable
            name="title"
            type="String" />

        <variable
            name="showConnectedText"
            type="boolean" />

        <variable
            name="serviceMetadata"
            type="com.stt.android.domain.connectedservices.ServiceMetadata" />

        <variable
            name="onClick"
            type="android.view.View.OnClickListener" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/FeedCard.ripple.noelevation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?selectableItemBackground"
        android:onClick="@{onClick}"
        android:paddingTop="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_medium">

        <ImageView
            android:id="@+id/guide_partner_icon"
            android:layout_width="@dimen/size_icon_large"
            android:layout_height="@dimen/size_icon_large"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:contentDescription="@null"
            app:partnerIconUrl="@{iconUrl}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/app_logo_small" />

        <TextView
            android:id="@+id/guide_partner_title"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:text="@{title}"
            app:layout_constraintBottom_toTopOf="@+id/guide_partner_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guide_partner_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="@tools:sample/lorem[3]" />

        <TextView
            android:id="@+id/guide_partner_info"
            style="@style/Body.Medium"
            android:layout_width="@dimen/size_spacing_zero"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_xsmall"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:text="@string/partner_connections_connected"
            android:textColor="@color/suunto_blue"
            android:visibility="@{showConnectedText ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/guide_partner_title"
            app:layout_constraintTop_toBottomOf="@+id/guide_partner_title"
            tools:text="@string/partner_connections_connected" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
