package com.stt.android.device.domain.suuntoplusguide

import com.google.common.truth.Truth.assertThat
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeatureManifestExtractor
import org.junit.Test
import java.util.Base64

class SuuntoPlusFeatureManifestExtractorTest {
    @Test
    fun `should parse manifest JSO<PERSON> correctly`() {
        val data = Base64.getMimeDecoder().decode(TEST_ZAPP_BASE64)

        val manifestJson = SuuntoPlusFeatureManifestExtractor.extractManifestJson(data)

        assertThat(manifestJson).hasLength(986)
        assertThat(manifestJson).startsWith("{\"name\":\"Anaerobic threshold\"")
        assertThat(manifestJson).endsWith("{\"name\":\"anaer-r-l.html\"}]}")
    }
}

private const val TEST_ZAPP_BASE64 = """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"""
