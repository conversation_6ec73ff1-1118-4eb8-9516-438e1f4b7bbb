package com.suunto.watch.offlinemusic

import android.content.SharedPreferences
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.di.WatchMusicManagerController
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.utils.ClearOfflineMusicLocalDataHelper
import com.stt.android.utils.STTConstants
import com.suunto.music.MusicManagerController
import com.suunto.music.OfflineMusicConstants.WATCH_ALL_SONG_ID
import com.suunto.music.model.MusicMode
import com.suunto.music.viewmodel.BaseMusicManagerViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.asScheduler
import javax.inject.Inject

@HiltViewModel
class WatchOfflineMusicViewModel @Inject constructor(
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    savedStateHandle: SavedStateHandle,
    @WatchMusicManagerController
    musicManagerController: MusicManagerController,
    sharedPreferences: SharedPreferences,
    clearOfflineMusicLocalDataHelper: ClearOfflineMusicLocalDataHelper,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseMusicManagerViewModel(
    savedStateHandle,
    musicManagerController,
    sharedPreferences,
    clearOfflineMusicLocalDataHelper,
    coroutinesDispatchers
) {
    override val showMusicPlayController: Boolean = false

    override val allSongId: String = WATCH_ALL_SONG_ID

    override val fileStatusKey: String = STTConstants.SuuntoPreferences.KEY_WATCH_FILE_STATUS

    init {
        setMusicMode(MusicMode.OFFLINE)
        observeDisconnectedState()
    }

    private fun observeDisconnectedState() {
        launch {
            deviceConnectionStateUseCase.connectedWatchState().asFlow().collect {
                setDeviceDisconnectedState(it.connectedWatchConnectionState != ConnectedWatchConnectionState.CONNECTED)
            }
        }
    }
}
