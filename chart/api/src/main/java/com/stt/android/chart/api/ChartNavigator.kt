package com.stt.android.chart.api

import android.content.Context
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle

interface ChartNavigator {
    fun openChartScreen(
        context: Context,
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison = ChartComparison.NONE,
    )
}
