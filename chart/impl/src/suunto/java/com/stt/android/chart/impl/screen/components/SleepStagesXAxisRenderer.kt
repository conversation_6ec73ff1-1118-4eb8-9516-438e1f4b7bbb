package com.stt.android.chart.impl.screen.components

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.utils.MPPointF
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.R
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

internal class SleepStagesXAxisRenderer(
    private val context: Context,
    viewPortHandler: ViewPortHandler,
    xAxis: XAxis,
    transformer: Transformer,
) : XAxisRenderer(viewPortHand<PERSON>, xAxis, transformer) {

    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    // To keep the timestamp accurate, use long as min/max range
    private var minTimestamp = 0L
    private var maxTimestamp = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC)

    private val textBounds = Rect()

    private val tickPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = ContextCompat.getColor(context, R.color.cloudy_grey)
    }

    fun setXRange(min: Long, max: Long) {
        minTimestamp = min
        maxTimestamp = max
    }

    override fun drawLabels(c: Canvas, pos: Float, anchor: MPPointF) {
        // float type can't keep timestamp accurate, use timestamp draw label
        val step = (maxTimestamp - minTimestamp) / (mXAxis.labelCount.coerceAtLeast(2) - 1)
        // fist label user min value
        var startX = minTimestamp
        val zoneOffset = ZonedDateTime.now().offset
        val axisHeight = Utils.convertDpToPixel(1f)
        val tickWidth = Utils.convertDpToPixel(1f)
        val tickHeight = Utils.convertDpToPixel(4f)
        tickPaint.strokeWidth = tickWidth
        for (index in 0..<mXAxis.labelCount) {
            val text = LocalDateTime.ofEpochSecond(startX, 0, zoneOffset).format(timeFormatter)
            val x = transformer.getPixelForValues(startX.toFloat(), pos).x.toFloat()
            val newY = pos + axisHeight / 2f + tickHeight
            val newTickX = when (index) {
                0 -> x + tickWidth / 2f
                mXAxis.labelCount - 1 -> x - tickWidth / 2f
                else -> x
            }
            c.drawLine(newTickX, pos + axisHeight / 2f, newTickX, newY, tickPaint)
            mAxisLabelPaint.getTextBounds(text, 0, text.length, textBounds)
            val newTextX = when (index) {
                0 -> x + textBounds.width() / 2f
                mXAxis.labelCount - 1 -> x - textBounds.width() / 2f
                else -> x
            }
            Utils.drawXAxisValue(c, text, newTextX, newY, mAxisLabelPaint, anchor, 0f)
            // last label use max value
            if (index == mXAxis.labelCount - 1) {
                startX = maxTimestamp
            } else {
                startX += step
            }
        }
    }
}
