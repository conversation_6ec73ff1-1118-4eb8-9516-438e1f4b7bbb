package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.data.AscentDataLoader
import com.stt.android.chart.impl.data.CaloriesDataLoader
import com.stt.android.chart.impl.data.CommuteDataLoader
import com.stt.android.chart.impl.data.DurationDataLoader
import com.stt.android.chart.impl.data.HeartRateDataLoader
import com.stt.android.chart.impl.data.MinimumHeartRateDataLoader
import com.stt.android.chart.impl.data.ResourcesDataLoader
import com.stt.android.chart.impl.data.SleepDataLoader
import com.stt.android.chart.impl.data.SleepMinimumHeartRateDataLoader
import com.stt.android.chart.impl.data.StepDataLoader
import com.stt.android.chart.impl.data.TSSDataLoader
import com.stt.android.chart.impl.data.HrvDataLoader
import com.stt.android.chart.impl.data.Vo2MaxDataLoader
import com.stt.android.chart.impl.screen.GoalEditorViewData
import javax.inject.Inject

internal class CreateGoalEditorDataUseCase @Inject constructor(
    private val sleepDataLoader: SleepDataLoader,
    private val stepDataLoader: StepDataLoader,
    private val caloriesDataLoader: CaloriesDataLoader,
    private val durationDataLoader: DurationDataLoader,
    private val ascentDataLoader: AscentDataLoader,
    private val minimumHeartRateDataLoader: MinimumHeartRateDataLoader,
    private val sleepMinimumHeartRateDataLoader: SleepMinimumHeartRateDataLoader,
    private val heartRateDataLoader: HeartRateDataLoader,
    private val restingHeartRateDataLoader: HeartRateDataLoader,
    private val commuteDataLoader: CommuteDataLoader,
    private val resourcesDataLoader: ResourcesDataLoader,
    private val tssDataLoader: TSSDataLoader,
    private val hrvDataLoader: HrvDataLoader,
    private val vo2MaxDataLoader: Vo2MaxDataLoader,
) {
    suspend operator fun invoke(
        chartContent: ChartContent,
    ): GoalEditorViewData = when (chartContent) {
        ChartContent.SLEEP -> sleepDataLoader.loadGoalEditorData()
        ChartContent.STEPS -> stepDataLoader.loadGoalEditorData()
        ChartContent.CALORIES -> caloriesDataLoader.loadGoalEditorData()
        ChartContent.DURATION -> durationDataLoader.loadGoalEditorData()
        ChartContent.ASCENT -> ascentDataLoader.loadGoalEditorData()
        ChartContent.MINIMUM_HEART_RATE -> minimumHeartRateDataLoader.loadGoalEditorData()
        ChartContent.SLEEPING_MINIMUM_HEART_RATE -> sleepMinimumHeartRateDataLoader.loadGoalEditorData()
        ChartContent.HEART_RATE -> heartRateDataLoader.loadGoalEditorData()
        ChartContent.RESTING_HEART_RATE -> restingHeartRateDataLoader.loadGoalEditorData()
        ChartContent.COMMUTE -> commuteDataLoader.loadGoalEditorData()
        ChartContent.RESOURCES -> resourcesDataLoader.loadGoalEditorData()
        ChartContent.TSS -> tssDataLoader.loadGoalEditorData()
        ChartContent.HRV -> hrvDataLoader.loadGoalEditorData()
        ChartContent.VO2MAX -> vo2MaxDataLoader.loadGoalEditorData()
    }
}
