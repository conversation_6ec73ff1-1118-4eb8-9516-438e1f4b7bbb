package com.stt.android.chart.impl.chart

import androidx.annotation.ColorInt
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberCandlestickCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.stacked
import com.patrykandpatrick.vico.compose.common.component.rememberShapeComponent
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.compose.common.shape.rounded
import com.patrykandpatrick.vico.core.cartesian.data.CandlestickCartesianLayerModel
import com.patrykandpatrick.vico.core.cartesian.data.ColumnCartesianLayerModel
import com.patrykandpatrick.vico.core.cartesian.data.LineCartesianLayerModel
import com.patrykandpatrick.vico.core.cartesian.layer.CandlestickCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.CartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.ColumnCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.LineCartesianLayer
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.component.LineComponent
import com.patrykandpatrick.vico.core.common.component.ShapeComponent
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import com.patrykandpatrick.vico.core.common.shader.ShaderProvider
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import com.patrykandpatrick.vico.core.common.shape.Shape
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.candleSpacing
import com.stt.android.chart.impl.model.columnSpace

private const val COLUMN_THICKNESS_DP = 16f
private const val COLUMN_GROUP_SPACING_DP = 2f
private const val LINE_THICKNESS_DP = 2f
private const val LINE_AREA_ALPHA = 0.2f
private const val LINE_POINT_SIZE_DP = 4f
private const val CANDLE_BODY_THICKNESS_DP = 8f



@Composable
internal fun rememberChartLayers(
    chartData: ChartData,
    rangeProvider: RangeProvider,
    selectedX: Long? = null,
): List<CartesianLayer<*>> {
    val mergeMode = { _: ExtraStore ->
        when (chartData.chartBarDisplayMode) {
            ChartBarDisplayMode.GROUPED -> ColumnCartesianLayer.MergeMode.Grouped(
                columnSpacingDp = COLUMN_GROUP_SPACING_DP,
            )
            ChartBarDisplayMode.STACKED -> ColumnCartesianLayer.MergeMode.Stacked
        }
    }

    val shouldUseGroupedLayers = chartData.chartBarDisplayMode == ChartBarDisplayMode.GROUPED &&
        chartData.series.count { it.chartType == ChartType.BAR } > 1
    return if (shouldUseGroupedLayers) {
        rememberGroupedLayers(chartData, rangeProvider, mergeMode, selectedX)
    } else {
        val shouldUseBarStackedLayers = chartData.chartBarDisplayMode == ChartBarDisplayMode.STACKED &&
            chartData.series.count { it.chartType == ChartType.BAR } > 1 &&
            chartData.series.all { it.chartType == ChartType.BAR }
        if (shouldUseBarStackedLayers) {
            return listOf(rememberBarStackedLayer(chartData, rangeProvider))
        } else {
            chartData.series.flatMap {
                rememberLayers(it, chartData.chartGranularity, chartData, rangeProvider, mergeMode, selectedX)
            }
        }
    }
}

@Composable
private fun rememberLayers(
    series: ChartData.Series,
    chartGranularity: ChartGranularity,
    chartData: ChartData,
    rangeProvider: RangeProvider,
    mergeMode: (ExtraStore) -> ColumnCartesianLayer.MergeMode,
    selectedX: Long? = null,
): List<CartesianLayer<*>> = when (series.chartType) {
    ChartType.BAR -> listOf(
        rememberColumnCartesianLayer(
            columnProvider = rememberSelectableColumnProvider(
                originalColor = series.color,
                selectedX = selectedX,
            ),
            rangeProvider = rangeProvider,
            mergeMode = mergeMode,
            columnCollectionSpacing = chartData.chartContent.columnSpace(chartGranularity).dp
        )
    )
    ChartType.LINE -> {
        val config = series.lineConfig ?: LineChartConfig()

        val hasGradientData = series.gradientEntries.isNotEmpty()

        if (hasGradientData) {
            createGradientLineLayer(series, config, rangeProvider)
        } else {
            createStandardLineLayer(series, config, rangeProvider)
        }
    }
    ChartType.CANDLESTICK -> listOf(
        rememberCandlestickCartesianLayer(
            candleProvider = createCandleProvider(
                color = series.color,
                selectedX = selectedX
            ),
            minCandleBodyHeight = 4f.dp,
            candleSpacing = chartGranularity.candleSpacing.dp,
            rangeProvider = rangeProvider
        )
    )
    ChartType.SLEEP_STAGE -> throw Exception("Not supported")
}

@Composable
private fun createPointProvider(
    @ColorInt color: Int,
    config: LineChartConfig,
): LineCartesianLayer.PointProvider? {
    if (!config.showPoints) return null
    
    val pointComponent = if (config.isPointFilled) {
        rememberShapeComponent(
            fill = fill(Color(color)),
            shape = CorneredShape.Pill,
        )
    } else {
        rememberShapeComponent(
            fill = fill(Color.White),
            shape = CorneredShape.Pill,
            strokeFill = fill(Color(color)),
            strokeThickness = 2.5.dp,
        )
    }

    return LineCartesianLayer.PointProvider.single(
        LineCartesianLayer.Point(
            component = pointComponent,
            sizeDp = config.pointSizeDP ?: LINE_POINT_SIZE_DP,
        )
    )
}

@Composable
private fun createCandleProvider(
    @ColorInt color: Int,
    bodyThickness: Float = CANDLE_BODY_THICKNESS_DP,
    showWicks: Boolean = false,
    selectedX: Long? = null,
): CandlestickCartesianLayer.CandleProvider {
    return remember(color, bodyThickness, showWicks, selectedX) {
        object : CandlestickCartesianLayer.CandleProvider {
            override fun getCandle(
                entry: CandlestickCartesianLayerModel.Entry,
                extraStore: ExtraStore,
            ): CandlestickCartesianLayer.Candle {
                val candleColor = if (selectedX == null || selectedX == entry.x.toLong()) {
                    Color(color)
                } else {
                    Color(getFadedColor(color))
                }

                val candleBody = LineComponent(
                    fill = Fill(candleColor.toArgb()),
                    thicknessDp = bodyThickness,
                    shape = CorneredShape.rounded(allDp = 100f),
                )

                val wick = if (showWicks) {
                    LineComponent(
                        fill = Fill(candleColor.toArgb()),
                        thicknessDp = (bodyThickness / 4.0F),
                    )
                } else {
                    LineComponent(
                        fill = Fill(Color.Transparent.toArgb()),
                        thicknessDp = 0f,
                    )
                }

                return CandlestickCartesianLayer.Candle(
                    body = candleBody,
                    topWick = wick,
                    bottomWick = wick,
                )
            }

            override fun getWidestCandle(extraStore: ExtraStore): CandlestickCartesianLayer.Candle {
                val candleColor = Color(color)
                val candleBody = LineComponent(
                    fill = Fill(candleColor.toArgb()),
                    thicknessDp = bodyThickness,
                    shape = CorneredShape.rounded(allDp = 100f),
                )
                val wick = if (showWicks) {
                    LineComponent(
                        fill = Fill(candleColor.toArgb()),
                        thicknessDp = (bodyThickness / 4.0F),
                    )
                } else {
                    LineComponent(
                        fill = Fill(Color.Transparent.toArgb()),
                        thicknessDp = 0f,
                    )
                }
                return CandlestickCartesianLayer.Candle(
                    body = candleBody,
                    topWick = wick,
                    bottomWick = wick,
                )
            }
        }
    }
}


@Composable
private fun rememberGroupedLayers(
    chartData: ChartData,
    rangeProvider: RangeProvider,
    mergeMode: (ExtraStore) -> ColumnCartesianLayer.MergeMode,
    selectedX: Long? = null,
): List<CartesianLayer<*>> = buildList {
    val barSeries = chartData.series.filter { series -> series.chartType == ChartType.BAR }

    if (barSeries.isNotEmpty()) {
        val hasMultiColorBars = barSeries.any { it.groupStackBarEntries.isNotEmpty() }

        if (hasMultiColorBars) {
            val barComponents = barSeries.map { series ->
                if (series.groupStackBarEntries.isNotEmpty()) {
                    MultiColorColumnComponent(
                        fill = Fill(series.color),
                        stackEntries = series.groupStackBarEntries,
                        xValue = series.groupStackBarEntries.firstOrNull()?.x ?: 0L,
                        thicknessDp = series.groupStackBarStyle?.thicknessDp ?: MultiColorColumnComponent.DEFAULT_THICKNESS_DP,
                        shape = if (series.groupStackBarStyle != null) {
                            CorneredShape.rounded(
                                topLeftDp = series.groupStackBarStyle.cornerRadiusTopLeftDp ?: MultiColorColumnComponent.DEFAULT_CORNER_RADIUS_DP,
                                topRightDp = series.groupStackBarStyle.cornerRadiusTopRightDp ?: MultiColorColumnComponent.DEFAULT_CORNER_RADIUS_DP,
                                bottomRightDp = series.groupStackBarStyle.cornerRadiusBottomRightDp ?: MultiColorColumnComponent.DEFAULT_CORNER_RADIUS_DP,
                                bottomLeftDp = series.groupStackBarStyle.cornerRadiusBottomLeftDp ?: MultiColorColumnComponent.DEFAULT_CORNER_RADIUS_DP
                            )
                        } else {
                            Shape.Rectangle
                        },
                        shadow = null,
                        multiColorBarStyle = series.groupStackBarStyle
                    )
                } else {
                    LineComponent(
                        fill = Fill(series.color),
                        thicknessDp = COLUMN_THICKNESS_DP,
                        shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                    )
                }
            }

                            add(
                rememberColumnCartesianLayer(
                    columnProvider = ColumnCartesianLayer.ColumnProvider.series(barComponents),
                    mergeMode = mergeMode,
                    rangeProvider = rangeProvider,
                    columnCollectionSpacing = chartData.chartContent.columnSpace(chartData.chartGranularity).dp
                )
            )
        } else {
            add(
                rememberColumnCartesianLayer(
                    columnProvider = if (selectedX != null) {
                        rememberMultiSeriesSelectableColumnProvider(
                            seriesColors = barSeries.map { it.color },
                            selectedX = selectedX
                        )
                    } else {
                        ColumnCartesianLayer.ColumnProvider.series(
                            barSeries.map { series ->
                                LineComponent(
                                    fill = Fill(series.color),
                                    thicknessDp = COLUMN_THICKNESS_DP,
                                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                                )
                            }
                        )
                    },
                    mergeMode = mergeMode,
                    rangeProvider = rangeProvider,
                    columnCollectionSpacing = chartData.chartContent.columnSpace(chartData.chartGranularity).dp
                )
            )
        }
    }

    chartData.series.forEach { series ->
        if (series.chartType != ChartType.BAR) {
            addAll(rememberLayers(series, chartData.chartGranularity, chartData, rangeProvider, mergeMode, selectedX))
        }
    }
}

@Composable
private fun rememberBarStackedLayer(
    chartData: ChartData,
    rangeProvider: RangeProvider,
): CartesianLayer<*> {
    return rememberColumnCartesianLayer(
        columnProvider = ColumnCartesianLayer.ColumnProvider.series(
            columns = chartData.series.mapIndexed { index, series ->
                rememberRoundedCornerStackedLineComponent(
                    fill = Fill(series.color),
                    prevFill = Fill(chartData.series.getOrNull(index - 1)?.color ?: series.color),
                    thicknessDp = COLUMN_THICKNESS_DP,
                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp),
                    roundedCornerOffsetDp = 4f,
                    horizontalAxisHeightDp = 20f,
                )
            },
        ),
        rangeProvider = rangeProvider,
        mergeMode = { ColumnCartesianLayer.MergeMode.stacked() },
        columnCollectionSpacing = chartData.chartContent.columnSpace(chartData.chartGranularity).dp
    )
}

@Composable
private fun createStandardLineLayer(
    series: ChartData.Series,
    config: LineChartConfig,
    rangeProvider: RangeProvider,
): List<CartesianLayer<*>> {
    val pointProvider = createPointProvider(series.color, config)
    val pointConnector = if (config.isSmoothCurve){
        LineCartesianLayer.PointConnector.cubic()
    } else {
        LineCartesianLayer.PointConnector.Sharp
    }
    val areaFill = if (config.showAreaFill) {
        LineCartesianLayer.AreaFill.single(
            fill = Fill(Color(series.color).copy(alpha = config.areaAlpha ?: LINE_AREA_ALPHA).toArgb()),
        )
    } else null

    return listOf(
        rememberLineCartesianLayer(
            lineProvider = LineCartesianLayer.LineProvider.series(
                lines = listOf(
                    LineCartesianLayer.Line(
                        fill = LineCartesianLayer.LineFill.single(
                            fill = Fill(series.color),
                        ),
                        stroke = LineCartesianLayer.LineStroke.Continuous(config.thicknessDP ?: LINE_THICKNESS_DP),
                        areaFill = areaFill,
                        pointProvider = pointProvider,
                        pointConnector = pointConnector
                    )
                ),
            ),
            rangeProvider = rangeProvider,
        )
    )
}


@Composable
private fun createGradientLineLayer(
    series: ChartData.Series,
    config: LineChartConfig,
    rangeProvider: RangeProvider,
): List<CartesianLayer<*>> {
    val colors = if (series.gradientEntries.isNotEmpty()) {
        series.gradientEntries.map { it.color }.toIntArray()
    } else {
        IntArray(series.entries.size) { series.color }
    }

    val pointConnector = if (config.isSmoothCurve){
        LineCartesianLayer.PointConnector.cubic()
    } else {
        LineCartesianLayer.PointConnector.Sharp
    }
    val lineStroke = LineCartesianLayer.LineStroke.Continuous(config.thicknessDP ?: LINE_THICKNESS_DP)

    val lineFill = if (colors.size >= 2) {
        LineCartesianLayer.LineFill.single(
            Fill(ShaderProvider.horizontalGradient(colors))
        )
    } else if (colors.size == 1) {
        LineCartesianLayer.LineFill.single(
            Fill(colors[0])
        )
    } else {
        LineCartesianLayer.LineFill.single(
            Fill(series.color)
        )
    }

    val pointProvider = if (config.showPoints) {
        createGradientPointProvider(series, config)
    } else null

    val areaFill = if (config.showAreaFill) {
        if (colors.size >= 2) {
            createGradientAreaFill(colors)
        } else if (colors.size == 1) {
            LineCartesianLayer.AreaFill.single(
                Fill(Color(colors[0]).copy(alpha = 0.2f).toArgb())
            )
        } else {
            null
        }
    } else null

    return listOf(
        LineCartesianLayer(
            lineProvider = LineCartesianLayer.LineProvider.series(
                LineCartesianLayer.Line(
                    fill = lineFill,
                    stroke = lineStroke,
                    pointConnector = pointConnector,
                    pointProvider = pointProvider,
                    areaFill = areaFill
                )
            ),
            rangeProvider = rangeProvider,
        )
    )
}

@Composable
private fun rememberSelectableColumnProvider(
    originalColor: Int,
    selectedX: Long?,
): ColumnCartesianLayer.ColumnProvider {
    return remember(originalColor, selectedX) {
        object : ColumnCartesianLayer.ColumnProvider {
            override fun getColumn(
                entry: ColumnCartesianLayerModel.Entry,
                seriesIndex: Int,
                extraStore: ExtraStore,
            ): LineComponent {
                val color = if (selectedX == null || selectedX == entry.x.toLong()) {
                    originalColor
                } else {
                    getFadedColor(originalColor)
                }

                return LineComponent(
                    fill = Fill(color),
                    thicknessDp = COLUMN_THICKNESS_DP,
                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                )
            }

            override fun getWidestSeriesColumn(seriesIndex: Int, extraStore: ExtraStore): LineComponent {
                return LineComponent(
                    fill = Fill(originalColor),
                    thicknessDp = COLUMN_THICKNESS_DP,
                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                )
            }
        }
    }
}
@Composable
private fun rememberMultiSeriesSelectableColumnProvider(
    seriesColors: List<Int>,
    selectedX: Long?,
): ColumnCartesianLayer.ColumnProvider {
    return remember(seriesColors, selectedX) {
        object : ColumnCartesianLayer.ColumnProvider {
            override fun getColumn(
                entry: ColumnCartesianLayerModel.Entry,
                seriesIndex: Int,
                extraStore: ExtraStore,
            ): LineComponent {
                val originalColor = seriesColors.getOrElse(seriesIndex) { seriesColors.firstOrNull() ?: 0xFF000000.toInt() }

                val color = if (selectedX == null || selectedX == entry.x.toLong()) {
                    originalColor
                } else {
                    getFadedColor(originalColor)
                }

                return LineComponent(
                    fill = Fill(color),
                    thicknessDp = COLUMN_THICKNESS_DP,
                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                )
            }

            override fun getWidestSeriesColumn(seriesIndex: Int, extraStore: ExtraStore): LineComponent {
                val originalColor = seriesColors.getOrElse(seriesIndex) { seriesColors.firstOrNull() ?: 0xFF000000.toInt() }
                return LineComponent(
                    fill = Fill(originalColor),
                    thicknessDp = COLUMN_THICKNESS_DP,
                    shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp)
                )
            }
        }
    }
}

private fun createGradientPointProvider(
    series: ChartData.Series,
    config: LineChartConfig
): LineCartesianLayer.PointProvider {
    return object : LineCartesianLayer.PointProvider {
        private val points = series.gradientEntries.map { entry ->
            val pointComponent = ShapeComponent(
                fill = Fill(if (config.isPointFilled) entry.color else Color.White.toArgb()),
                strokeFill = Fill(entry.color),
                shape = CorneredShape.Pill,
                strokeThicknessDp = 2f
            )
            LineCartesianLayer.Point(
                component = pointComponent,
                sizeDp = config.pointSizeDP ?: LINE_POINT_SIZE_DP
            )
        }

        override fun getPoint(
            entry: LineCartesianLayerModel.Entry,
            seriesIndex: Int,
            extraStore: ExtraStore
        ): LineCartesianLayer.Point? {
            val entriesForCheck = series.gradientEntries.map { it.x }

            val index = entriesForCheck.indexOfFirst { it == entry.x.toLong() }
            return if (index >= 0 && index < points.size) points[index] else null
        }

        override fun getLargestPoint(extraStore: ExtraStore): LineCartesianLayer.Point? {
            return points.maxByOrNull { it.sizeDp }
        }
    }
}


private fun createGradientAreaFill(colors: IntArray): LineCartesianLayer.AreaFill {
    val transparentColors = colors.map {
        Color(it).copy(alpha = 0.2f).toArgb()
    }.toIntArray()

    return LineCartesianLayer.AreaFill.single(
        Fill(ShaderProvider.horizontalGradient(transparentColors))
    )
}

private fun getFadedColor(@ColorInt originalColor: Int, fadeAlpha: Float = 0.8f): Int {
    val color = Color(originalColor)
    val fadedColor = color.copy(alpha = 1f - fadeAlpha)
    return fadedColor.copy(
        red = color.red + (1f - color.red) * fadeAlpha,
        green = color.green + (1f - color.green) * fadeAlpha,
        blue = color.blue + (1f - color.blue) * fadeAlpha,
        alpha = 1f
    ).toArgb()
}
