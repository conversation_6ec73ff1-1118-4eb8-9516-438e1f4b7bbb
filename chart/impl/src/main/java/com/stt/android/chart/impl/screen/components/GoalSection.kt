package com.stt.android.chart.impl.screen.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.screen.ChartViewData
import com.stt.android.chart.impl.screen.ChartViewEvent
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing

@Composable
internal fun GoalSection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    when (viewData.goal) {
        is GoalViewData.None -> Unit
        is GoalViewData.Goal -> SuuntoCard(modifier = modifier) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        onClick = { onEvent(ChartViewEvent.ShowGoalEditor) },
                    )
                    .padding(MaterialTheme.spacing.medium),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painter = painterResource(viewData.goal.icon),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    tint = colorResource(viewData.goal.iconColor),
                )

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(viewData.goal.title),
                        color = MaterialTheme.colorScheme.nearBlack,
                        style = MaterialTheme.typography.bodyLarge,
                    )

                    val goal by viewData.goal.goal.collectAsState("")
                    Text(
                        text = goal,
                        color = MaterialTheme.colorScheme.darkGrey,
                        style = MaterialTheme.typography.bodyMedium,
                    )
                }

                Icon(
                    painter = painterResource(R.drawable.ic_chevron_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.nearBlack,
                )
            }
        }
    }
}
