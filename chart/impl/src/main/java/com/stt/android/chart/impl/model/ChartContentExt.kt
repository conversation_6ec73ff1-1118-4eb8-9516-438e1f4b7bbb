package com.stt.android.chart.impl.model

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import kotlin.math.roundToInt
import com.stt.android.R as BR
import com.stt.android.core.R as CR

internal val ChartContent.titleRes: Int
    get() = when (this) {
        ChartContent.SLEEP -> R.string.chart_content_sleep
        ChartContent.STEPS -> R.string.chart_content_steps
        ChartContent.CALORIES -> R.string.chart_content_calories
        ChartContent.DURATION -> R.string.chart_content_duration
        ChartContent.ASCENT -> R.string.chart_content_ascent
        ChartContent.MINIMUM_HEART_RATE -> R.string.chart_content_minimum_heart_rate
        ChartContent.SLEEPING_MINIMUM_HEART_RATE -> R.string.chart_content_sleep_minimum_heart_rate
        ChartContent.HEART_RATE -> R.string.chart_content_heart_rate
        ChartContent.RESTING_HEART_RATE -> R.string.chart_content_resting_heart_rate
        ChartContent.COMMUTE -> R.string.chart_connect_commute
        ChartContent.RESOURCES -> R.string.chart_content_resources
        ChartContent.TSS -> R.string.chart_connect_training_load
        ChartContent.HRV -> R.string.chart_content_hrv
        ChartContent.VO2MAX -> R.string.chart_content_vo2max
    }

internal val ChartContent.availableStyles: List<ChartStyle>
    get() = when (this) {
        ChartContent.STEPS,
        ChartContent.CALORIES -> listOf(ChartStyle.SINGLE, ChartStyle.CUMULATIVE)
        ChartContent.DURATION -> listOf(ChartStyle.SINGLE, ChartStyle.CUMULATIVE)
        ChartContent.ASCENT -> listOf(ChartStyle.SINGLE, ChartStyle.CUMULATIVE)
        ChartContent.TSS -> listOf(ChartStyle.SINGLE, ChartStyle.CUMULATIVE)
        ChartContent.SLEEP,
        ChartContent.HEART_RATE,
        ChartContent.MINIMUM_HEART_RATE,
        ChartContent.RESTING_HEART_RATE,
        ChartContent.SLEEPING_MINIMUM_HEART_RATE,
        ChartContent.COMMUTE,
        ChartContent.RESOURCES,
        ChartContent.HRV,
        ChartContent.VO2MAX -> listOf(ChartStyle.SINGLE)
    }

@StringRes
fun ChartContent.valueTypeRes(
    chartGranularity: ChartGranularity,
): Int = when (this) {
    ChartContent.TSS, -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY -> R.string.chart_value_total
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }
    ChartContent.SLEEP,
    ChartContent.COMMUTE,
    ChartContent.HRV -> when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.chart_value_daily_avg
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_avg
    }
    ChartContent.RESOURCES ->  when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.chart_value_total
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS -> R.string.chart_value_daily_avg
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_avg
    }

    ChartContent.VO2MAX -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }
    ChartContent.HEART_RATE  -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS  -> R.string.chart_value_range
    }

    ChartContent.STEPS -> when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.chart_value_total
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS  -> R.string.chart_value_daily_avg
    }
    ChartContent.CALORIES ->  when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.chart_value_active
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg_calories
    }
    ChartContent.ASCENT,
    ChartContent.DURATION -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS -> R.string.chart_value_total
        ChartGranularity.YEARLY -> R.string.chart_value_monthly_avg
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_Yearly_avg
    }
}

@StringRes
internal fun ChartContent.rightValueTypeRes(
    chartGranularity: ChartGranularity,
): Int = when (this) {
    ChartContent.SLEEP -> R.string.chart_value_range
    ChartContent.VO2MAX -> valueTypeRes(chartGranularity)
    else -> valueTypeRes(chartGranularity)
}

internal val ChartContent.leftComparisonColorRes: Int?
    @ColorRes
    get() = when (this) {
        ChartContent.SLEEP -> CR.color.medium_purple
        ChartContent.STEPS -> BR.color.dashboard_widget_steps
        ChartContent.CALORIES -> BR.color.dashboard_widget_calories
        ChartContent.DURATION -> BR.color.dashboard_widget_duration
        ChartContent.ASCENT -> BR.color.dashboard_widget_max_vo2
        ChartContent.TSS -> BR.color.dashboard_widget_tss
        else -> null
    }

internal val ChartContent.rightComparisonColorRes: Int?
    @ColorRes
    get() = when (this) {
        ChartContent.SLEEP -> CR.color.bright_red
        ChartContent.STEPS,
        ChartContent.CALORIES,
        ChartContent.DURATION,
        ChartContent.ASCENT,
        ChartContent.TSS -> BR.color.medium_grey
        else -> null
    }

@Suppress("unused")
@StringRes
internal fun ChartContent.comparisonOffTitleRes(
    chartGranularity: ChartGranularity,
): Int = when (this) {
    ChartContent.SLEEP -> R.string.sleep_heart_rate_compare
    ChartContent.STEPS,
    ChartContent.CALORIES,
    ChartContent.DURATION,
    ChartContent.ASCENT,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.COMMUTE,
    ChartContent.HEART_RATE,
    ChartContent.HRV,
    ChartContent.TSS,
    ChartContent.RESOURCES -> R.string.chart_comparison_compare
    ChartContent.VO2MAX -> R.string.chart_comparison_compare
}

@Suppress("unused")
@StringRes
internal fun ChartContent.comparisonOnTitleRes(
    chartGranularity: ChartGranularity,
): Int = when (this) {
    ChartContent.SLEEP -> R.string.sleep_heart_rate_hide_comparison
    ChartContent.STEPS,
    ChartContent.CALORIES,
    ChartContent.DURATION,
    ChartContent.ASCENT,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.COMMUTE,
    ChartContent.HEART_RATE,
    ChartContent.HRV,
    ChartContent.TSS,
    ChartContent.RESOURCES -> R.string.chart_comparison_hide_comparison
    ChartContent.VO2MAX -> R.string.chart_comparison_hide_comparison
}

@Suppress("unused")
internal fun ChartContent.targetChartComparison(
    chartGranularity: ChartGranularity,
): ChartComparison = when (this) {
    ChartContent.SLEEP -> ChartComparison.RIGHT_AXIS
    ChartContent.STEPS,
    ChartContent.CALORIES,
    ChartContent.DURATION,
    ChartContent.ASCENT,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.COMMUTE,
    ChartContent.TSS,
    ChartContent.HEART_RATE,
    ChartContent.HRV,
    ChartContent.RESOURCES,
    ChartContent.VO2MAX -> ChartComparison.LAST_PERIOD
}

internal fun ChartContent.supportsComparison(
    chartGranularity: ChartGranularity,
): Boolean = when (this) {
    ChartContent.CALORIES,
    ChartContent.STEPS -> when (chartGranularity) {
        ChartGranularity.WEEKLY,
        ChartGranularity.MONTHLY,
        ChartGranularity.YEARLY -> true
        ChartGranularity.DAILY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.EIGHT_YEARS -> false
    }
    ChartContent.SLEEP -> chartGranularity == ChartGranularity.DAILY
    ChartContent.DURATION ->  when (chartGranularity) {
        ChartGranularity.WEEKLY,
        ChartGranularity.MONTHLY,
        ChartGranularity.YEARLY -> true
        ChartGranularity.DAILY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.EIGHT_YEARS -> false
    }
    ChartContent.ASCENT -> true
    ChartContent.TSS -> when (chartGranularity) {
        ChartGranularity.WEEKLY,
        ChartGranularity.MONTHLY,
        ChartGranularity.YEARLY,
        ChartGranularity.DAILY,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> true
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.EIGHT_YEARS -> false
    }
    ChartContent.HEART_RATE,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.COMMUTE,
    ChartContent.HRV,
    ChartContent.RESOURCES,
    ChartContent.VO2MAX -> false
}

internal val ChartContent.mainChartGranularity: List<ChartGranularity>
    get() = when (this) {
        ChartContent.STEPS,
        ChartContent.CALORIES -> listOf(
            ChartGranularity.DAILY,
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
        )
        ChartContent.SLEEP -> listOf(
            ChartGranularity.DAILY,
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.SIX_MONTHS,
        )
        ChartContent.DURATION -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SIX_MONTHS,
        )
        ChartContent.ASCENT,
        ChartContent.TSS -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SIX_MONTHS,
        )
        ChartContent.MINIMUM_HEART_RATE,
        ChartContent.SLEEPING_MINIMUM_HEART_RATE -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.SIX_MONTHS,
        )

        ChartContent.RESTING_HEART_RATE -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SIX_MONTHS,
        )


        ChartContent.HEART_RATE -> listOf(
            ChartGranularity.DAILY,
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
        )
        ChartContent.COMMUTE -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.THIRTY_DAYS,
            )

        ChartContent.RESOURCES -> listOf(
            ChartGranularity.DAILY,
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.SEVEN_DAYS,
        )
        ChartContent.HRV -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.SIX_MONTHS,
        )
        ChartContent.VO2MAX -> listOf(
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY,
            ChartGranularity.EIGHT_YEARS,
        )
    }

internal val ChartContent.extraChartGranularity: List<ChartGranularity>
    get() = when (this) {
        ChartContent.COMMUTE -> emptyList()
        ChartContent.VO2MAX -> listOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
        )
        ChartContent.SLEEP -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.YEARLY,
        )
        ChartContent.CALORIES,
        ChartContent.STEPS -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.EIGHT_YEARS,
        )
        ChartContent.DURATION -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.EIGHT_YEARS,
        )
        ChartContent.ASCENT,
        ChartContent.TSS -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.EIGHT_YEARS,
        )
        ChartContent.HEART_RATE -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.EIGHT_YEARS,
        )
        ChartContent.RESTING_HEART_RATE -> listOf(
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
        )
        ChartContent.MINIMUM_HEART_RATE,
        ChartContent.SLEEPING_MINIMUM_HEART_RATE,
        ChartContent.HRV,
        ChartContent.RESOURCES  -> emptyList()
    }
@StringRes
fun ChartContent.highlightTitleRes(
    chartGranularity: ChartGranularity,
): Int = when (this) {
    ChartContent.CALORIES,
    ChartContent.SLEEP,
    ChartContent.TSS,
    ChartContent.STEPS -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS -> R.string.chart_value_total
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }
    ChartContent.COMMUTE -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_total
    }
    ChartContent.HEART_RATE -> when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.sleep_heart_rate_compare
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_range
    }
    ChartContent.MINIMUM_HEART_RATE -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY -> R.string.chart_value_min_daytime_hr
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_avg_min_daytime_hr
    }
    ChartContent.SLEEPING_MINIMUM_HEART_RATE -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY -> R.string.chart_value_min_sleep_hr
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_avg_sleep_hr
    }
    ChartContent.RESTING_HEART_RATE -> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY -> R.string.chart_value_resting_hr
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_avg_resting_hr
    }
    ChartContent.RESOURCES -> when (chartGranularity) {
        ChartGranularity.DAILY -> R.string.chart_value_active
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_range
    }
    ChartContent.HRV,
    ChartContent.VO2MAX-> when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }
    ChartContent.ASCENT,
    ChartContent.DURATION ->when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS ->  R.string.chart_value_total
    }
}

internal fun ChartContent.formatYAxisLabel(
    context: Context,
    value: Float
): String = when (this) {
    ChartContent.DURATION -> {
        val minutes = value.toDouble()
        val totalMinutes = minutes.toLong()
        val hours = totalMinutes / 60
        val mins = totalMinutes % 60
        
        val hourUnit = context.getString(CR.string.hour)
        val minuteUnit = context.getString(CR.string.minute)
        
        when {
            hours == 0L && mins == 0L -> "0"
            hours == 0L -> "$mins$minuteUnit"
            mins == 0L -> "$hours$hourUnit"
            else -> "$hours$hourUnit$mins$minuteUnit"
        }
    }
    ChartContent.SLEEP -> {
        val hourUnit = context.getString(CR.string.hour)
        val hours = value.toInt()
        val minutes = ((value - hours) * 60).toInt()
        when {
            hours == 0 && minutes == 0 -> "0"
            minutes == 0 -> "$hours$hourUnit"
            else -> "$hours:$minutes$hourUnit"
        }
    }
    ChartContent.STEPS,
    ChartContent.CALORIES,
    ChartContent.ASCENT,
    ChartContent.COMMUTE,
    ChartContent.HEART_RATE,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.RESOURCES,
    ChartContent.HRV,
    ChartContent.TSS,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.VO2MAX -> value.roundToInt().toString()
}

fun ChartContent?.columnSpace(
    chartGranularity: ChartGranularity,
): Float = when (this) {
    ChartContent.RESOURCES -> when (chartGranularity) {
        ChartGranularity.DAILY -> 1f
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> 16f
    }
    ChartContent.SLEEP,
    ChartContent.STEPS,
    ChartContent.CALORIES,
    ChartContent.DURATION,
    ChartContent.ASCENT,
    ChartContent.MINIMUM_HEART_RATE,
    ChartContent.SLEEPING_MINIMUM_HEART_RATE,
    ChartContent.HEART_RATE,
    ChartContent.RESTING_HEART_RATE,
    ChartContent.COMMUTE,
    ChartContent.TSS,
    ChartContent.HRV,
    ChartContent.VO2MAX -> 16f
    null -> 16f
}
