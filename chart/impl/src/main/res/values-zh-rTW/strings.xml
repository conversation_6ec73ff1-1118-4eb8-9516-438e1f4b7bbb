<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="chart_content_sleep">睡眠</string>
    <string name="chart_content_steps">步數</string>
    <string name="chart_content_calories">卡路里</string>
    <string name="chart_content_duration">持續時間</string>
    <string name="chart_content_ascent">上升</string>
    <string name="chart_content_heart_rate">心率</string>
    <string name="chart_content_minimum_heart_rate">最小日間心率</string>
    <string name="chart_content_sleep_minimum_heart_rate">最小日間心率</string>
    <string name="chart_content_resting_heart_rate">靜止心率</string>
    <string name="chart_content_resources">活力</string>
    <string name="chart_content_hrv">HRV</string>
    <string name="chart_content_vo2max">VO2Max</string>

    <!-- Resource states -->
    <string name="resources_state_active">已啟動</string>
    <string name="resources_state_inactive">不活躍</string>
    <string name="resources_state_stressed">承受壓力</string>
    <string name="resources_state_recovering">正在恢復</string>

    <string name="resources_state_active_avg">平均 作用中</string>
    <string name="resources_state_inactive_avg">平均 非使用中</string>
    <string name="resources_state_stressed_avg">平均 壓力沉重</string>
    <string name="resources_state_recovering_avg">平均值正在恢復</string>

    <!-- Resource levels -->
    <string name="resources_level_low">低</string>
    <string name="resources_level_moderate">中等</string>
    <string name="resources_level_high">高</string>
    <string name="resources_level_very_high">非常高</string>

    <string name="chart_value_total">總計</string>
    <string name="chart_value_total_co2e_saved">公斤二氧化碳當量已減少</string>
    <string name="chart_value_daily_avg">每日平均值</string>

    <string name="chart_value_range">範圍</string>
    <string name="chart_value_min_daytime_hr">最小日間心率</string>
    <string name="chart_value_avg_min_daytime_hr">最小日間心率</string>
    <string name="chart_value_min_sleep_hr">最小日間心率</string>
    <string name="chart_value_avg_sleep_hr">平均睡眠心率</string>
    <string name="chart_value_resting_hr">靜止心率</string>
    <string name="chart_value_avg_resting_hr">平均值靜止心率</string>
    <string name="chart_value_active">已啟動</string>
    <string name="chart_value_avg">平均值</string>

    <string name="chart_daily_target">每日目標</string>
    <string name="chart_daily_step_target">每日步數目標</string>
    <string name="chart_daily_calorie_target">每日卡路里目標</string>
    <string name="chart_daily_sleep_target">每日卡路里目標</string>

    <string name="chart_comparison_compare">對比</string>
    <string name="chart_comparison_hide_comparison">隱藏比較</string>

    <string name="chart_granularity_daily_abbreviation">天</string>
    <string name="chart_granularity_weekly_abbreviation">週</string>
    <string name="chart_granularity_seven_days_abbreviation">7D</string>
    <string name="chart_granularity_monthly_abbreviation">五月</string>
    <string name="chart_granularity_thirty_days_abbreviation">30D</string>
    <string name="chart_granularity_six_weeks_abbreviation">6 週</string>
    <string name="chart_granularity_six_months_abbreviation">6M</string>
    <string name="chart_granularity_yearly_abbreviation">年</string>
    <string name="chart_granularity_eight_years_abbreviation">8Y</string>
    <string name="chart_granularity_sixty_day_days_abbreviation">60D</string>
    <string name="chart_granularity_three_hundred_sixty_five_days_abbreviation">365D</string>
    <string name="chart_granularity_one_hundred_eight_days_abbreviation">180D</string>

    <string name="chart_granularity_seven_days">最近 7 天</string>
    <string name="chart_granularity_thirty_days">最近 30 天</string>
    <string name="chart_granularity_six_weeks">6 週</string>
    <string name="chart_granularity_six_months">6 個月</string>
    <string name="chart_granularity_one_year">1 年</string>
    <string name="chart_granularity_eight_years">8 年</string>

    <string name="chart_granularity_daily_interval">每日間隔</string>
    <string name="chart_granularity_weekly_interval">每週間隔</string>
    <string name="chart_granularity_monthly_interval">每月間隔</string>
    <string name="chart_granularity_yearly_interval">每年間隔</string>

    <string name="chart_granularity_more">更多</string>
    <string name="chart_granularity_time_range_title">時間範圍</string>
    <string name="chart_granularity_time_range_desc">選取分析的時間範圍</string>

    <string name="calories_bmr">BMR</string>
    <string name="total_calories">總卡路里</string>
    <string name="about_calories">關於卡路里</string>
    <string name="calories_instruction">總卡路里代表一天所消耗的能量，包括基礎代謝率（BMR）和活動卡路里。BMR 為維持重要生理功能所需的最低能量，如呼吸，心跳，溫度調節等靜止不動。活動卡路里會將運動和日常活動期間消耗的能量納入考量。增加 BMR 可透過鍛鍊肌肉量，進行定期運動，保持水分充足和維持良好的睡眠習慣來達成。</string>
    <string name="workout_sessions_title">訓練課程</string>
    <string name="workout_filter_all">全部</string>

    <string name="about_minimum_hr_title">最低心率</string>
    <string name="about_minimum_hr_description">最小白天心率是清醒時測得的最低心率，通常高於休息和睡眠心率。它反映出整體的活動程度和心臟健康，有助於在睡眠資料無法使用時辨識疲勞，壓力和恢復狀態。</string>
    <string name="about_sleep_minimum_hr_title">關於睡眠最小心率</string>"
    <string name="about_sleep_minimum_hr_description">最小睡眠心率是睡眠期間所記錄的最低心率。這可作為睡眠品質與恢復的指標，通常較低的值表示有更好的恢復效果。</string>
    <string name="about_heart_rate_title">關於心率</string>"
    <string name="about_heart_rate_description">心率（HR）是指每分鐘心跳次數，是心臟健康和體適能的關鍵指標。瞭解心率變化有助於最佳化訓練成效，並提升整體身心健康。心率在睡眠，運動和日常活動中波動。靜止心率和最低睡眠時的聽聲率通常較低，有助於恢復評估和引導訓練強度。監測每日心率有助於追蹤趨勢，偵測異常狀況，並維持對整體健康的認知。</string>

    <string name="about_resting_heart_rate_title">關於靜止心率（RHR）</string>
    <string name="about_resting_heart_rate_description">靜止心率（RHR）是靜止時測量的心率，以反映心血管健康和體能。較低的 RHR 通常代表較佳的心率和調節。追蹤 RHR 也可以協助評估恢復情況，並據此調整訓練和休息期間。</string>
    <string name="about_resources_title">關於資源</string>
    <string name="about_resources_description">資源反映出您的日常復原與能源支出，協助您監控身體狀態並調整活動等級。能源通常會在睡眠期間恢復。</string>

    <!-- HRV instructions -->
    <string name="about_hrv_title">什麼是 HRV？</string>
    <string name="about_hrv_description">心率變異性 (HRV) 是指心跳間隔時間變化的測量指標。該指標反映了自主神經系統 (ANS) 的平衡，並提供了有關整體健康和壓力水準的深入分析。HRV 是瞭解自主神經功能和促進健康的強大工具。</string>
    <string name="how_to_read_hrv_data_title">要怎麼解讀資料？</string>
    <string name="how_to_read_hrv_data_description">HRV 的最佳數值，應保持在正常範圍內並稍微接近「過高」限值。雖然較高的 HRV 會與較佳的健康情況有關，但是它應該一律相對於您的基線進行解讀。例如放鬆，身體與精神費力程度或疾病（例如流感）等因素可能會在 HRV 造成波動。</string>
    <string name="how_to_measure_hrv_title">如何測量我的 HRV？</string>
    <string name="how_to_measure_hrv_description">1.Suunto 會在您睡眠期間測量 HRV。若要取得 HRV 資料，您應該在睡覺時佩戴手錶，並確保有在裝置上啟用「睡眠追蹤」功能。在整個睡眠期間持續測量心率變化率，以計算夜間的平均 RMSSD 值。RMSSD（連續差異的根本原因）是一種廣泛使用的 HRV 測量標準。</string>
    <string name="hrv_values_explained_title">2.數值說明</string>
    <string name="hrv_todays_value_explained">今天的 HRV 值來自昨天晚上的測量結果，而昨天的 HRV 值是前天晚上的測量結果。</string>
    <string name="hrv_seven_day_average_explained">7 天平均值是根據您過去 7 晚的 HRV 測量值計算得出。要建立此平均值，需要在 7 天內進行至少 3 次 HRV 測量。</string>
    <string name="hrv_normal_range_explained">要確定您的正常範圍，您應該在 60 天內進行 14 次測量。</string>

    <string name="sleep_heart_rate_compare">心率</string>
    <string name="sleep_heart_rate_hide_comparison">隱藏</string>
    <string name="sleep_heart_rate_min">最小%s</string>
    <string name="sleep_heart_rate_max">最大%s</string>

    <string name="sleep_stages_title">階段</string>
    <string name="sleep_stages_avg_awake">平均清醒</string>
    <string name="sleep_stages_avg_rem">平均值REM (眼球快速移動期)</string>
    <string name="sleep_stages_light">淺睡眠</string>
    <string name="sleep_stages_avg_light">平均淺睡眠時長</string>
    <string name="sleep_stages_avg_deep">平均深度睡眠時長</string>
    <string name="sleep_quality_title">睡眠品質</string>
    <string name="sleep_quality_arg_sleep_hr">平均睡眠心率</string>
    <string name="sleep_quality_min_sleep_hr">最小日間心率</string>
    <string name="sleep_quality_arg_sleep_hrv">平均睡眠心率</string>
    <string name="sleep_quality_max_sleep_spo2">最大睡眠呼吸器 CO</string>
    <string name="sleep_quality_max_altitude">最大%d</string>
    <string name="sleep_time_duration_of_target">已完成 %s</string>
    <string name="sleep_time_duration_of_sleep">%s 的睡眠</string>
    <string name="sleep_time_duration_of_nap">%s 的小睡</string>
    <string name="sleep_wake_up_resources_title">睡眠後的活力</string>
    <string name="sleep_wake_up_resources_gained_during_sleep">睡眠期間獲得</string>
    <string name="sleep_wake_up_resources_lost_during_sleep">睡眠期間流失</string>
    <string name="sleep_comparison_title">對比</string>
    <string name="sleep_comparison_graph_type_sleep_duration">睡眠持續時間</string>
    <string name="sleep_comparison_graph_type_sleep_regularity">睡眠規律</string>
    <string name="sleep_comparison_graph_type_nap_duration">小睡持續時間</string>
    <string name="sleep_comparison_graph_type_total_duration">總計時間</string>
    <string name="sleep_comparison_graph_type_blood_oxygen">最大睡眠呼吸器 CO</string>
    <string name="sleep_comparison_graph_type_training">訓練持續時間</string>
    <string name="sleep_comparison_graph_type_min_hr_during_sleep">最小日間心率</string>
    <string name="sleep_comparison_graph_type_avg_hr_during_sleep">平均睡眠心率</string>
    <string name="sleep_comparison_graph_type_morning_resources">睡眠後的活力</string>
    <string name="sleep_goal_title">每日睡眠目標</string>

    <string name="heart_rate_stat_resting">靜止心率</string>
    <string name="heart_rate_stat_average">平均值心率</string>
    <string name="heart_rate_stat_min_sleep">最小日間心率</string>
    <string name="heart_rate_stat_min_daytime">最小日間心率</string>
    <string name="chart_connect_commute">通勤</string>
    <string name="about_commute">什麼是通勤？</string>
    <string name="commute_instruction">通勤是指前往目的地。從起點起直線距離 500 公尺或以上的跑步，騎乘和步行等活動會自動標記為通勤。我們鼓勵騎乘和步行等環保選項，因為它們能減少碳排放量，改善空氣品質，並促進健康。除了自動指派的通勤標籤之外，您還可以手動編輯活動記錄以新增通勤標籤並記錄您對環境的貢獻。</string>
    <string name="exercises_counts">運動</string>
    <string name="commute_tags_title">自動標記通勤</string>
    <string name="commute_tags_comment">讓我們自動標記您所有的跑步，騎乘和健走，在開頭和端點之間至少有 500 公尺的直線航線，以作為通勤，並開始追蹤您省下的 COA 排放量。</string>

    <!-- Resource States Instructions -->
    <string name="how_to_read_data">如何讀取資料？</string>
    <string name="recovering">正在恢復</string>
    <string name="resources_rise_quickly">資源快速增加。</string>
    <string name="scenario_prefix">A. 情境：</string>
    <string name="recovering_scenario">深層放鬆，特別是優質的睡眠，這表示有最佳的恢復效果。</string>

    <string name="inactive">不活躍</string>
    <string name="resources_change_slowly">資源的變化速度緩慢且無法預測。</string>
    <string name="during_sleep_prefix">睡眠期間</string>
    <string name="inactive_sleep_desc">保持穩定或逐漸增加（不包括清醒時段），並緩慢恢復。</string>
    <string name="during_wakefulness_prefix">B. 醒來時：</string>
    <string name="inactive_wakefulness_desc">可稍微波動，視小活動而定。</string>

    <string name="active">已啟動</string>
    <string name="resources_decline">資源減少，與運動強度和恢復相關的速率也隨之降低。</string>
    <string name="active_scenario">每日活動或練習，資源會根據活動強度和恢復時間而改變。</string>

    <string name="stressed">承受壓力</string>
    <string name="resources_drop_rapidly">資源快速下降。</string>
    <string name="stressed_scenario">醒來時的壓力很快就會讓資源下降，表示高壓狀態。</string>

    <string name="how_resources_measured">如何評量資源？</string>
    <string name="resources_measured_based">資源是根據生理狀態測量而得</string>
    <string name="physiological_state">生理狀態。</string>
    <string name="active_state">使用中狀態</string>
    <string name="active_state_desc">「活動復原時間」反映出復原需求；復原時間越長，表示資源耗用速度越快。</string>
    <string name="inactive_state">非使用中狀態</string>
    <string name="inactive_state_desc">心率變化性（HRV）會測量自動的平衡。HRV 會指出高資源；HRV 訊號低的資源。RMSSD，壓力指數和 SDNN 等重要的 HRV 測量數據，加上心率（HR），有助於評估壓力等級。</string>

    <string name="resource_states_intro">圖表根據資源變更顯示四種狀態：</string>

    <string name="chart_connect_training_load">訓練負荷</string>
    <string name="about_tss">什麼是訓練壓力分數（TSS）？</string>
    <string name="tss_instruction">訓練壓力分數（TSS）會考量強度和持續時間，以計入訓練負荷，主要針對耐力運動。分數越高表示生理壓力越大。它通常會與心率和功率輸出資料搭配使用，強度取決於運動員的有氧閾值，在 Suunto 定義為區間 4 或 5 的心率，配速和功率限制。</string>

    <string name="vo2_max_fitness_age">體適能年齡</string>
    <string name="vo2_max_six_week_avg">6 週平均值</string>

    <!-- VO2MAX instructions -->
    <string name="about_vo2max_title">最大攝氧量是什麼？</string>
    <string name="about_vo2max_description">最大攝氧量 (VO₂max) 測量身體在高強度運動中可以使用的氧氣量，以每分鐘毫升數 (ml/kg/min) 表示。這反映了心血管健康和耐力—數值越高表示表現越好。最大攝氧量會受到年齡，性別和訓練等級等因素影響。改善最大攝氧量可提升運動能力，Suunto 可根據心率和速度評估此項指標。</string>
    <string name="how_to_get_vo2max_title">如何取得最大攝氧量的值？</string>
    <string name="how_to_get_vo2max_description">您的手錶會透過分析在訓練期間收集到的資料（包括心率，配速和運動強度）來預估最大攝氧量。例如，戶外跑步至少 10 分鐘可提供估計值。此外，您也可以使用 SuuntoPlus 中的最大攝氧量測試 (Cooper Test) 來評估您的最大攝氧量容量。</string>

    <!-- How to use VO2MAX -->
    <string name="how_to_use_vo2max_title">如何使用最大攝氧量？</string>
    <string name="how_to_use_vo2max_description">較高的最大攝氧量通常代表心血管功能和供氧效果更好。追蹤此測量標準可提供心臟和肺部健康的深入分析，協助您判斷是否需要更多有氧訓練或生活型態調整。比較您的最大攝氧量與年齡 / 性別標準，或是您過去的資料，有助於評估有氧能力。這份深入分析可引導您調整運動計畫，無論是要加強或維持目前的例行訓練，都能找到適合的時間。長期測量透過數月或數年的定期最大攝氧量測量可以顯示您的健康狀況是正在改善、穩定還是下降。及早找出模式可讓您調整訓練量和強度，以跟上目標。</string>
</resources>
