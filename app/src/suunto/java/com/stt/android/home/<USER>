package com.stt.android.home

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.stt.android.utils.awaitFirstNonNull
import com.stt.android.utils.isBackgroundLocationPermissionGranted
import com.stt.android.watch.SuuntoWatchModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

internal class ShouldRequestBackgroundLocationPermissionForWeatherUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    @SuuntoSharedPrefs private val suuntoSharedPrefs: SharedPreferences,
    @ApplicationContext private val appContext: Context,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(): Boolean = withContext(coroutinesDispatchers.io) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            return@withContext false
        }

        if (appContext.isBackgroundLocationPermissionGranted()) {
            return@withContext false
        }

        if (suuntoSharedPrefs.getBoolean(STTConstants.SuuntoPreferences.KEY_HAS_SHOWN_BACKGROUND_LOCATION_FOR_WEATHER_TUTORIAL, false)) {
            return@withContext false
        }

        runSuspendCatching {
            // If the watch requires connected GPS, we don't show the UI for weather, and that case
            // is already handled in HomeActivity / HomeViewModel.
            // TODO Merge the handling into this use case.
            !suuntoWatchModel.currentWatch()
                .stateChangeObservable
                .awaitFirstNonNull()
                .isConnectedGpsInUse
        }.getOrElse { e ->
            Timber.w(e, "Error while checking background location permission for weather")
            false
        }
    }
}
