package com.stt.android.home;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.TaskStackBuilder;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Transformations;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.navigation.NavController;
import androidx.work.WorkManager;
import com.android.installreferrer.api.InstallReferrerClient;
import com.android.installreferrer.api.InstallReferrerStateListener;
import com.android.installreferrer.api.ReferrerDetails;
import com.stt.android.BuildConfig;
import com.stt.android.FeatureFlags;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.app.R;
import com.stt.android.app.databinding.HomeActivityBinding;
import com.stt.android.appversion.AppVersionViewModel;
import static com.stt.android.common.navigation.NavigationExtensionsKt.findNavController;
import com.stt.android.common.ui.SimpleDialogFragment;
import com.stt.android.common.ui.UiExtensionsKt;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.domain.diary.models.GraphDataType;
import com.stt.android.domain.workouts.WorkoutHeader;
import static com.stt.android.home.AutoTaggedDialogFragment.ON_AUTO_TAGGING_DIALOG_DISMISSED;
import static com.stt.android.home.HomeConstantsKt.KEY_ANALYTICS_SOURCE;
import static com.stt.android.home.HomeConstantsKt.KEY_SHOW_FOLLOWING_TAB;
import static com.stt.android.home.HomeConstantsKt.PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED;
import com.stt.android.home.dashboard.BaseDashboardFragment;
import com.stt.android.home.dashboard.DashboardFragment;
import com.stt.android.home.diary.Diary;
import com.stt.android.home.diary.diarycalendar.CalendarTab;
import com.stt.android.home.diary.diarycalendar.NewDiaryCalendarContainerFragment;
import static com.stt.android.home.diary.diarycalendar.planner.DiaryCalendarPlannerFragment.KEY_SHOW_CALENDAR_AI_PROGRAM_ID;
import com.stt.android.home.explore.ExploreNavigator;
import com.stt.android.inappreview.InAppReviewSource;
import com.stt.android.inappreview.InAppReviewTrigger;
import com.stt.android.launcher.DeepLinkIntentBuilder;
import com.stt.android.launcher.ProxyActivity;
import com.stt.android.questionnaire.QuestionnaireMode;
import com.stt.android.questionnaire.QuestionnaireNavigator;
import com.stt.android.remote.appversion.CheckAppVersionRemoteApiKt;
import com.stt.android.session.PhoneNumberVerificationForExistingUserHook;
import com.stt.android.tooltips.ShowCO2EmissionsReducedToolTipLiveData;
import com.stt.android.ui.activities.AppUpdateActivity;
import com.stt.android.ui.activities.settings.PowerManagementSettingsActivity;
import com.stt.android.ui.components.BottomNavigationBar;
import com.stt.android.ui.fragments.login.terms.OnTermsListener;
import com.stt.android.ui.fragments.login.terms.TermsActivity;
import com.stt.android.ui.tasks.LogoutTask;
import com.stt.android.utils.STTConstants;
import static com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI;
import static com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_DEFAULT;
import static com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER;
import static com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER_DEFAULT;
import com.stt.android.workouts.edit.SaveWorkoutHeaderService;
import dagger.Lazy;
import io.reactivex.disposables.CompositeDisposable;
import java.io.Serializable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import kotlin.Pair;
import kotlin.Unit;
import pub.devrel.easypermissions.EasyPermissions;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

public abstract class BaseHomeActivity extends AppCompatActivity
    implements OnTermsListener, SharedPreferences.OnSharedPreferenceChangeListener,
    SimpleDialogFragment.Callback, HomeActivityActions {

    public static class Navigator implements HomeActivityNavigator {
        @Inject
        public Navigator() {
        }

        @NonNull
        @Override
        public Intent newStartIntent(@NonNull Context context) {
            return BaseHomeActivity.newStartIntent(context);
        }

        @NonNull
        @Override
        public Intent newStartIntent(@NonNull Context context, boolean fromNewMapNotification) {
            return BaseHomeActivity.newStartIntent(context, fromNewMapNotification);
        }

        @NonNull
        @Override
        public Intent newStartIntent(@NonNull Context context,
            boolean fromNewMapNotification, boolean newRoute, boolean showHeartRateSetting) {
            return BaseHomeActivity.newStartIntent(context, fromNewMapNotification, newRoute, showHeartRateSetting);
        }

        @Override
        public @NonNull Intent newStartIntentToHome(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToHome(context);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryWorkoutList(
            @NonNull Context context,
            @Nullable GraphDataType primaryGraphType,
            @Nullable GraphDataType secondaryGraphType
        ) {
            return BaseHomeActivity.newStartIntentToDiaryWorkoutList(context, primaryGraphType, secondaryGraphType);
        }

        @NonNull
        @Override
        public Intent newStartIntentToPeopleTab(@NonNull Context context,
            boolean fromFollowNotification, boolean showPendingRequests, boolean showFollowingTab) {
            return BaseHomeActivity.newStartIntentToPeopleTab(
                context, fromFollowNotification, showPendingRequests, showFollowingTab);
        }

        @NonNull
        @Override
        public TaskStackBuilder getTaskStackBuilder(@NonNull Context context, @NonNull Intent intent) {
            TaskStackBuilder builder = TaskStackBuilder.create(context);
            builder.addParentStack(HomeActivity.class);
            builder.addNextIntent(intent);
            return builder;
        }

        @NonNull
        @Override
        public Intent newStartIntentToExploreTab(
            @NonNull Context context,
            boolean showMaps,
            String analyticsSource
        ) {
            return BaseHomeActivity.newStartIntentToExploreTab(
                context, showMaps, null, null, null, null, analyticsSource);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryCalendar(
            @NonNull Context context,
            @NonNull String source,
            boolean showActivitiesList
        ) {
            return BaseHomeActivity.newStartIntentToDiaryCalendar(context, source, showActivitiesList);
        }

        @NonNull
        @Override
        public Intent newStartIntentToAiPlanner(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiaryCalendar_AiProgramsTab(context, null, null);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryStepsTab(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiaryStepsTab(context);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryCaloriesTab(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiaryCaloriesTab(context);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiarySleepTab(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiarySleepTab(context);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryProgressTab(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiaryProgressTab(context);
        }

        @NonNull
        @Override
        public Intent newStartIntentToDiaryRecoveryTab(@NonNull Context context) {
            return BaseHomeActivity.newStartIntentToDiaryRecoveryTab(context);
        }
    }

    private static final String KEY_SHOW_HEART_RATE = "com.stt.android.KEY_SHOW_HEART_RATE";
    public static final String KEY_SHOW_DIARY_TAB_TYPE = "com.stt.android.KEY_SHOW_DIARY_TAB_TYPE";
    public static final String KEY_SHOW_DIARY_PRIMARY_GRAPH_TYPE = "com.stt.android.KEY_SHOW_DIARY_PRIMARY_GRAPH_TYPE";
    public static final String KEY_SHOW_DIARY_SECONDARY_GRAPH_TYPE = "com.stt.android.KEY_SHOW_DIARY_SECONDARY_GRAPH_TYPE";
    public static final String KEY_SHOW_MOTIVATION_QUESTIONNAIRE = "com.stt.android.KEY_SHOW_MOTIVATION_QUESTIONNAIRE";
    public static final String KEY_SHOW_HOME = "com.stt.android.KEY_SHOW_HOME";
    public static final String KEY_SHOW_CALENDAR = "com.stt.android.KEY_SHOW_CALENDAR";
    public static final String KEY_SHOW_CALENDAR_AI_PLANNER_TAB = "com.stt.android.KEY_SHOW_CALENDAR_AI_PLANNER_TAB";
    public static final String KEY_SHOW_EXPLORE = "com.stt.android.KEY_SHOW_EXPLORE";
    public static final String KEY_SHOW_PEOPLE = "com.stt.android.KEY_SHOW_PEOPLE";
    public static final String KEY_SHOW_WEEKLY_GOAL = "com.stt.android.KEY_SHOW_WEEKLY_GOAL";
    public static final String KEY_SHOW_CO2_EMISSIONS_REDUCED = "com.stt.android.KEY_SHOW_CO2_EMISSIONS_REDUCED";
    public static final String KEY_SWITCH_MAP_STYLE = "com.stt.android.KEY_SWITCH_MAP_STYLE";
    public static final String KEY_MAP_STYLE_ENABLE_3D = "com.stt.android.KEY_MAP_STYLE_ENABLE_3D";
    public static final String KEY_MAP_ROAD_SURFACE = "com.stt.android.KEY_MAP_ROAD_SURFACE";
    public static final String KEY_MAP_HIDE_CYCLING_FORBIDDEN = "com.stt.android.KEY_MAP_HIDE_CYCLING_FORBIDDEN";
    public static final String KEY_SUBSCRIBE_MARKETING_CONSENT = "com.stt.android.KEY_SUBSCRIBE_MARKETING_CONSENT";

    public static final String KEY_SHOW_DIARY_CALENDAR_SOURCE = "com.stt.android.KEY_SHOW_DIARY_SOURCE";
    public static final String KEY_SHOW_CALENDAR_ACTIVITIES_LIST = "com.stt.android.KEY_SHOW_CALENDAR_ACTIVITIES_LIST";

    private static final String KEY_SELECTED_TAB = "com.stt.android.KEY_SELECTED_TAB";

    private static final String KEY_FROM_NEW_MAP_NOTIFICATION =
        "com.stt.android.KEY_FROM_NEW_MAP_NOTIFICATION";

    private static final String KEY_FROM_FOLLOW_NOTIFICATION =
        "com.stt.android.KEY_FROM_FOLLOW_NOTIFICATION";

    private static final String GPS_TRACKING_INTERRUPTED_DIALOG = "gpsTrackingInterruptedDialog";
    private static final String PHONE_NUMBER_VERIFICATION_DEBUG_DIALOG = "PHONE_NUMBER_VERIFICATION_DEBUG_DIALOG";

    private static final int TAB_HOME = 0;
    protected static final int TAB_CALENDAR = 1;
    protected static final int TAB_DIARY = 2;
    private static final int TAB_MAP = 3;

    private static final int REQUEST_POST_NOTIFICATION = 201;

    @Inject
    ExploreNavigator exploreNavigator;

    @Inject
    QuestionnaireNavigator questionnaireNavigator;

    @Inject
    DeepLinkIntentBuilder deepLinkIntentBuilder;

    @Inject
    FeatureFlags featureFlags;

    @Nullable
    InstallReferrerClient installReferrerClient = null;

    public static Intent newStartIntent(Context context) {
        return new Intent(context, HomeActivity.class)
            .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
    }

    public static Intent newStartIntent(Context context, boolean fromNewMapNotification) {
        return newStartIntent(context, fromNewMapNotification, false, false);
    }

    public static Intent newStartIntent(Context context, boolean fromNewMapNotification,
        boolean newRoute, boolean showHeartRateSetting) {
        return newStartIntent(context)
            .putExtra(KEY_FROM_NEW_MAP_NOTIFICATION, fromNewMapNotification)
            .putExtra(HomeConstantsKt.KEY_NEW_ROUTE, newRoute)
            .putExtra(KEY_SHOW_HEART_RATE, showHeartRateSetting);
    }

    public static Intent newStartIntentToHome(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_HOME, true);
    }

    public static Intent newStartIntentToPeopleTab(Context context, boolean fromFollowNotification,
        boolean showPendingRequests, boolean showFollowingTab) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_PEOPLE, true)
            .putExtra(KEY_FROM_FOLLOW_NOTIFICATION, fromFollowNotification)
            .putExtra(HomeConstantsKt.KEY_SHOW_PENDING_REQUESTS, showPendingRequests)
            .putExtra(KEY_SHOW_FOLLOWING_TAB, showFollowingTab);
    }

    public static Intent newStartIntentToDiaryWorkoutList(
        Context context,
        @Nullable GraphDataType primaryGraphType,
        @Nullable GraphDataType secondaryGraphType
    ) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.WORKOUTS)
            .putExtra(KEY_SHOW_DIARY_PRIMARY_GRAPH_TYPE, primaryGraphType)
            .putExtra(KEY_SHOW_DIARY_SECONDARY_GRAPH_TYPE, secondaryGraphType);
    }

    public static Intent newStartIntentToDiaryCalendar(
        Context context,
        String source,
        boolean showActivitiesList
    ) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_CALENDAR, true)
            .putExtra(KEY_SHOW_DIARY_CALENDAR_SOURCE, source)
            .putExtra(KEY_SHOW_CALENDAR_ACTIVITIES_LIST, showActivitiesList);
    }

    public static Intent newStartIntentToDiaryCalendar(Context context, Uri uri) {
        return newStartIntent(context)
            .setData(uri)
            .putExtra(KEY_SHOW_CALENDAR, true);
    }

    public static Intent newStartIntentToDiaryCalendar_AiProgramsTab(Context context, Uri uri, String programId) {
        return newStartIntent(context)
            .setData(uri)
            .putExtra(KEY_SHOW_CALENDAR, true)
            .putExtra(KEY_SHOW_CALENDAR_AI_PLANNER_TAB, true)
            .putExtra(KEY_SHOW_CALENDAR_AI_PROGRAM_ID, programId);
    }

    public static Intent newStartIntentToDiaryStepsTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.DAILY_ACTIVITY);
    }

    public static Intent newStartIntentToDiaryCaloriesTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.DAILY_ACTIVITY);
    }

    public static Intent newStartIntentToDiarySleepTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.SLEEP);
    }

    public static Intent newStartIntentToDiaryProgressTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.PROGRESS);
    }

    public static Intent newStartIntentToDiarySummaryTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.SUMMARY);
    }

    public static Intent newStartIntentToDiaryRecoveryTab(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_DIARY_TAB_TYPE, Diary.TabType.RECOVERY);
    }

    public static Intent newStartIntentToMotivationsQuestionnaire(Context context) {
        return newStartIntent(context).putExtra(KEY_SHOW_MOTIVATION_QUESTIONNAIRE, true);
    }

    public static Intent newStartIntentToExploreTab(
        Context context,
        boolean showMaps,
        @Nullable String mapStyle,
        @Nullable Boolean enable3D,
        @Nullable List<String> roadSurfaces,
        @Nullable Boolean hideCyclingForbidden,
        String analyticsSource
    ) {
        Intent intent = newStartIntent(context)
            .putExtra(KEY_SHOW_EXPLORE, true)
            .putExtra(KEY_ANALYTICS_SOURCE, analyticsSource)
            .putExtra(KEY_SWITCH_MAP_STYLE, mapStyle)
            .putExtra(HomeConstantsKt.KEY_SHOW_MAPS, showMaps);

        if (enable3D != null) {
            intent.putExtra(KEY_MAP_STYLE_ENABLE_3D, enable3D);
        }

        if (roadSurfaces != null) {
            intent.putStringArrayListExtra(KEY_MAP_ROAD_SURFACE, new ArrayList<>(roadSurfaces));
        }

        if (hideCyclingForbidden != null) {
            intent.putExtra(KEY_MAP_HIDE_CYCLING_FORBIDDEN, hideCyclingForbidden);
        }

        return intent;
    }

    public static Intent newStartIntentToRoutes(Context context) {
        return newStartIntent(context)
            .putExtra(HomeConstantsKt.KEY_SHOW_ROUTES, true);
    }

    public static Intent newStartIntentToWeeklyGoal(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_WEEKLY_GOAL, true);
    }

    public static Intent newStartIntentToCO2Widget(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SHOW_CO2_EMISSIONS_REDUCED, true);
    }

    public static Intent newStartIntentToSubscribeMarketingConsent(Context context) {
        return newStartIntent(context)
            .putExtra(KEY_SUBSCRIBE_MARKETING_CONSENT, true);
    }

    public static Intent newStartIntentToLogMenstrualCycle(Context context) {
        return newStartIntent(context)
            .putExtra(BaseDashboardFragment.EXTRA_LOG_MENSTRUAL_CYCLE, true);
    }

    @Inject
    CurrentUserController currentUserController;

    @Inject
    UserSettingsController userSettingsController;

    @Inject
    WorkoutHeaderController workoutHeaderController;

    @Inject
    Lazy<LogoutTask> logoutTask;

    protected HomeViewModel viewModel;

    protected AppVersionViewModel appVersionViewModel;

    @Inject
    LocalBroadcastManager localBM;

    @Inject
    WorkManager workManager;

    @Inject
    SharedPreferences sharedPreferences;

    @Inject
    PhoneNumberVerificationForExistingUserHook phoneNumberVerificationForExistingUserHook;

    @Inject
    InAppReviewTrigger inAppReviewTrigger;

    @Inject
    ShowCO2EmissionsReducedToolTipLiveData showCO2EmissionsReducedToolTipLiveData;

    @Inject
    @FeatureTogglePreferences
    SharedPreferences featureTogglePreferences;

    protected HomeActivityBinding binding;
    protected BottomNavigationBar bottomBar;
    protected NavController navController;

    private boolean showNewRoute;
    private boolean showRoutes;
    private boolean showMaps;
    private Diary.TabType showDiaryTabType;
    private GraphDataType showDiaryPrimaryGraphDataType;
    private GraphDataType showDiarySecondaryGraphDataType;
    private String showDiaryCalendarSource;
    private String mapScreenSourceOverride;

    private boolean showCalendarActivitiesList;
    private Subscription loadUnseenPendingFollowRequestsSubscription;
    private Subscription unseenWorkoutsCountSubscription;

    private CalendarTab showCalendarTabType;

    private final CompositeDisposable disposable = new CompositeDisposable();

    private final BroadcastReceiver syncFinishedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            viewModel.showAutoTaggedDialogIfNeeded();
        }
    };

    private final OnBackPressedCallback goToHomeTabOnBackPressedCallback = new OnBackPressedCallback(false) {
        @Override
        public void handleOnBackPressed() {
            goToHomeTab();
        }
    };

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.home_activity);
        bottomBar = binding.bottomNavigationBar;
        ViewModelProvider viewModelProvider =
            new ViewModelProvider(this, getDefaultViewModelProviderFactory());
        viewModel = viewModelProvider.get(HomeViewModel.class);
        appVersionViewModel = viewModelProvider.get(AppVersionViewModel.class);
        navController = findNavController(this, R.id.nav_host_fragment_container);

        getOnBackPressedDispatcher().addCallback(this, goToHomeTabOnBackPressedCallback);

        initUi();

        handleNavigation(savedInstanceState);

        initBottomBarReselectListener();

        observeEnforceLogoutEvent();
        observePhoneNumberVerificationEvent();

        initAutoTaggedDialog();
        observeAppVersion();
    }

    private void observeAppVersion() {
        UiExtensionsKt.observeK(
            Transformations.distinctUntilChanged(appVersionViewModel.getNewVersionLive()),
            this,
            info -> {
                if (info != null && CheckAppVersionRemoteApiKt.isShowUpdateToUser(info)) {
                    Intent intent = AppUpdateActivity.newIntent(this, info);
                    startActivity(intent);
                }
                return Unit.INSTANCE;
            }
        );
    }

    private void initAutoTaggedDialog() {
        viewModel.getShowTrackingCO2EmissionsReducedTooltip()
            .observe(this, unit -> showCO2EmissionsReducedToolTipLiveData.sendShowTooltipEvent(false));

        getSupportFragmentManager().setFragmentResultListener(
            ON_AUTO_TAGGING_DIALOG_DISMISSED,
            this,
            (requestKey, result) -> viewModel.onAutoTaggedDialogDismissed()
        );

        viewModel.getShowAutoTaggedDialog()
            .observe(this, workoutHeader ->
                AutoTaggedDialogFragment.showAdaptive(workoutHeader, getSupportFragmentManager(), this)
            );
    }

    @SuppressWarnings("ConstantConditions")
    private void observePhoneNumberVerificationEvent() {
        viewModel.getSmsVerificationNeeded().observe(this, o -> {
            if (BuildConfig.BUILD_TYPE.equals("debug")) {
                // in debug build we do optional phone number verification
                SimpleDialogFragment phoneNumberVerificationDebugDialog =
                    SimpleDialogFragment.newInstance(
                        "Proceed to phone number verification?",
                        "Debug Phone Number Verification",
                        "yes",
                        "no");
                phoneNumberVerificationDebugDialog.show(
                    getSupportFragmentManager(),
                    PHONE_NUMBER_VERIFICATION_DEBUG_DIALOG);
            } else if (BuildConfig.BACKEND_POINTING_TO_CHINA){
                startPhoneNumberVerification();
            }
        });
    }

    private void startPhoneNumberVerification() {
        startActivity(phoneNumberVerificationForExistingUserHook.newStartIntent(this));
        finish();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (sharedPreferences.getBoolean(PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED, false)) {
            reportGpsTrackingInterruptionIfNeeded();
        }
        sharedPreferences.registerOnSharedPreferenceChangeListener(this);
        viewModel.showAutoTaggedDialogIfNeeded();

        // Checking bottomBar's current tab in onCreate / onStart always returns TAB_HOME, so checking
        // the initial enabled state of the backPressed handler is delayed to happen here
        goToHomeTabOnBackPressedCallback.setEnabled(bottomBar.getCurrentTabPosition() != TAB_HOME);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
            !viewModel.getAreNotificationsEnabled() &&
            !viewModel.isPostNotificationAsked()) {
                requestPermissions(
                    new String[] { Manifest.permission.POST_NOTIFICATIONS },
                    REQUEST_POST_NOTIFICATION
                );
                viewModel.setPostNotificationAsked();
        }

        checkAppVersion();
    }

    private void checkAppVersion() {
        appVersionViewModel.checkAppUpdateIfNeeded();
    }

    @Override
    protected void onPause() {
        super.onPause();
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(this);
    }

    private void observeEnforceLogoutEvent() {
        viewModel.getEnforceLogout().observe(this, enforceLogout -> {
            if (enforceLogout) {
                // logging out
                Timber.d("observeEnforceLogoutEvent: forcing logout");
                disposable.add(
                    logoutTask.get()
                        .logoutWithProgressDialog(this, getSupportFragmentManager())
                        .subscribe(
                            () -> Timber.d("observeEnforceLogoutEvent: logout complete"),
                            (error) -> Timber.w(error, "observeEnforceLogoutEvent: failed")
                        )
                );
            }
        });
    }

    public void handleNavigation(@Nullable Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            return;
        }

        if (!handleDeepLinkNavigation(getIntent().getExtras())) {
            bottomBar.selectTabAtPosition(TAB_HOME);
            checkPlayStoreInstallReferrer();
        }
    }

    private void checkPlayStoreInstallReferrer() {
        boolean alreadyChecked = sharedPreferences.getBoolean(
            STTConstants.DefaultPreferences.KEY_CHECKED_PLAYSTORE_INSTALL_REFERRER,
            false);
        if (alreadyChecked) return;
        if (installReferrerClient == null) {
            installReferrerClient = InstallReferrerClient.newBuilder(this).build();
        }
        installReferrerClient.startConnection(new InstallReferrerStateListener() {
            @Override
            public void onInstallReferrerSetupFinished(int responseCode) {
                Timber.d("onInstallReferrerSetupFinished with responseCode: %d", responseCode);
                switch (responseCode) {
                    case InstallReferrerClient.InstallReferrerResponse.OK -> {
                        try {
                            ReferrerDetails installReferrer =
                                installReferrerClient.getInstallReferrer();
                            evaluateInstallReferrer(installReferrer);
                        } catch (Exception e) {
                            Timber.w(e, "Error getting install referrer");
                        }
                    }
                    case InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED ->
                        Timber.w("Cannot evaluate install referrer, API not available");
                    case InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE ->
                        Timber.w("Cannot evaluate install referrer, connection not established");
                    case InstallReferrerClient.InstallReferrerResponse.DEVELOPER_ERROR ->
                        Timber.w("Cannot evaluate install referrer, developer error");
                    case InstallReferrerClient.InstallReferrerResponse.SERVICE_DISCONNECTED ->
                        Timber.w("Cannot evaluate install referrer, service disconnected");
                }
                sharedPreferences.edit()
                    .putBoolean(STTConstants.DefaultPreferences.KEY_CHECKED_PLAYSTORE_INSTALL_REFERRER, true)
                    .apply();
                releaseInstallReferrerClient();
            }

            @Override
            public void onInstallReferrerServiceDisconnected() {
                Timber.w("onInstallReferrerServiceDisconnected");
                releaseInstallReferrerClient();
            }
        });
    }

    private void evaluateInstallReferrer(ReferrerDetails referrerDetails) {
        String installReferrer = referrerDetails.getInstallReferrer();
        Timber.w("Evaluating install referrer with link: %s", installReferrer);
        long millisDiff = System.currentTimeMillis() -
            referrerDetails.getInstallBeginTimestampSeconds();
        boolean notTooLongAgo = Duration.ofMillis(millisDiff).toHours() <= 100;
        if (notTooLongAgo) {
            Intent deepLinkIntent =
                deepLinkIntentBuilder.getDeepLinkIntentFromInstallReferral(
                    this,
                    installReferrer,
                    currentUserController
                );
            startActivity(deepLinkIntent);
        } else {
            Timber.d("Install Referrer ignored because installation is too old");
        }
    }

    private void releaseInstallReferrerClient() {
        if (installReferrerClient != null) {
            try {
                installReferrerClient.endConnection();
                installReferrerClient = null;
            } catch (Throwable ignored) {
            }
        }
    }

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);

        // Handle deep link navigation when the HomeActivity is already open and visible to user
        Bundle bundle = intent.getExtras() != null ? intent.getExtras() : new Bundle();
        handleDeepLinkNavigation(bundle);
    }

    /**
     * Handle deep link related navigation
     * @param bundle Bundle containing intent parameters
     * @return Did the navigation handling select a tab in the bottom navigation
     */
    private boolean handleDeepLinkNavigation(@Nullable Bundle bundle) {
        boolean openedTab = false;

        if (bundle == null) return false;

        if (bundle.getBoolean(HomeConstantsKt.KEY_NEW_ROUTE)) {
            getIntent().removeExtra(HomeConstantsKt.KEY_NEW_ROUTE);
            showNewRoute = true;
            bottomBar.selectTabAtPosition(TAB_MAP);
            openedTab = true;
        }

        if (bundle.getBoolean(KEY_SHOW_EXPLORE)) {
            getIntent().removeExtra(KEY_SHOW_EXPLORE);

            if (bundle.getBoolean(HomeConstantsKt.KEY_SHOW_MAPS)) {
                getIntent().removeExtra(HomeConstantsKt.KEY_SHOW_MAPS);
                showMaps = true;
            }

            String mapStyle = bundle.getString(KEY_SWITCH_MAP_STYLE);

            Boolean enable3D = bundle.containsKey(KEY_MAP_STYLE_ENABLE_3D)
                ? bundle.getBoolean(KEY_MAP_STYLE_ENABLE_3D)
                : null;

            List<String> roadSurfaces = bundle.getStringArrayList(KEY_MAP_ROAD_SURFACE);

            Boolean hideCyclingForbidden = bundle.containsKey(KEY_MAP_HIDE_CYCLING_FORBIDDEN)
                ? bundle.getBoolean(KEY_MAP_HIDE_CYCLING_FORBIDDEN)
                : null;

            if (mapStyle != null || enable3D != null || roadSurfaces != null || hideCyclingForbidden != null) {
                viewModel.setMapStyleFromDeepLink(mapStyle, enable3D, roadSurfaces, hideCyclingForbidden);
            }

            mapScreenSourceOverride = bundle.getString(KEY_ANALYTICS_SOURCE);

            bottomBar.selectTabAtPosition(TAB_MAP);
            openedTab = true;
        }

        if (bundle.getBoolean(KEY_SHOW_CALENDAR)) {
            getIntent().removeExtra(KEY_SHOW_CALENDAR);
            if (bundle.getBoolean(KEY_SHOW_CALENDAR_AI_PLANNER_TAB, false)) {
                showCalendarTabType = CalendarTab.PROGRAMS_TAB_POSITION;
            } else {
                showCalendarTabType = CalendarTab.CALENDAR_TAB_POSITION;
            }
            getIntent().removeExtra(KEY_SHOW_CALENDAR_AI_PLANNER_TAB);

            showCalendarActivitiesList = bundle.getBoolean(KEY_SHOW_CALENDAR_ACTIVITIES_LIST, false);
            getIntent().removeExtra(KEY_SHOW_CALENDAR_ACTIVITIES_LIST);

            bottomBar.selectTabAtPosition(TAB_CALENDAR);
            openedTab = true;
        }

        if (bundle.getBoolean(KEY_SHOW_HOME)) {
            getIntent().removeExtra(KEY_SHOW_HOME);

            bottomBar.selectTabAtPosition(TAB_HOME);
            openedTab = true;
        }

        Serializable diaryTypeExtra = bundle.getSerializable(KEY_SHOW_DIARY_TAB_TYPE);
        if (diaryTypeExtra != null) {
            showDiaryTabType = (diaryTypeExtra instanceof Diary.TabType)
                ? (Diary.TabType)diaryTypeExtra
                : null;
            getIntent().removeExtra(KEY_SHOW_DIARY_TAB_TYPE);

            Serializable graphTypeExtra = bundle.getSerializable(KEY_SHOW_DIARY_PRIMARY_GRAPH_TYPE);
            showDiaryPrimaryGraphDataType = (graphTypeExtra instanceof GraphDataType)
                ? (GraphDataType)graphTypeExtra
                : null;
            getIntent().removeExtra(KEY_SHOW_DIARY_PRIMARY_GRAPH_TYPE);

            graphTypeExtra = bundle.getSerializable(KEY_SHOW_DIARY_SECONDARY_GRAPH_TYPE);
            showDiarySecondaryGraphDataType = (graphTypeExtra instanceof GraphDataType)
                ? (GraphDataType)graphTypeExtra
                : null;
            getIntent().removeExtra(KEY_SHOW_DIARY_SECONDARY_GRAPH_TYPE);

            showDiaryCalendarSource = bundle.getString(KEY_SHOW_DIARY_CALENDAR_SOURCE);
            if (showDiaryCalendarSource != null) {
                getIntent().removeExtra(KEY_SHOW_DIARY_CALENDAR_SOURCE);
            }
            bottomBar.selectTabAtPosition(TAB_DIARY);
            openedTab = true;
        }

        if (bundle.getBoolean(HomeConstantsKt.KEY_SHOW_ROUTES)) {
            getIntent().removeExtra(HomeConstantsKt.KEY_SHOW_ROUTES);
            showRoutes = true;
            bottomBar.selectTabAtPosition(TAB_MAP);
            openedTab = true;
        }

        if (bundle.getBoolean(KEY_SHOW_WEEKLY_GOAL)) {
            getIntent().removeExtra(KEY_SHOW_WEEKLY_GOAL);
            showWeeklyGoal();
        }

        if (bundle.getBoolean(KEY_SHOW_CO2_EMISSIONS_REDUCED)) {
            getIntent().removeExtra(KEY_SHOW_CO2_EMISSIONS_REDUCED);
            showCO2EmissionsReducedToolTipLiveData.sendShowTooltipEvent(true);
        }

        if (bundle.getBoolean(KEY_SHOW_MOTIVATION_QUESTIONNAIRE)) {
            getIntent().removeExtra(KEY_SHOW_MOTIVATION_QUESTIONNAIRE);
            startActivity(questionnaireNavigator.newStartIntent(
                this,
                QuestionnaireMode.MOTIVATION_QUESTIONNAIRE,
                new Pair<>(com.stt.android.R.anim.fade_in, com.stt.android.R.anim.slide_out_down),
                AnalyticsPropertyValue.SurveySkippedContext.OTHER
            ));
        }

        return openedTab;
    }

    protected abstract void showWeeklyGoal();

    private void initUi() {
        bottomBar.setOnNavigationItemSelectedListener(
                menuItem -> {
                    viewModel.setNavigationItemSelected(menuItem.getItemId());
                    return true;
                });
    }

    protected void handleNavigationItemSelected(int id) {
        Fragment fragment;
        String tag;
        FragmentManager fm = getSupportFragmentManager();
        FragmentTransaction transaction = fm.beginTransaction();
        if (id == com.stt.android.R.id.bottomBarDashboard) {
            boolean isNewHomeUiEnabled = currentUserController.isFieldTester()
                ? featureTogglePreferences.getBoolean(KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER, KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER_DEFAULT)
                : featureTogglePreferences.getBoolean(KEY_ENABLE_NEW_HOME_UI, KEY_ENABLE_NEW_HOME_UI_DEFAULT);
            fragment = isNewHomeUiEnabled
                ? com.stt.android.home.dashboardv2.BaseDashboardFragment.Companion.newInstance()
                : DashboardFragment.newInstance();
            tag = DashboardFragment.FRAGMENT_TAG;

            // We have some scenarios that requires the user to do something in other tabs like maps in order to show the in-app review
            // but as a requirement we have to show it only when the user navigate back to home screen
            // example: the user opens maps screen, do some clicks there (here we schedule the in-app review), when the user navigate back to home, then we trigger the in-app review
            // Not a rotation, consider as a new opening
            inAppReviewTrigger.incNumberOfVisitsForSource(InAppReviewSource.HOME);
            inAppReviewTrigger.scheduleInAppReviewIfPossible(false);
            checkInAppReviewTrigger();
        } else if (id == com.stt.android.R.id.bottomBarDiary) {
            fragment = getDiaryFragment(
                showDiaryTabType,
                showDiaryPrimaryGraphDataType,
                showDiarySecondaryGraphDataType,
                showDiaryCalendarSource
            );
            showDiaryTabType = null;
            showDiaryPrimaryGraphDataType = null;
            showDiarySecondaryGraphDataType = null;
            showDiaryCalendarSource = null;
            tag = Diary.FRAGMENT_TAG;
        } else if (id == com.stt.android.R.id.bottomBarCalendar) {
            // entry for new calendar tab
            tag = NewDiaryCalendarContainerFragment.FRAGMENT_TAG;
            fragment = fm.findFragmentByTag(tag);
            if (showCalendarActivitiesList || fragment == null) {
                fragment = NewDiaryCalendarContainerFragment.newInstance(
                    showCalendarActivitiesList,
                    featureFlags.isAiPlannerEnabled(),
                    showCalendarTabType
                );
            }

            showCalendarActivitiesList = false;
            showCalendarTabType = null;
        } else if (id == com.stt.android.R.id.bottomBarExplore) {
            tag = exploreNavigator.getTag();
            if (showNewRoute) {
                showNewRoute = false;
                fragment = exploreNavigator.newInstanceNewRoute();
            } else if (showRoutes) {
                showRoutes = false;
                fragment = exploreNavigator.newInstanceShowRoutes();
            } else if (showMaps) {
                showMaps = false;
                fragment = exploreNavigator.newInstanceShowMaps();
            } else {
                if (fm.findFragmentByTag(tag) == null) {
                    fragment = exploreNavigator.newInstance(mapScreenSourceOverride);
                    mapScreenSourceOverride = null;
                } else {
                    // Do not re-create Explore fragment if it already exists. The
                    // existing fragment may have a pending onActivityResult call for
                    // GPX import, for example.
                    return;
                }
            }
            hideExploreBadgeHighlight();
        } else {
            throw new IllegalStateException(
                "Unknown menu item: " + id);
        }

        // Leaving diary?
        if (fm.findFragmentByTag(Diary.FRAGMENT_TAG) != null && !tag.equals(
            Diary.FRAGMENT_TAG)) {
            markAllWorkoutsAsSeen();
        }

        goToHomeTabOnBackPressedCallback.setEnabled(id != com.stt.android.R.id.bottomBarDashboard);

        transaction.replace(R.id.mainContent, fragment, tag).commitAllowingStateLoss();
    }

    protected void handleNavigationItemReSelected(int id) {
        String tag;
        if (id == com.stt.android.R.id.bottomBarDashboard) {
            tag = DashboardFragment.FRAGMENT_TAG;
        } else if (id == com.stt.android.R.id.bottomBarDiary) {
            tag = Diary.FRAGMENT_TAG;
        } else if (id == com.stt.android.R.id.bottomBarCalendar) {
            tag = NewDiaryCalendarContainerFragment.FRAGMENT_TAG;
        } else if (id == com.stt.android.R.id.bottomBarExplore) {
            tag = exploreNavigator.getTag();
        } else {
            throw new IllegalStateException("Unknown menu item: " + id);
        }
        Fragment fragment = getSupportFragmentManager().findFragmentByTag(tag);
        if (fragment instanceof HomeTab) {
            ((HomeTab) fragment).moveTo(0);
        }
    }

    private void initBottomBarReselectListener() {
        //Set the reselect-listener after all other initialization logic has been done,
        //otherwise this might prevent the real selection listener from being called
        //in certain cases during the activity creation.

        bottomBar.setOnNavigationItemReselectedListener(
                menuItem -> viewModel.setNavigationItemReselected(menuItem.getItemId()));
    }

    protected abstract Fragment getDiaryFragment(
        @Nullable Diary.TabType diaryTabType,
        @Nullable GraphDataType primaryGraphType,
        @Nullable GraphDataType secondaryGraphType,
        @Nullable String source
    );

    protected abstract void hideExploreBadgeHighlight();

    @Override
    protected void onStart() {
        super.onStart();
        if (currentUserController.isLoggedIn()) {
            viewModel.pollAccountStatusIfNeeded();
        } else {
            if (currentUserController.getSession() != null) {
                Timber.d("BaseHomeActivity.onStart: current user is not logged in: %s", currentUserController.getSession().getSessionKey() == null);
            }
            // If there's no user logged in then redirect to ProxyActivity to force login/signup
            // We no longer allow anonymous use
            startActivity(ProxyActivity.newStartIntentClearStack(this));
            finish();
            overridePendingTransition(0, 0);
        }

        localBM.registerReceiver(syncFinishedReceiver,
            new IntentFilter(STTConstants.BroadcastActions.SYNC_FINISHED));

        viewModel.refreshAppData(false);
        clearNotifications();

        subscribeUnseenWorkoutsCount();
    }

    public void openRouteList() {
        FragmentManager fm = getSupportFragmentManager();
        FragmentTransaction transaction = fm.beginTransaction();
        Fragment fragment = exploreNavigator.newInstanceShowRoutes();
        transaction.replace(R.id.mainContent, fragment, exploreNavigator.getTag()).commitAllowingStateLoss();
    }

    private void unsubscribeLoadPendingFollowRequestSubscription() {
        if (loadUnseenPendingFollowRequestsSubscription != null) {
            loadUnseenPendingFollowRequestsSubscription.unsubscribe();
            loadUnseenPendingFollowRequestsSubscription = null;
        }
    }

    private void subscribeUnseenWorkoutsCount() {
        unsubscribeUnseenWorkoutsCount();
        unseenWorkoutsCountSubscription = workoutHeaderController
            .getCurrentUserWorkoutUpdatesAsObservable()
            .debounce(100, TimeUnit.MILLISECONDS)
            .startWith((WorkoutHeaderController.WorkoutUpdate) null)
            .map(workoutUpdate -> workoutHeaderController.loadUnseenWorkoutsCount
                (currentUserController.getUsername()))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(count -> {
                if (count > 0L) {
                    if (bottomBar.getCurrentTabPosition() != TAB_DIARY) {
                        setDiaryBadgeText(count.toString());
                    }
                } else {
                    setDiaryBadgeText(null);
                }
            }, throwable -> setDiaryBadgeText(null));
    }

    private void unsubscribeUnseenWorkoutsCount() {
        if (unseenWorkoutsCountSubscription != null) {
            unseenWorkoutsCountSubscription.unsubscribe();
            unseenWorkoutsCountSubscription = null;
        }
    }

    private void clearNotifications() {
        NotificationManager nm = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        for (int i = 0; i < STTConstants.NotificationIds.CANCEL_ON_START_NOTIFICATION_IDS.length;
            ++i) {
            nm.cancel(STTConstants.NotificationIds.CANCEL_ON_START_NOTIFICATION_IDS[i]);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // "Route list" is opened, if route is created from "Map Explore" view
        if (requestCode == STTConstants.RequestCodes.MAPEXPLORE_ROUTE_SAVE
            && resultCode == RESULT_OK) {
            openRouteList();
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean(HomeConstantsKt.KEY_NEW_ROUTE, showNewRoute);
        outState.putInt(KEY_SELECTED_TAB, bottomBar.getCurrentTabPosition());
    }

    @Override
    protected void onStop() {
        unsubscribeLoadPendingFollowRequestSubscription();
        unsubscribeUnseenWorkoutsCount();

        localBM.unregisterReceiver(syncFinishedReceiver);

        super.onStop();
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED.equals(key)) {
            reportGpsTrackingInterruptionIfNeeded();
        }
    }

    @SuppressLint("ApplySharedPref")
    private synchronized void reportGpsTrackingInterruptionIfNeeded() {
        if (sharedPreferences.getBoolean(PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED, false)) {
            sharedPreferences.edit()
                .putBoolean(PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED, false)
                .commit();
            SimpleDialogFragment dialog = SimpleDialogFragment.newInstance(
                getString(com.stt.android.R.string.report_gps_tracking_issue_dialog_message),
                getString(com.stt.android.R.string.report_gps_tracking_issue_dialog_title), getString(com.stt.android.R.string.settings),
                getString(com.stt.android.R.string.cancel), false);
            dialog.setCancelable(false);
            dialog.show(getSupportFragmentManager(), GPS_TRACKING_INTERRUPTED_DIALOG);
        }
    }

    @Override
    public void onDialogButtonPressed(@Nullable String tag, int which) {
        if (GPS_TRACKING_INTERRUPTED_DIALOG.equals(tag)
            && which == DialogInterface.BUTTON_POSITIVE) {
            startActivity(
                PowerManagementSettingsActivity.newStartIntentForGpsTrackingInterruptedPopUp(this));
        } else if (PHONE_NUMBER_VERIFICATION_DEBUG_DIALOG.equals(tag)
            && which == DialogInterface.BUTTON_POSITIVE) {
            startPhoneNumberVerification();
        }
    }

    @Override
    public void onDialogDismissed(@Nullable String tag) {
    }

    @Override
    protected void onDestroy() {
        disposable.clear();
        super.onDestroy();
    }

    private void goToHomeTab() {
        bottomBar.selectTabAtPosition(TAB_HOME);
    }

    private void setDiaryBadgeText(String text) {
        bottomBar.setTabBadgeText(com.stt.android.R.id.bottomBarDiary, text);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
        @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    private void markAllWorkoutsAsSeen() {
        Observable.fromCallable(() -> workoutHeaderController.findAllWhereOwner(
            currentUserController.getUsername(), false))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(workouts -> {
                for (WorkoutHeader workout : workouts) {
                    if (!workout.getSeen()) {
                        workout = workout.toBuilder().seen(true).build();
                        SaveWorkoutHeaderService.enqueueWork(BaseHomeActivity.this,
                            workout, false);
                    }
                }
            }, e -> Timber.e(e, "Failed to mark workouts as seen"));
    }

    @Override
    public void onShowTerms() {
        startActivity(new Intent(this, TermsActivity.class));
    }

    private void checkInAppReviewTrigger() {
        if (inAppReviewTrigger.canShowInAppReviewDialog()) {
            inAppReviewTrigger.showInAppReviewDialog(getSupportFragmentManager());
        }
    }

    @Override
    public void refreshAppData(boolean force) {
        viewModel.refreshAppData(force);
    }

    @Override
    public void navigateToWorkoutDetails(int workoutId, boolean showComments) {
        new WorkoutDetailsNavEvent(workoutId, showComments).navigate(navController);
    }

    @Override
    public void navigateToWorkoutMapGraphAnalysis(@NonNull WorkoutHeader workoutHeader) {
        new WorkoutMapGraphAnalysisNavEvent(
            workoutHeader,
            AnalyticsPropertyValue.WorkoutAnalysisScreenSource.PLAY_BUTTON_FEED
        ).navigate(navController);
    }

    @Override
    public void navigateToFullscreenGraphAnalysis(@NonNull WorkoutHeader workoutHeader) {
        new WorkoutFullscreenGraphAnalysisNavEvent(
            workoutHeader,
            AnalyticsPropertyValue.WorkoutAnalysisScreenSource.PLAY_BUTTON_FEED
        ).navigate(navController);
    }
}
