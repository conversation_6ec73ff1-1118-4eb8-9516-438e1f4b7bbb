<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/activity_horizontal_margin"
        android:orientation="vertical">

        <!-- Device Type -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/device_type"/>

            <TextView
                android:id="@+id/device_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Device Serial -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/device_serial"/>

            <TextView
                android:id="@+id/device_serial"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Device SW Version -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/device_sw_version"/>

            <TextView
                android:id="@+id/device_sw_version"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Device State -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/device_state"/>

            <TextView
                android:id="@+id/device_state"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"
                android:freezesText="true"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/device_latest_sync"/>

            <TextView
                android:id="@+id/latest_sync"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"
                android:freezesText="true"/>
        </LinearLayout>


        <!-- GNSS version -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_gnss"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/btn_get_gnss"/>

            <TextView
                android:id="@+id/gnss_version"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to disconnect connection -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_disconnect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_disconnect"
                android:layout_weight="3"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Send notification -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_send"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/btn_send_notification"/>

            <EditText
                android:id="@+id/notification"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="6"
                android:hint="@string/notification_hint"/>
        </LinearLayout>


        <!-- Button to access Ancs -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_notifications"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/btn_notifications"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to access Logbook -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_logbook"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/btn_logbook"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to access Settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_settings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="@string/btn_settings"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to sync watch -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_sync"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_sync"
                android:layout_weight="3"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to reset connection -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_reset_connection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_reset_connection"
                android:layout_weight="3"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

        <!-- Button to get service stability -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp">

            <Button
                android:id="@+id/btn_get_service_stability"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_get_service_stability"
                android:layout_weight="3"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="6"/>
        </LinearLayout>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
