package com.suunto.scsampleapp.di.buscode

import com.suunto.connectivity.buscode.AlipayBusCodeApi
import com.suunto.connectivity.buscode.BusCodeOperation
import dagger.Binds
import dagger.Module
import dagger.hilt.android.scopes.ServiceScoped
import javax.inject.Inject

@Module
abstract class AlipayBusCodeModule {
    @ServiceScoped
    @Binds
    abstract fun bindBusCode(impl: AlipayBusCodeApiImpl): AlipayBusCodeApi
}

class AlipayBusCodeApiImpl @Inject constructor(): AlipayBusCodeApi {
    override suspend fun subscribeOperations(serial: String) {
    }

    override suspend fun performOperation(serial: String, operation: BusCodeOperation) {
    }
}
