package com.suunto.scsampleapp.di.weather;

import com.squareup.moshi.Moshi;
import com.stt.android.data.weather.WeatherConditionsRepository;
import com.stt.android.data.weather.WeatherConditionsRepositoryV2;
import com.stt.android.domain.weather.WeatherConditionsDataSource;
import com.stt.android.domain.weatherV2.WeatherConditionsDataSourceV2;
import com.stt.android.remote.OpenWeatherMapApi;
import com.stt.android.remote.OpenWeatherMapAppId;
import com.stt.android.remote.OpenWeatherMapAppKey;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.SuuntoWeatherBackendApi;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.weather.OpenWeatherMapRestApi;
import com.stt.android.remote.weatherV2.OpenWeatherMapRestApiV2;
import com.suunto.scsampleapp.BuildConfig;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;

@Module
public abstract class WeatherConditionsModule {

    @Provides
    @OpenWeatherMapAppId
    static String provideOpenWeatherMapAppId() {
        return "********************************";
    }

    @Provides
    @OpenWeatherMapAppKey
    static String provideOpenWeatherMapAppKey() {
        return "87e9myXiSqHW3cgVqvrSR3IFoc6bqV7x";
    }

    @Provides
    @SuuntoWeatherBackendApi
    public static String provideOpenWeatherBaseUrl() {
        return "https://weather-test.sports-tracker.com/";
    }

    @Binds
    abstract WeatherConditionsDataSource bindWeatherConditionsDataSource(
        WeatherConditionsRepository weatherConditionsRepository);

    @Provides
    @UserAgent
    static String provideUserAgent() {
        return BuildConfig.APPLICATION_ID + "/" + BuildConfig.VERSION_CODE;
    }

    @Binds
    abstract WeatherConditionsDataSourceV2 bindWeatherConditionsDataSourceV2(
        WeatherConditionsRepositoryV2 weatherConditionsRepository);

    @Provides
    @OpenWeatherMapApi
    static OpenWeatherMapRestApi provideOpenWeatherMapRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @UserAgent String userAgent,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            "https://api.openweathermap.org/data/2.5/",
            OpenWeatherMapRestApi.class,
            BrandOkHttpConfigFactory.getNoAuthOkHttpConfig(userAgent),
            moshi);
    }

    @Provides
    @SuuntoWeatherBackendApi
    static OpenWeatherMapRestApiV2 provideBackendRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @SuuntoWeatherBackendApi String weatherUrl,
        @OpenWeatherMapAppKey String appKey,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            weatherUrl,
            OpenWeatherMapRestApiV2.class,
            BrandOkHttpConfigFactory.getWeatherAppKeyOkHttpConfig(appKey),
            moshi);
    }
}
