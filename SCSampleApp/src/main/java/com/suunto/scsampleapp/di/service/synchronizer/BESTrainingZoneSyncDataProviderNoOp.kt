package com.suunto.scsampleapp.di.service.synchronizer

import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncData
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncDataProvider
import java.time.DayOfWeek
import javax.inject.Inject

class BESTrainingZoneSyncDataProviderNoOp @Inject constructor() : BESTrainingZoneSyncDataProvider {

    override suspend fun getAnalysis(firstDayOfWeek: DayOfWeek): BESTrainingZoneSyncData {
        TODO("Not yet implemented")
    }
}
