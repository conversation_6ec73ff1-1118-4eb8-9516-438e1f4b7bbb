package com.suunto.scsampleapp;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothManager;
import static android.content.ContentValues.TAG;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice;
import com.suunto.connectivity.btscanner.SuuntoLeScanner;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;

/**
 *
 */
public class ScanFragment extends Fragment {

    interface DeviceSelectionListener {
        void onDeviceSelected(SuuntoBtDevice suuntoBtDevice);
    }

    private static final int MY_PERMISSIONS_REQUEST_LOCATION = 99;

    private BluetoothManager bluetoothManager;
    private DeviceSelectionListener listener;
    private SuuntoBtDeviceAdapter suuntoBtDeviceAdapter;
    private SuuntoLeScanner suuntoLeScanner;
    private Subscription clickSubscription;
    private Subscription scanSubscription;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bluetoothManager = (BluetoothManager) requireContext().getSystemService(Context.BLUETOOTH_SERVICE);

        Activity activity = getActivity();
        if (activity instanceof DeviceSelectionListener) {
            listener = (DeviceSelectionListener) activity;
        } else {
            throw new IllegalArgumentException("Containing Activity does not implement DeviceSelectionListener!");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.fragment_scan, container, false);

        // Cancel scan button
        Button cancelScan = (Button) view.findViewById(R.id.btn_cancel);
        cancelScan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                stopScanning();
                getActivity().onBackPressed();
            }
        });

        // List of scanned devices
        RecyclerView deviceList = (RecyclerView) view.findViewById(R.id.device_list);
        deviceList.setLayoutManager(new LinearLayoutManager(getContext()));
        suuntoBtDeviceAdapter = new SuuntoBtDeviceAdapter();
        deviceList.setAdapter(suuntoBtDeviceAdapter);
        deviceList.setItemAnimator(null);

        // Instantiate scanned
        suuntoLeScanner = SuuntoLeScanner.withContext(requireContext(), bluetoothManager);

        clickSubscription = suuntoBtDeviceAdapter.getDeviceClicks()
                .subscribeOn(AndroidSchedulers.mainThread())
                .subscribe(new Action1<SuuntoBtDevice>() {
                    @Override
                    public void call(SuuntoBtDevice suuntoBtDevice) {
                        onDeviceSelected(suuntoBtDevice);
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        Log.e(TAG, "Error when receiving clicks: " + throwable.getMessage());
                    }
                });

        // Start scanning immediately
        startScanning();

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        if (clickSubscription != null) {
            clickSubscription.unsubscribe();
            clickSubscription = null;
        }

        if (scanSubscription != null) {
            scanSubscription.unsubscribe();
            scanSubscription = null;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String permissions[],
                                           @NonNull int[] grantResults) {
        if (requestCode == MY_PERMISSIONS_REQUEST_LOCATION) {
            // If request is cancelled, the result arrays are empty.
            if (grantResults.length > 0
                    && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                // permission was granted, yay! Do the
                // location-related task you need to do.
                if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION)
                        == PackageManager.PERMISSION_GRANTED) {

                    // Try starting scan again
                    startScanning();
                }

            }
        }
    }

    private void startScanning() {
        if (scanSubscription != null) {
            Log.d(TAG, "Already scanning");
            return;
        }

        if (!checkLocationPermission()) {
            return;
        }

        scanSubscription = suuntoLeScanner.scanBleDevices(false)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Action1<ScannedSuuntoBtDevice>() {
                    @Override
                    public void call(ScannedSuuntoBtDevice device) {
                        suuntoBtDeviceAdapter.addDevice(device);
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        if (getContext() != null) {
                            Toast.makeText(getContext().getApplicationContext(), throwable.getMessage(), Toast.LENGTH_LONG).show();
                            Log.e(TAG, "Scan error: " + throwable.getMessage());
                            stopScanning();
                        }
                    }
                });
    }

    private void stopScanning() {
        Log.d(TAG, "Stopping scanning");
        if (scanSubscription != null) {
            scanSubscription.unsubscribe();
            scanSubscription = null;
        }

        suuntoBtDeviceAdapter.clear();
    }

    private void onDeviceSelected(SuuntoBtDevice device) {
        stopScanning();
        listener.onDeviceSelected(device);
    }

    private boolean checkLocationPermission() {
        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {

            // Should we show an explanation?
            if (shouldShowRequestPermissionRationale(Manifest.permission.ACCESS_COARSE_LOCATION)) {
                new AlertDialog.Builder(getActivity())
                        .setTitle(R.string.title_location_permission)
                        .setMessage(R.string.text_location_permission)
                        .setPositiveButton(R.string.btn_ok, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                //Prompt the user once explanation has been shown
                                requestPermissions(new String[]{Manifest.permission.ACCESS_COARSE_LOCATION},
                                        MY_PERMISSIONS_REQUEST_LOCATION);
                            }
                        })
                        .create()
                        .show();

            } else {
                // No explanation needed, we can request the permission.
                requestPermissions(new String[]{Manifest.permission.ACCESS_COARSE_LOCATION},
                        MY_PERMISSIONS_REQUEST_LOCATION);
            }
            return false;
        } else {
            return true;
        }
    }
}
