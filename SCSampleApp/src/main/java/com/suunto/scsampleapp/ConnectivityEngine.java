package com.suunto.scsampleapp;

import android.content.Context;
import android.util.Log;
import com.suunto.connectivity.Connectivity;
import com.suunto.connectivity.ScLib;
import com.suunto.connectivity.Spartan;
import com.suunto.connectivity.notifications.NotificationsSettings;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.util.IOUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;
import rx.Single;
import rx.functions.Action0;
import rx.functions.Action1;

class ConnectivityEngine {
    private static final String TAG = ConnectivityEngine.class.getSimpleName();
    private static ConnectivityEngine INSTANCE;

    private final ScLib scLib;
    private final File logsDir;
    private boolean gatheringLogs;
    Spartan connectedSpartan;

    // Simple interface to communicate failing connect back to caller
    interface ConnectFailCallback {
        void onConnectFailed();
    }

    private ConnectivityEngine(Context context) {
        scLib = Connectivity.scLibBuilder(context)
                .build();

        logsDir = context.getCacheDir();
        gatheringLogs = false;
    }

    Spartan connect(SuuntoBtDevice device, final ConnectFailCallback callback) {
        connectedSpartan = scLib.toSpartan(device);

        connectedSpartan.connect()
                .subscribe(new Action1<Boolean>() {
                    @Override
                    public void call(Boolean connected) {
                        if (connected) {
                            Log.d(TAG, "Connected!");
                        } else {
                            Log.e(TAG, "Connecting watch failed!");
                            callback.onConnectFailed();
                        }
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        Log.e(TAG, "Connecting watch failed: " + throwable.toString());
                        callback.onConnectFailed();
                    }
                });

        return connectedSpartan;
    }

    NotificationsSettings getNotificationsSettings() {
        return scLib.getNotificationSettings();
    }

    List<SuuntoBtDevice> getAvailableBondedDevices() {
        return scLib.getAvailableBondedDevices();
    }

    static void initialize(Context context) {
        if (INSTANCE != null) {
            throw new IllegalStateException("ConnectivityEngine already initialized");
        }

        INSTANCE = new ConnectivityEngine(context);
    }

    static ConnectivityEngine getInstance() {
        if (INSTANCE == null) {
            throw new IllegalAccessError("ConnectivityEngine not initialized");
        }

        return INSTANCE;
    }

    public Single<SuuntoBtDevice> getDevice(String macAddress) {
        return scLib.getDevice(macAddress);
    }

    void gatherLogs() {
        if (gatheringLogs) {
            gatheringLogs = false;
            Log.d(TAG, "Stopping logs gathering");
            scLib.stopLoggingToFile()
                    .subscribe(new Action1<File>() {
                        @Override
                        public void call(File file) {
                            Log.d(TAG, "Gathered log to: " + file.getPath());
                            printFile(file);
                        }
                    }, new Action1<Throwable>() {
                        @Override
                        public void call(Throwable throwable) {
                            Log.e(TAG, "Gathering logs failed", throwable);
                        }
                    });
        } else {
            gatheringLogs = true;
            Log.d(TAG, "Starting logs gathering");
            scLib.startLoggingToFile(logsDir)
                    .subscribe(new Action0() {
                        @Override
                        public void call() {
                            Log.d(TAG, "Gathering started");
                        }
                    }, new Action1<Throwable>() {
                        @Override
                        public void call(Throwable throwable) {
                            Log.e(TAG, "Starting to gather failed", throwable);
                        }
                    });
        }
    }

    private void printFile(File file) {
        Log.d(TAG, "-------- PRINTING FILE: " + file.getPath() + " ---------");

        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));

            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(reader);
        }

        Log.d(TAG, "-------- DONE FILE: " + file.getPath() + " ---------");
    }
}
