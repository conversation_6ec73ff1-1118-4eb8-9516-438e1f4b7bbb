package com.stt.android.suunto.china.debug.douyinapi

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bytedance.sdk.open.aweme.CommonConstants
import com.bytedance.sdk.open.aweme.common.handler.IApiEventHandler
import com.bytedance.sdk.open.aweme.common.model.BaseReq
import com.bytedance.sdk.open.aweme.common.model.BaseResp
import com.bytedance.sdk.open.aweme.share.Share
import com.bytedance.sdk.open.douyin.DouYinOpenApiFactory
import com.bytedance.sdk.open.douyin.api.DouYinOpenApi
import com.stt.android.sharingplatform.DouYinAPI
import com.stt.android.sharingplatform.SharingResultState
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class DouYinEntryActivity : AppCompatActivity(), IApiEventHandler {

    private lateinit var douYinOpenApi: DouYinOpenApi

    @Inject
    lateinit var douYinApi: DouYinAPI

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        douYinOpenApi = DouYinOpenApiFactory.create(this);
        douYinOpenApi.handleIntent(intent, this);
    }

    override fun onReq(p0: BaseReq?) {
        // do nothing
    }

    override fun onResp(p0: BaseResp?) {
        if (p0?.type == CommonConstants.ModeType.SHARE_CONTENT_TO_TT_RESP) {
            val response = p0 as Share.Response
            when (response.errorCode) {
                0 -> {
                    douYinApi.onShareResult(SharingResultState.Success)
                }

                -2 -> {
                    douYinApi.onShareResult(SharingResultState.Cancel)
                }

                else -> {
                    douYinApi.onShareResult(SharingResultState.Fail)
                    Timber.w("douyin sharing failed: errorCode: ${response.errorCode}, subCode: ${response.subErrorCode}, ${response.errorMsg}")
                }
            }
        }
        finish()
    }

    override fun onErrorIntent(p0: Intent?) {
        Timber.w("douyin sharing intent error")
    }
}
