plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
}

android {
    namespace 'com.stt.android.diveplanner'
    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.divePlannerDomain)
    implementation project(Deps.infoModel)

    implementation libs.diving.algorithm
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }
    implementation libs.androidx.gridlayout
}
