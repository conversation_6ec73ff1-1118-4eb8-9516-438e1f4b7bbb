package com.stt.android.diveplanner.ui.diveprofile.graph

import android.graphics.Paint
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Chip
import androidx.compose.material.ChipDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.primaryColorDarker
import com.stt.android.compose.theme.spacing
import com.stt.android.diveplanner.R
import com.stt.android.diveplanner.ui.common.ValueWithUnit
import com.stt.android.diveplanner.ui.common.drawHeaderBorders
import com.stt.android.diveplanner.ui.diveprofile.gases.common.DecompressionDiveProfileUiStateProvider
import kotlinx.collections.immutable.ImmutableList

data class Point(val x: Float, val y: Float)

private val graphPaddingTop = 12.dp
private val graphPaddingBottom = 28.dp
private val graphPaddingStart = 8.dp
private val graphPaddingEnd = 20.dp
private val yAxisFontSize = 12.sp
private val eventRowHeaderHeight = 40.dp
private val eventRowItemHeight = 45.dp
private val eventRowItemWidth = 80.dp
private val arrowIconSize = 28.dp

@Composable
fun DecompressionDiveGraphWithScenariosRow(
    decompressionDiveProfileUiState: DecompressionDiveProfileUiState,
    onPlusFiveMinClicked: () -> Unit,
    onPlusFiveMetersClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(color = Color.White)
    ) {
        DecompressionDiveGraph(
            minDuration = decompressionDiveProfileUiState.minDuration,
            maxDuration = decompressionDiveProfileUiState.maxDuration,
            minDepth = decompressionDiveProfileUiState.minDepth,
            maxDepth = decompressionDiveProfileUiState.maxDepth,
            decompressions = decompressionDiveProfileUiState.decompressions,
            lineAlpha = 1.0f,
            modifier = Modifier.weight(1f)
        )
        ScenariosRow(
            isPlusFiveMinSelected = decompressionDiveProfileUiState.isPlusFiveMinSelected,
            onPlusFiveMinClicked = onPlusFiveMinClicked,
            isPlusFiveMetersSelected = decompressionDiveProfileUiState.isPlusFiveMetersSelected,
            onPlusFiveMetersClicked = onPlusFiveMetersClicked
        )
    }
}

@Composable
fun DecompressionDiveGraph(
    minDuration: Int,
    maxDuration: Int,
    minDepth: Int,
    maxDepth: Int,
    decompressions: ImmutableList<Decompression>,
    modifier: Modifier = Modifier,
    lineAlpha: Float = 1.0f
) {
    val density = LocalDensity.current
    val textPaint = remember(density) {
        Paint().apply {
            color = android.graphics.Color.rgb(126, 128, 132) // DarkGrey
            textAlign = Paint.Align.CENTER
            textSize = density.run { yAxisFontSize.toPx() }
        }
    }
    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    start = graphPaddingStart,
                    end = graphPaddingEnd,
                    top = graphPaddingTop,
                    bottom = graphPaddingBottom
                )
        ) {
            val minXValue: Float = minDuration.toFloat()
            val maxXValue: Float = maxDuration.toFloat()
            val minYValue: Float = minDepth.toFloat()
            val maxYValue: Float = maxDepth.toFloat()
            val points = extractPoints(decompressions)

            drawGraph(
                points,
                minXValue = minXValue,
                maxXValue = maxXValue,
                minYValue = minYValue,
                maxYValue = maxYValue,
                lineAlpha = lineAlpha
            )

            drawXAxis(
                minXValue = minXValue.toInt(),
                maxXValue = maxXValue.toInt(),
                textPaint = textPaint
            )
            drawYAxis(
                textPaint = textPaint,
                minYValue = minYValue.toInt(),
                maxYValue = maxYValue.toInt()
            )
        }
    }
}

@Composable
fun ScenariosRow(
    isPlusFiveMinSelected: Boolean,
    onPlusFiveMinClicked: () -> Unit,
    isPlusFiveMetersSelected: Boolean,
    onPlusFiveMetersClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = graphPaddingStart
            )
    ) {
        ScenarioChip(
            text = stringResource(R.string.master),
            iconRes = R.drawable.dive_levels_master,
            isSelected = true, // master is always selected
            onClick = {},
            backgroundColorAlpha = if (isPlusFiveMetersSelected || isPlusFiveMinSelected) 0.5f else 1.0f
        )
        Spacer(modifier = Modifier.width(12.dp))
        ScenarioChip(
            text = stringResource(R.string.plus_five_minutes),
            iconRes = R.drawable.dive_contingency_circled_fill,
            isSelected = isPlusFiveMinSelected,
            onClick = onPlusFiveMinClicked
        )
        Spacer(modifier = Modifier.width(12.dp))
        ScenarioChip(
            text = stringResource(R.string.plus_five_meters),
            iconRes = R.drawable.dive_contingency_circled_fill,
            isSelected = isPlusFiveMetersSelected,
            onClick = onPlusFiveMetersClicked
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun ScenarioChip(
    text: String,
    @DrawableRes iconRes: Int,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColorAlpha: Float = 1.0f
) {
    Chip(
        leadingIcon = { Icon(painter = painterResource(iconRes), contentDescription = null) },
        onClick = onClick,
        colors = ChipDefaults.chipColors(
            backgroundColor = if (isSelected) {
                MaterialTheme.colors.primaryColorDarker.copy(alpha = backgroundColorAlpha)
            } else {
                MaterialTheme.colors.lightGrey
            },
            contentColor = Color.Black,
            leadingIconContentColor = if (isSelected) {
                Color.White
            } else {
                Color.Black
            }
        ),
        modifier = modifier
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.body,
            color = if (isSelected) {
                Color.White
            } else {
                MaterialTheme.colors.nearBlack
            }
        )
    }
}

@Composable
fun EventsList(
    decompressions: ImmutableList<Decompression>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .horizontalScroll(rememberScrollState())
    ) {
        Column(
            modifier = Modifier
                .wrapContentWidth()
        ) {
            EventRowHeader()
            decompressions.forEach {
                EventRowItem(
                    decompressionDepth = it.decompressionDepth,
                    runtime = ValueWithUnit(value = "${it.runtime}", unit = "min"),
                    time = ValueWithUnit(value = "${it.time}", unit = "m")
                )
            }
        }
    }
}

@Composable
private fun EventRow(
    minHightIn: Dp,
    modifier: Modifier = Modifier,
    content: @Composable RowScope.() -> Unit
) {
    Row(
        modifier = modifier
            .wrapContentWidth()
            .heightIn(min = minHightIn)
            .drawHeaderBorders(
                drawTopLine = false,
                drawBottomLine = true
            )
            .padding(horizontal = MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        content()
    }
}

@Composable
private fun EventRowHeader(
    modifier: Modifier = Modifier
) {
    EventRow(
        minHightIn = eventRowHeaderHeight,
        modifier = modifier
    ) {
        HeaderTitle(textRes = R.string.depth)
        HeaderTitle(textRes = R.string.runtime)
        HeaderTitle(textRes = R.string.time)
    }
}

@Composable
private fun HeaderTitle(
    @StringRes textRes: Int,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .widthIn(min = eventRowItemWidth)
    ) {
        Text(
            text = stringResource(textRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.darkGrey
        )
    }
}

@Composable
private fun EventRowItem(
    decompressionDepth: DecompressionDepth,
    runtime: ValueWithUnit,
    time: ValueWithUnit,
    modifier: Modifier = Modifier
) {
    EventRow(
        minHightIn = eventRowItemHeight,
        modifier = modifier
    ) {
        when (decompressionDepth) {
            DecompressionDepth.Down -> EventRowItemIcon(isUp = false)
            DecompressionDepth.Up -> EventRowItemIcon(isUp = true)
            is DecompressionDepth.Value -> EventRowItemValueAndUnit(
                value = decompressionDepth.depth.toString(),
                unit = "m"
            )
        }
        EventRowItemValueAndUnit(value = runtime.value, unit = runtime.unit)
        EventRowItemValueAndUnit(value = time.value, unit = time.unit)
    }
}

@Composable
fun EventRowItemValueAndUnit(
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .widthIn(min = eventRowItemWidth)
    ) {
        Text(
            text = buildAnnotatedString {
                withStyle(
                    style = SpanStyle(
                        color = MaterialTheme.colors.nearBlack,
                        fontSize = MaterialTheme.typography.bodyLargeBold.fontSize,
                        fontWeight = MaterialTheme.typography.bodyLargeBold.fontWeight,
                        fontFamily = MaterialTheme.typography.bodyLargeBold.fontFamily
                    )
                ) { append(value) }
                append(" ")
                withStyle(
                    style = SpanStyle(
                        color = MaterialTheme.colors.nearBlack,
                        fontSize = MaterialTheme.typography.bodySmall.fontSize,
                        fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                        fontFamily = MaterialTheme.typography.bodySmall.fontFamily
                    )
                ) { append(unit) }
            }
        )
    }
}

@Composable
fun EventRowItemIcon(
    isUp: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .widthIn(min = eventRowItemWidth)
    ) {
        if (isUp) {
            Icon(
                painter = painterResource(R.drawable.rising_arrow_outline),
                contentDescription = null,
                modifier = Modifier.size(arrowIconSize)
            )
        } else {
            Icon(
                painter = painterResource(R.drawable.falling_arrow_outline),
                contentDescription = null,
                modifier = Modifier.size(arrowIconSize)
            )
        }
    }
}

private fun DrawScope.drawXAxis(
    minXValue: Int,
    maxXValue: Int,
    textPaint: Paint
) {
    val tmpXValues = (0..maxXValue step 5)
        .map { it }

    for (value in tmpXValues) {
        val x = value.toFloat().mapValueToDifferentRange(
            inMin = minXValue.toFloat(),
            inMax = maxXValue.toFloat(),
            outMin = 0f,
            outMax = size.width
        )

        drawContext.canvas.nativeCanvas.drawText(
            "'",
            x,
            size.height + 40,
            textPaint
        )

        drawContext.canvas.nativeCanvas.drawText(
            "$value",
            x,
            size.height + graphPaddingBottom.toPx(),
            textPaint
        )
    }
}

private fun DrawScope.drawYAxis(
    minYValue: Int,
    maxYValue: Int,
    textPaint: Paint
) {
    val tmpYValues = (0..maxYValue step 10)
        .map { it }
    for (value in tmpYValues) {
        val y = value.toFloat().mapValueToDifferentRange(
            inMin = minYValue.toFloat(),
            inMax = maxYValue.toFloat(),
            outMin = 0f,
            outMax = size.height
        )
        drawContext.canvas.nativeCanvas.drawText(
            "$value",
            size.width + graphPaddingEnd.toPx() / 2,
            y - ((textPaint.descent() + textPaint.ascent()) / 2),
            textPaint
        )
        drawLine(
            color = Color(0xFF303030).copy(alpha = 0.2f), // NearBlack
            start = Offset(0f, y),
            end = Offset(size.width, y)
        )
    }
}

private fun extractPoints(
    decompressions: List<Decompression>,
): List<Point> {
    val points = mutableListOf<Point>()

    // Link to the first item
    points.add(Point(0f, 0f))
    val listIterator = decompressions.listIterator()
    listIterator.next() // skip first item
    while (listIterator.hasNext()) {
        val item = listIterator.next()
        val x = item.runtime.toFloat()
        val y = when (item.decompressionDepth) {
            DecompressionDepth.Down -> points.last().y
            DecompressionDepth.Up -> points.last().y
            is DecompressionDepth.Value -> item.decompressionDepth.depth.toFloat()
        }
        points.add(Point(x, y))
    }

    // Link from the last item
    points.add(
        Point(
            (decompressions.last().runtime + decompressions.last().time).toFloat(),
            0f
        )
    )

    return points
}

private fun DrawScope.drawGraph(
    points: List<Point>,
    minXValue: Float,
    maxXValue: Float,
    minYValue: Float,
    maxYValue: Float,
    lineAlpha: Float = 1.0f
) {
    val path = Path()
    points.forEachIndexed { index, point ->
        if (index == 0) {
            path.moveTo(point.x, point.y)
        } else {
            path.lineTo(
                point.x.mapValueToDifferentRange(
                    inMin = minXValue,
                    inMax = maxXValue,
                    outMin = 0f,
                    outMax = size.width
                ),
                point.y.mapValueToDifferentRange(
                    inMin = minYValue,
                    inMax = maxYValue,
                    outMin = 0f,
                    outMax = size.height
                )
            )
        }
    }

    drawPath(
        path,
        color = Color(0xFF1190BB).copy(alpha = lineAlpha), // PrimaryColorDarker
        style = Stroke(
            width = 2.dp.toPx(),
            pathEffect = PathEffect.cornerPathEffect(2.dp.toPx())
        )
    )
}

// simple extension function that allows conversion between ranges
fun Float.mapValueToDifferentRange(
    inMin: Float,
    inMax: Float,
    outMin: Float,
    outMax: Float
) = (this - inMin) * (outMax - outMin) / (inMax - inMin) + outMin

@Preview(showBackground = true)
@Composable
private fun DecompressionDiveProfileScreenPreview(
    @PreviewParameter(DecompressionDiveProfileUiStateProvider::class) decompressionDiveProfileUiState: DecompressionDiveProfileUiState
) {
    AppTheme {
        Column {
            DecompressionDiveGraphWithScenariosRow(
                decompressionDiveProfileUiState = decompressionDiveProfileUiState,
                onPlusFiveMinClicked = {},
                onPlusFiveMetersClicked = {},
                modifier = Modifier.weight(1f)
            )
            Box(Modifier.weight(1f))
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun EventsListPreview(
    @PreviewParameter(DecompressionDiveProfileUiStateProvider::class) decompressionDiveProfileUiState: DecompressionDiveProfileUiState
) {
    AppTheme {
        EventsList(decompressions = decompressionDiveProfileUiState.decompressions)
    }
}

@Preview(showBackground = true)
@Composable
private fun ScenarioChipPreview() {
    AppTheme {
        ScenariosRow(
            isPlusFiveMinSelected = true,
            onPlusFiveMinClicked = { },
            isPlusFiveMetersSelected = false,
            onPlusFiveMetersClicked = { }
        )
    }
}
