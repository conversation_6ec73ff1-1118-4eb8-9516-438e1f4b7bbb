package com.stt.android.diveplanner.ui.utils

import com.stt.android.diveplanner.ui.common.Configuration
import com.stt.android.diveplanner.ui.diveprofile.gases.CylinderUiState
import com.stt.android.diveplanner.ui.diveprofile.gases.GasUiState
import com.stt.android.diveplanner.ui.diveprofile.gases.MixtureUiState
import com.stt.android.diveplanner.ui.diveprofile.gases.PreferencesUiState
import java.util.UUID
import kotlin.random.Random

object DataFaker {
    fun generateGasUiState(
        configuration: Configuration = Configuration.OpenCircuit,
        useAsDecompressionGasEnabled: Boolean = false,
        useAsBailoutGasEnabled: Boolean = false
    ): GasUiState {
        return GasUiState(
            mixtureUiState = MixtureUiState(
                o2 = Random.nextInt(1, 99),
                he = Random.nextInt(1, 99)
            ),
            cylinderUiState = CylinderUiState(
                size = Random.nextInt(1, 100),
                pressure = Random.nextInt(1, 233)
            ),
            preferencesUiState = PreferencesUiState(
                configuration = configuration,
                useAsDecompressionGasEnabled = useAsDecompressionGasEnabled,
                partialPressureLimit = Random.nextDouble(0.1, 99.0).toFloat(),
                useAsBailoutGasEnabled = useAsBailoutGasEnabled,
                endLimit = Random.nextInt(1, 99)
            ),
            isSelected = false,
            isEnabled = false,
            id = UUID.randomUUID().toString()
        )
    }
}
